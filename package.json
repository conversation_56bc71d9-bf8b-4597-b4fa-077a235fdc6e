{"name": "flutter-data-router-database-tests", "version": "1.0.0", "description": "Database integration test scripts for Flutter Data Router ObjectBox implementation", "scripts": {"test:db": "flutter test test/database_integration_test_suite.dart --reporter=expanded", "test:db:comprehensive": "flutter test test/database_integration_test_suite.dart --reporter=expanded", "test:db:individual": "npm run test:user && npm run test:collection && npm run test:sticker && npm run test:user-private-data && npm run test:visited-profile && npm run test:metadata && npm run test:call && npm run test:channel && npm run test:member && npm run test:message && npm run test:attachment && npm run test:translated-result", "test:db:all": "flutter test test/entities/ --reporter=expanded", "test:user": "flutter test test/entities/user/user_objectbox_integration_test.dart --reporter=compact", "test:collection": "flutter test test/entities/collection/collection_objectbox_integration_test.dart --reporter=compact", "test:sticker": "flutter test test/entities/sticker/sticker_objectbox_integration_test.dart --reporter=compact", "test:user-private-data": "flutter test test/entities/user_private_data/user_private_data_objectbox_integration_test.dart --reporter=compact", "test:visited-profile": "flutter test test/entities/visited_profile/visited_profile_objectbox_integration_test.dart --reporter=compact", "test:metadata": "flutter test test/entities/metadata/metadata_entities_objectbox_integration_test.dart --reporter=compact", "test:call": "flutter test test/entities/call/call_entities_objectbox_integration_test.dart --reporter=compact", "test:channel": "flutter test test/entities/channel/channel_entities_objectbox_integration_test.dart --reporter=compact", "test:member": "flutter test test/entities/member/member_objectbox_integration_test.dart --reporter=compact", "test:message": "flutter test test/entities/message/message_objectbox_integration_test.dart --reporter=compact", "test:attachment": "flutter test test/entities/attachment/attachment_objectbox_integration_test.dart --reporter=compact", "test:translated-result": "flutter test test/entities/translated_result/translated_result_objectbox_integration_test.dart --reporter=compact", "test:user-entities": "npm run test:user && npm run test:user-private-data && npm run test:visited-profile", "test:content-entities": "npm run test:collection && npm run test:sticker", "test:messaging-entities": "npm run test:message && npm run test:attachment && npm run test:translated-result", "test:communication-entities": "npm run test:call && npm run test:channel && npm run test:member", "setup:test": "flutter pub get && dart run build_runner build", "clean:test": "flutter clean && flutter pub get", "test:coverage": "flutter test --coverage test/database_integration_test_suite.dart", "test:performance": "flutter test test/database_integration_test_suite.dart --reporter=json > test_results.json"}, "keywords": ["flutter", "dart", "objectbox", "database", "testing", "integration", "relationships"], "author": "Flutter Data Router Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/flutter-data-router.git"}, "bugs": {"url": "https://github.com/your-org/flutter-data-router/issues"}, "homepage": "https://github.com/your-org/flutter-data-router#readme"}