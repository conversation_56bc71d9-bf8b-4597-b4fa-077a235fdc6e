# Database Integration Test Makefile
# Provides convenient commands for running ObjectBox database tests

.PHONY: help test-db test-db-comprehensive test-db-individual test-db-all test-entity clean-test setup-test

# Default target
help:
	@echo "🗄️ Database Integration Test Commands"
	@echo "======================================"
	@echo ""
	@echo "Available commands:"
	@echo "  make test-db              - Run comprehensive database test suite (default)"
	@echo "  make test-db-comprehensive - Run comprehensive database test suite"
	@echo "  make test-db-individual   - Run individual entity tests with summary"
	@echo "  make test-db-all          - Run all database-related tests"
	@echo "  make test-entity ENTITY=<name> - Run specific entity tests"
	@echo "  make setup-test           - Setup test environment"
	@echo "  make clean-test           - Clean test artifacts"
	@echo ""
	@echo "Examples:"
	@echo "  make test-db"
	@echo "  make test-entity ENTITY=user"
	@echo "  make test-entity ENTITY=message"
	@echo ""
	@echo "📊 Test Coverage:"
	@echo "  - 23 Entities with ObjectBox relationships"
	@echo "  - 70+ Integration tests"
	@echo "  - 50+ ToOne/ToMany relationships"

# Default database test
test-db: test-db-comprehensive

# Run comprehensive database test suite
test-db-comprehensive:
	@echo "🗄️ Running Comprehensive Database Test Suite..."
	@echo "=============================================="
	flutter test test/database_integration_test_suite.dart --reporter=expanded

# Run individual entity tests
test-db-individual:
	@echo "🗄️ Running Individual Entity Tests..."
	@echo "====================================="
	@echo "📋 Testing User Management Entities..."
	@flutter test test/entities/user/user_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/user_private_data/user_private_data_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/visited_profile/visited_profile_objectbox_integration_test.dart --reporter=compact
	@echo ""
	@echo "📋 Testing Content & Media Entities..."
	@flutter test test/entities/collection/collection_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/sticker/sticker_objectbox_integration_test.dart --reporter=compact
	@echo ""
	@echo "📋 Testing Metadata & Management Entities..."
	@flutter test test/entities/metadata/metadata_entities_objectbox_integration_test.dart --reporter=compact
	@echo ""
	@echo "📋 Testing Communication Entities..."
	@flutter test test/entities/call/call_entities_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/channel/channel_entities_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/member/member_objectbox_integration_test.dart --reporter=compact
	@echo ""
	@echo "📋 Testing Messaging Entities..."
	@flutter test test/entities/message/message_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/attachment/attachment_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/translated_result/translated_result_objectbox_integration_test.dart --reporter=compact
	@echo ""
	@echo "✅ Individual entity tests completed!"

# Run all database tests
test-db-all:
	@echo "🗄️ Running All Database Tests..."
	@echo "==============================="
	flutter test test/entities/ --reporter=expanded

# Run specific entity test
test-entity:
ifndef ENTITY
	@echo "❌ Error: Please specify ENTITY name"
	@echo "Usage: make test-entity ENTITY=<entity_name>"
	@echo "Examples:"
	@echo "  make test-entity ENTITY=user"
	@echo "  make test-entity ENTITY=message"
	@echo "  make test-entity ENTITY=attachment"
	@exit 1
endif
	@echo "🗄️ Running $(ENTITY) Entity Tests..."
	@echo "=================================="
	flutter test test/entities/$(ENTITY)/$(ENTITY)_objectbox_integration_test.dart --reporter=expanded

# Setup test environment
setup-test:
	@echo "🔧 Setting up test environment..."
	@echo "================================"
	flutter pub get
	dart run build_runner build
	@echo "✅ Test environment ready!"

# Clean test artifacts
clean-test:
	@echo "🧹 Cleaning test artifacts..."
	@echo "============================"
	flutter clean
	flutter pub get
	@echo "✅ Test artifacts cleaned!"

# Quick test commands for specific entity groups
test-user-entities:
	@echo "👤 Testing User Management Entities..."
	@flutter test test/entities/user/user_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/user_private_data/user_private_data_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/visited_profile/visited_profile_objectbox_integration_test.dart --reporter=compact

test-content-entities:
	@echo "🎨 Testing Content & Media Entities..."
	@flutter test test/entities/collection/collection_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/sticker/sticker_objectbox_integration_test.dart --reporter=compact

test-messaging-entities:
	@echo "💬 Testing Messaging Entities..."
	@flutter test test/entities/message/message_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/attachment/attachment_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/translated_result/translated_result_objectbox_integration_test.dart --reporter=compact

test-communication-entities:
	@echo "📞 Testing Communication Entities..."
	@flutter test test/entities/call/call_entities_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/channel/channel_entities_objectbox_integration_test.dart --reporter=compact
	@flutter test test/entities/member/member_objectbox_integration_test.dart --reporter=compact

# Performance test
test-db-performance:
	@echo "⚡ Running Database Performance Tests..."
	@echo "======================================"
	flutter test test/performance/database_performance_test.dart --reporter=compact
	@echo "📊 Performance test completed!"

# Legacy performance test (for compatibility)
test-db-performance-legacy:
	@echo "⚡ Running Legacy Database Performance Tests..."
	@echo "=============================================="
	flutter test test/database_integration_test_suite.dart --reporter=json > test_results.json
	@echo "📊 Performance results saved to test_results.json"

# Test with coverage
test-db-coverage:
	@echo "📊 Running Database Tests with Coverage..."
	@echo "========================================"
	flutter test --coverage test/database_integration_test_suite.dart
	genhtml coverage/lcov.info -o coverage/html
	@echo "📊 Coverage report generated in coverage/html/"
