import 'package:data_router/src/core/data_router.dart';
import 'package:data_router/src/core/di/di.dart';
import 'package:data_router/src/data/adapters/user_adapter.dart';
import 'package:data_router/src/data/database/entities/user.dart';

/// Test DataRouter functionality
Future<void> testDataRouter() async {
  print('🚀 Starting DataRouter test...\n');

  try {
    // 1. Initialize DI
    print('⚙️ Initializing dependency injection...');
    await configureInjection();
    print('✅ DI initialized successfully');

    // 2. Create DataRouter instance
    final dataRouter = DataRouter();
    print('✅ Created DataRouter instance');

    // 3. Register User adapter
    dataRouter.registerAdapter<User>(UserAdapter());
    print('✅ Registered User adapter');

    // 4. Initialize DataRouter
    await dataRouter.init();
    print('✅ DataRouter initialized successfully');

    // 5. Show stats
    final stats = dataRouter.getStats();
    print('📊 DataRouter Stats:');
    stats.forEach((key, value) {
      print('   $key: $value');
    });

    // 6. Get user operation
    final userOp = await dataRouter.get<User>();
    print('✅ Got user operation');

    // 7. Test basic operations
    print('\n👤 Testing User operations...');
    
    // Test get user (should return null from stub)
    print('📡 Testing get user...');
    final userStream = userOp.get('test_user_123');
    
    userStream.take(1).listen(
      (resource) {
        print('📡 Get user result: ${resource.state}');
        if (resource.hasData) {
          print('👤 User: ${resource.data?.username ?? 'null'}');
        } else if (resource.hasError) {
          print('❌ Get user error: ${resource.error}');
        } else {
          print('📭 No user data (expected from stub)');
        }
      },
      onError: (error) {
        print('💥 Get user stream error: $error');
      },
    );

    // Wait for async operations
    await Future.delayed(const Duration(seconds: 1));

    // 8. Test create user
    print('\n➕ Testing create user...');
    try {
      final newUser = User.create(
        userId: 'test_user_new',
        username: 'Test User',
        sessionKey: 'test_session_key',
      );

      final created = await userOp.create(newUser);
      print('✅ Created user: ${created.username}');
    } catch (e) {
      print('⚠️ Create user failed (expected from stub): $e');
    }

    // 9. Cleanup
    await dataRouter.dispose();
    print('\n✅ DataRouter disposed successfully');

    print('\n🎉 DataRouter test completed successfully!');
    print('📝 Note: Using stub implementations - actual data operations will work when integrated with real data sources');

  } catch (e, stackTrace) {
    print('❌ DataRouter test failed: $e');
    print('Stack trace: $stackTrace');
    rethrow;
  }
}

/// Main function to run the test
Future<void> main() async {
  await testDataRouter();
}
