# 🔄 **MIGRATION IMPLEMENTATION PLAN**

## 📋 **OVERVIEW**

<PERSON><PERSON> hoạch chi tiết để migrate từ **Service-Oriented API Client** sang **Data-Centric DataRouter** theo kiến trúc trong `plan/final_implement.md`.

## 🎯 **PHASE 0: PREPARATION (Week 1)**

### **Day 1-2: Directory Structure Setup**

#### **New Directory Structure:**
```
lib/src/
├── core/
│   ├── data_router.dart                 # Main DataRouter singleton
│   ├── interfaces/
│   │   ├── model_adapter.dart           # ModelAdapter<T> interface
│   │   ├── data_source.dart             # DataSource interfaces
│   │   └── data_operation.dart          # DataOperation<T> interface
│   └── operations/
│       └── data_operation_impl.dart     # DataOperation implementation
├── data/
│   ├── sources/
│   │   ├── local_generic_data_source.dart    # SQLite generic operations
│   │   ├── remote_generic_data_source.dart   # HTTP generic operations
│   │   └── websocket_generic_data_source.dart # WebSocket generic operations
│   ├── adapters/
│   │   ├── user_adapter.dart            # User entity adapter
│   │   ├── message_adapter.dart         # Message entity adapter
│   │   └── channel_adapter.dart         # Channel entity adapter
│   └── models/
│       ├── resource.dart                # Resource<T> state wrapper
│       └── data_state.dart              # DataState enum
└── legacy/                              # Current API implementation
    └── api/                             # Move current API code here
```

#### **Tasks:**
- [ ] Create new directory structure
- [ ] Move current API code to `legacy/api/`
- [ ] Update imports to use legacy path
- [ ] Add feature flags for gradual migration

### **Day 3-4: Core Interfaces**

#### **Create ModelAdapter Interface:**
```dart
// lib/src/core/interfaces/model_adapter.dart
abstract class ModelAdapter<T> {
  /// Table name for local storage
  String get tableName;
  
  /// API endpoint for remote operations
  String get endpoint;
  
  /// WebSocket channel for real-time updates
  String get socketChannel;
  
  /// Convert from Map to Model
  T fromMap(Map<String, dynamic> map);
  
  /// Convert from Model to Map
  Map<String, dynamic> toMap(T model);
  
  /// Get primary key from model
  String getId(T model);
  
  /// Validation rules (optional)
  bool validate(T model) => true;
}
```

#### **Create DataSource Interfaces:**
```dart
// lib/src/core/interfaces/data_source.dart
abstract class LocalDataSource {
  Future<T?> get<T>(String id, ModelAdapter<T> adapter);
  Future<List<T>> getAll<T>(ModelAdapter<T> adapter);
  Future<void> save<T>(T item, ModelAdapter<T> adapter);
  Future<void> saveAll<T>(List<T> items, ModelAdapter<T> adapter);
  Future<void> delete<T>(String id, ModelAdapter<T> adapter);
  Stream<T?> watch<T>(String id, ModelAdapter<T> adapter);
  Stream<List<T>> watchAll<T>(ModelAdapter<T> adapter);
}

abstract class RemoteDataSource {
  Future<T?> fetch<T>(String id, ModelAdapter<T> adapter);
  Future<List<T>> fetchAll<T>(ModelAdapter<T> adapter);
  Future<T> create<T>(T item, ModelAdapter<T> adapter);
  Future<T> update<T>(T item, ModelAdapter<T> adapter);
  Future<void> delete<T>(String id, ModelAdapter<T> adapter);
}

abstract class WebSocketDataSource {
  Stream<T> subscribe<T>(String id, ModelAdapter<T> adapter);
  Stream<List<T>> subscribeAll<T>(ModelAdapter<T> adapter);
  void unsubscribe<T>(String id, ModelAdapter<T> adapter);
}
```

### **Day 5: Resource & State Models**

#### **Create Resource Wrapper:**
```dart
// lib/src/data/models/resource.dart
enum DataState {
  loading,
  success,
  error,
  empty,
}

class Resource<T> {
  final DataState state;
  final T? data;
  final String? error;
  final bool isFromCache;
  final DateTime timestamp;

  const Resource({
    required this.state,
    this.data,
    this.error,
    this.isFromCache = false,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory Resource.loading([T? data]) => Resource(
    state: DataState.loading,
    data: data,
    isFromCache: data != null,
  );

  factory Resource.success(T data, {bool isFromCache = false}) => Resource(
    state: DataState.success,
    data: data,
    isFromCache: isFromCache,
  );

  factory Resource.error(String error, [T? data]) => Resource(
    state: DataState.error,
    error: error,
    data: data,
    isFromCache: data != null,
  );

  factory Resource.empty() => const Resource(state: DataState.empty);

  bool get isLoading => state == DataState.loading;
  bool get isSuccess => state == DataState.success;
  bool get isError => state == DataState.error;
  bool get isEmpty => state == DataState.empty;
  bool get hasData => data != null;
}
```

## 🎯 **PHASE 1: CORE IMPLEMENTATION (Week 2-3)**

### **Week 2: DataRouter & DataOperation**

#### **Day 1-2: DataRouter Singleton**
```dart
// lib/src/core/data_router.dart
class DataRouter {
  static final DataRouter _instance = DataRouter._internal();
  factory DataRouter() => _instance;
  DataRouter._internal();

  final Map<Type, ModelAdapter> _adapters = {};
  late final LocalDataSource _localSource;
  late final RemoteDataSource _remoteSource;
  late final WebSocketDataSource _webSocketSource;
  
  bool _initialized = false;

  /// Register adapter for entity type
  void registerAdapter<T>(ModelAdapter<T> adapter) {
    _adapters[T] = adapter;
  }

  /// Initialize data sources
  Future<void> init() async {
    if (_initialized) return;
    
    _localSource = LocalGenericDataSource();
    _remoteSource = RemoteGenericDataSource();
    _webSocketSource = WebSocketGenericDataSource();
    
    await _localSource.init();
    await _remoteSource.init();
    await _webSocketSource.init();
    
    _initialized = true;
  }

  /// Get data operation for entity type
  Future<DataOperation<T>> get<T>() async {
    if (!_initialized) throw StateError('DataRouter not initialized');
    
    final adapter = _adapters[T] as ModelAdapter<T>?;
    if (adapter == null) {
      throw StateError('No adapter registered for type $T');
    }

    return DataOperationImpl<T>(
      adapter: adapter,
      localSource: _localSource,
      remoteSource: _remoteSource,
      webSocketSource: _webSocketSource,
    );
  }
}
```

#### **Day 3-4: DataOperation Implementation**
```dart
// lib/src/core/operations/data_operation_impl.dart
class DataOperationImpl<T> implements DataOperation<T> {
  final ModelAdapter<T> adapter;
  final LocalDataSource localSource;
  final RemoteDataSource remoteSource;
  final WebSocketDataSource webSocketSource;

  DataOperationImpl({
    required this.adapter,
    required this.localSource,
    required this.remoteSource,
    required this.webSocketSource,
  });

  @override
  Stream<Resource<T?>> get(String id) async* {
    // 1. Emit loading with cached data if available
    final cached = await localSource.get(id, adapter);
    if (cached != null) {
      yield Resource.success(cached, isFromCache: true);
    } else {
      yield Resource.loading();
    }

    try {
      // 2. Fetch from remote
      final remote = await remoteSource.fetch(id, adapter);
      if (remote != null) {
        // 3. Save to local
        await localSource.save(remote, adapter);
        
        // 4. Emit fresh data
        yield Resource.success(remote, isFromCache: false);
      } else if (cached == null) {
        yield Resource.empty();
      }
    } catch (e) {
      yield Resource.error(e.toString(), cached);
    }
  }

  @override
  Stream<Resource<List<T>>> getAll() async* {
    // Similar implementation for list operations
    // ...
  }

  @override
  Stream<T> subscribeUpdate(String id) {
    return webSocketSource.subscribe(id, adapter);
  }
}
```

### **Week 3: Generic DataSources**

#### **Day 1-2: LocalGenericDataSource**
```dart
// lib/src/data/sources/local_generic_data_source.dart
class LocalGenericDataSource implements LocalDataSource {
  late final Database _database;

  Future<void> init() async {
    // Initialize SQLite database
    _database = await openDatabase(
      'app_database.db',
      version: 1,
      onCreate: _createTables,
    );
  }

  @override
  Future<T?> get<T>(String id, ModelAdapter<T> adapter) async {
    final maps = await _database.query(
      adapter.tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (maps.isEmpty) return null;
    return adapter.fromMap(maps.first);
  }

  @override
  Future<List<T>> getAll<T>(ModelAdapter<T> adapter) async {
    final maps = await _database.query(adapter.tableName);
    return maps.map((map) => adapter.fromMap(map)).toList();
  }

  @override
  Future<void> save<T>(T item, ModelAdapter<T> adapter) async {
    final map = adapter.toMap(item);
    await _database.insert(
      adapter.tableName,
      map,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Implement other methods...
}
```

#### **Day 3-4: RemoteGenericDataSource**
```dart
// lib/src/data/sources/remote_generic_data_source.dart
class RemoteGenericDataSource implements RemoteDataSource {
  late final UnifiedApiClient _apiClient; // Reuse existing API client

  Future<void> init() async {
    _apiClient = getIt<UnifiedApiClient>();
    await _apiClient.initialize();
  }

  @override
  Future<T?> fetch<T>(String id, ModelAdapter<T> adapter) async {
    try {
      final response = await _apiClient.chatApi.dio.get(
        '${adapter.endpoint}/$id',
      );
      
      if (response.statusCode == 200 && response.data != null) {
        return adapter.fromMap(response.data);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to fetch ${T.toString()}: $e');
    }
  }

  // Implement other methods...
}
```

## 🎯 **PHASE 2: FIRST ENTITY MIGRATION (Week 4)**

### **Day 1-2: User Entity Setup**

#### **Create User Model:**
```dart
// lib/src/data/models/user.dart
class User {
  final String id;
  final String name;
  final String email;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromMap(Map<String, dynamic> map) => User(
    id: map['id'] as String,
    name: map['name'] as String,
    email: map['email'] as String,
    avatarUrl: map['avatar_url'] as String?,
    createdAt: DateTime.parse(map['created_at'] as String),
    updatedAt: DateTime.parse(map['updated_at'] as String),
  );

  Map<String, dynamic> toMap() => {
    'id': id,
    'name': name,
    'email': email,
    'avatar_url': avatarUrl,
    'created_at': createdAt.toIso8601String(),
    'updated_at': updatedAt.toIso8601String(),
  };
}
```

#### **Create User Adapter:**
```dart
// lib/src/data/adapters/user_adapter.dart
class UserAdapter implements ModelAdapter<User> {
  @override
  String get tableName => 'users';

  @override
  String get endpoint => '/users';

  @override
  String get socketChannel => 'user_updates';

  @override
  User fromMap(Map<String, dynamic> map) => User.fromMap(map);

  @override
  Map<String, dynamic> toMap(User model) => model.toMap();

  @override
  String getId(User model) => model.id;

  @override
  bool validate(User model) {
    return model.id.isNotEmpty && 
           model.name.isNotEmpty && 
           model.email.contains('@');
  }
}
```

### **Day 3-4: Integration & Testing**

#### **Setup DataRouter:**
```dart
// lib/src/core/setup.dart
Future<void> setupDataRouter() async {
  final dataRouter = DataRouter();
  
  // Register adapters
  dataRouter.registerAdapter<User>(UserAdapter());
  
  // Initialize
  await dataRouter.init();
}
```

#### **Usage Example:**
```dart
// In UI
final dataRouter = DataRouter();
final userOp = await dataRouter.get<User>();

// Get single user with local-first strategy
userOp.get('user123').listen((resource) {
  switch (resource.state) {
    case DataState.loading:
      showLoading();
      break;
    case DataState.success:
      showUser(resource.data!);
      break;
    case DataState.error:
      showError(resource.error!);
      break;
    case DataState.empty:
      showEmpty();
      break;
  }
});

// Subscribe to real-time updates
userOp.subscribeUpdate('user123').listen((user) {
  updateUserInUI(user);
});
```

## 📊 **SUCCESS METRICS**

### **Technical Metrics:**
- [ ] DataRouter initialization < 100ms
- [ ] Local data access < 10ms
- [ ] Remote data fetch < 2s
- [ ] Memory usage < current implementation
- [ ] 100% test coverage for core components

### **UX Metrics:**
- [ ] Offline data availability
- [ ] Real-time updates working
- [ ] Smooth loading states
- [ ] Error handling graceful
- [ ] No data loss during sync

### **Developer Experience:**
- [ ] Simple API: `DataRouter().get<T>()`
- [ ] Easy entity addition
- [ ] Clear error messages
- [ ] Good documentation
- [ ] Migration guide available

---

**Next Steps**: Bắt đầu với Phase 0 để setup foundation, sau đó implement từng phase một cách tuần tự để đảm bảo stability và quality.
