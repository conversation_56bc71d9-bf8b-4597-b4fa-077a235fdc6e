import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:objectbox/objectbox.dart';
import 'package:data_router/src/data/database/entities/entities.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';

/// Test cases for SessionLocalMetadata entity relationships
/// Following TDD methodology and test requirements from @plan/test_case_requiment/test_case_req.md
void main() {
  late Store store;
  late Box<Session> sessionBox;
  late Box<SessionLocalMetadata> sessionLocalMetadataBox;

  setUp(() async {
    // Create in-memory ObjectBox store for testing
    store = Store(getObjectBoxModel(), directory: Directory.systemTemp.createTempSync().path);
    sessionBox = store.box<Session>();
    sessionLocalMetadataBox = store.box<SessionLocalMetadata>();
  });

  tearDown(() {
    store.close();
  });

  group('SessionLocalMetadata Relationships', () {
    group('✅ 1. Relationship Mapping', () {
      test('should correctly map SessionLocalMetadata to Session via ToOne relationship', () {
        // Arrange
        final session = Session.create(
          sessionKey: 'test_session_001',
          sessionId: 'server_session_001',
          sessionToken: 'token_001',
        );
        sessionBox.put(session);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'test_session_001',
          introChannelView: true,
          speechLang: 'en',
        );

        // Act - Set relationship
        metadata.session.target = session;
        sessionLocalMetadataBox.put(metadata);

        // Assert
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);
        expect(retrievedMetadata, isNotNull);
        expect(retrievedMetadata!.session.target, isNotNull);
        expect(retrievedMetadata.session.target!.sessionKey, equals('test_session_001'));
        expect(retrievedMetadata.session.target!.sessionId, equals('server_session_001'));
      });

      test('should handle null Session relationship gracefully', () {
        // Arrange
        final metadata = SessionLocalMetadata.create(
          sessionKey: 'orphan_session',
          introChannelView: false,
        );

        // Act - No relationship set
        sessionLocalMetadataBox.put(metadata);

        // Assert
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);
        expect(retrievedMetadata, isNotNull);
        expect(retrievedMetadata!.session.target, isNull);
        expect(retrievedMetadata.sessionKey, equals('orphan_session'));
      });
    });

    group('✅ 2. Persistence', () {
      test('should persist ToOne relationship when putting SessionLocalMetadata', () {
        // Arrange
        final session = Session.create(
          sessionKey: 'persist_session_001',
          sessionId: 'persist_server_001',
          sessionToken: 'persist_token_001',
        );
        sessionBox.put(session);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'persist_session_001',
          introTranslateEntireChat: true,
          speechToTextEnable: true,
        );

        // Act - Set relationship and persist
        metadata.session.target = session;
        final metadataId = sessionLocalMetadataBox.put(metadata);

        // Assert - Verify persistence
        expect(metadataId, greaterThan(0));
        final retrievedMetadata = sessionLocalMetadataBox.get(metadataId);
        expect(retrievedMetadata!.session.target, isNotNull);
        expect(retrievedMetadata.session.target!.id, equals(session.id));
        expect(retrievedMetadata.session.targetId, equals(session.id));
      });

      test('should persist empty relationship correctly', () {
        // Arrange
        final metadata = SessionLocalMetadata.create(
          sessionKey: 'empty_relation_session',
          introCallLog: true,
        );

        // Act - Put without setting relationship
        final metadataId = sessionLocalMetadataBox.put(metadata);

        // Assert
        expect(metadataId, greaterThan(0));
        final retrievedMetadata = sessionLocalMetadataBox.get(metadataId);
        expect(retrievedMetadata!.session.target, isNull);
        expect(retrievedMetadata.session.targetId, equals(0));
      });

      test('should update relationship when putting again', () {
        // Arrange
        final session1 = Session.create(
          sessionKey: 'update_session_001',
          sessionId: 'update_server_001',
          sessionToken: 'update_token_001',
        );
        final session2 = Session.create(
          sessionKey: 'update_session_002',
          sessionId: 'update_server_002',
          sessionToken: 'update_token_002',
        );
        sessionBox.putMany([session1, session2]);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'update_session_001',
          introCreateChannel: true,
        );
        metadata.session.target = session1;
        sessionLocalMetadataBox.put(metadata);

        // Act - Update relationship
        metadata.session.target = session2;
        metadata.sessionKey = 'update_session_002';
        sessionLocalMetadataBox.put(metadata);

        // Assert
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);
        expect(retrievedMetadata!.session.target!.sessionKey, equals('update_session_002'));
        expect(retrievedMetadata.sessionKey, equals('update_session_002'));
      });
    });

    group('✅ 3. Fetch & Access', () {
      test('should access relationship target correctly', () {
        // Arrange
        final session = Session.create(
          sessionKey: 'fetch_session_001',
          sessionId: 'fetch_server_001',
          sessionToken: 'fetch_token_001',
        );
        sessionBox.put(session);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'fetch_session_001',
          speechLang: 'vi',
          resumeId: 'resume_001',
        );
        metadata.session.target = session;
        sessionLocalMetadataBox.put(metadata);

        // Act - Fetch and access
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);

        // Assert - Test different access methods
        expect(retrievedMetadata!.session.target, isNotNull);
        expect(retrievedMetadata.session.target!.sessionKey, equals('fetch_session_001'));
        expect(retrievedMetadata.session.targetId, equals(session.id));
        expect(retrievedMetadata.session.hasValue, isTrue);
      });

      test('should handle lazy loading correctly', () {
        // Arrange
        final session = Session.create(
          sessionKey: 'lazy_session_001',
          sessionId: 'lazy_server_001',
          sessionToken: 'lazy_token_001',
        );
        sessionBox.put(session);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'lazy_session_001',
          userUpdateTimeAfter: DateTime.now(),
        );
        metadata.session.target = session;
        sessionLocalMetadataBox.put(metadata);

        // Act - Access should trigger lazy load (ObjectBox handles this automatically)
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);
        final sessionTarget = retrievedMetadata!.session.target;

        // Assert
        expect(sessionTarget, isNotNull);
        expect(sessionTarget!.sessionKey, equals('lazy_session_001'));
      });
    });

    group('✅ 4. Update Relationship', () {
      test('should update relationship from one Session to another', () {
        // Arrange
        final sessionA = Session.create(
          sessionKey: 'session_A',
          sessionId: 'server_A',
          sessionToken: 'token_A',
        );
        final sessionB = Session.create(
          sessionKey: 'session_B',
          sessionId: 'server_B',
          sessionToken: 'token_B',
        );
        sessionBox.putMany([sessionA, sessionB]);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'session_A',
          introChannelView: true,
        );
        metadata.session.target = sessionA;
        sessionLocalMetadataBox.put(metadata);

        // Act - Change relationship from A to B
        metadata.session.target = sessionB;
        metadata.sessionKey = 'session_B';
        sessionLocalMetadataBox.put(metadata);

        // Assert
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);
        expect(retrievedMetadata!.session.target!.sessionKey, equals('session_B'));
        expect(retrievedMetadata.session.target!.sessionId, equals('server_B'));
        expect(retrievedMetadata.sessionKey, equals('session_B'));
      });

      test('should update relationship to null', () {
        // Arrange
        final session = Session.create(
          sessionKey: 'null_update_session',
          sessionId: 'null_update_server',
          sessionToken: 'null_update_token',
        );
        sessionBox.put(session);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'null_update_session',
          speechToTextEnable: true,
        );
        metadata.session.target = session;
        sessionLocalMetadataBox.put(metadata);

        // Act - Set relationship to null
        metadata.session.target = null;
        sessionLocalMetadataBox.put(metadata);

        // Assert
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);
        expect(retrievedMetadata!.session.target, isNull);
        expect(retrievedMetadata.session.targetId, equals(0));
      });
    });

    group('✅ 5. Deletion Cascade', () {
      test('should handle Session deletion correctly', () {
        // Arrange
        final session = Session.create(
          sessionKey: 'delete_session_001',
          sessionId: 'delete_server_001',
          sessionToken: 'delete_token_001',
        );
        sessionBox.put(session);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'delete_session_001',
          introChannelList: true,
        );
        metadata.session.target = session;
        sessionLocalMetadataBox.put(metadata);

        // Act - Delete the Session
        sessionBox.remove(session.id);

        // Assert - SessionLocalMetadata should still exist but relationship should be null
        final retrievedMetadata = sessionLocalMetadataBox.get(metadata.id);
        expect(retrievedMetadata, isNotNull);
        expect(retrievedMetadata!.session.target, isNull);
        expect(retrievedMetadata.sessionKey, equals('delete_session_001')); // String field remains
      });
    });

    group('✅ 6. Query By Relation', () {
      test('should query SessionLocalMetadata by Session targetId', () {
        // Arrange
        final session1 = Session.create(
          sessionKey: 'query_session_001',
          sessionId: 'query_server_001',
          sessionToken: 'query_token_001',
        );
        final session2 = Session.create(
          sessionKey: 'query_session_002',
          sessionId: 'query_server_002',
          sessionToken: 'query_token_002',
        );
        sessionBox.putMany([session1, session2]);

        final metadata1 = SessionLocalMetadata.create(
          sessionKey: 'query_session_001',
          introCallLog: true,
        );
        metadata1.session.target = session1;

        final metadata2 = SessionLocalMetadata.create(
          sessionKey: 'query_session_002',
          introCallLog: false,
        );
        metadata2.session.target = session2;

        final metadata3 = SessionLocalMetadata.create(
          sessionKey: 'orphan_session',
          introCallLog: true,
        );
        // metadata3 has no session relationship

        sessionLocalMetadataBox.putMany([metadata1, metadata2, metadata3]);

        // Act - Query by session relationship
        final query = sessionLocalMetadataBox.query(
          SessionLocalMetadata_.session.equals(session1.id)
        ).build();
        final results = query.find();
        query.close();

        // Assert
        expect(results.length, equals(1));
        expect(results.first.sessionKey, equals('query_session_001'));
        expect(results.first.session.target!.sessionKey, equals('query_session_001'));
      });
    });

    group('🔧 Helper Methods', () {
      test('should provide convenient sessionKey getter/setter through relationship', () {
        // Arrange
        final session = Session.create(
          sessionKey: 'helper_session_001',
          sessionId: 'helper_server_001',
          sessionToken: 'helper_token_001',
        );
        sessionBox.put(session);

        final metadata = SessionLocalMetadata.create(
          sessionKey: 'helper_session_001',
          speechLang: 'ja',
        );

        // Act - Set relationship
        metadata.session.target = session;
        sessionLocalMetadataBox.put(metadata);

        // Assert - Test helper methods
        expect(metadata.effectiveSessionKey, equals('helper_session_001'));
        expect(metadata.hasSessionRelationship, isTrue);
      });
    });

    group('🔄 Comprehensive Integration Test', () {
      test('should handle complete SessionLocalMetadata ↔ Session relationship lifecycle', () {
        // This test validates all implemented relationships working together
        // in a comprehensive real-world scenario

        // ARRANGE - Create multiple sessions and metadata
        final session1 = Session.create(
          sessionKey: 'integration_session_001',
          sessionId: 'integration_server_001',
          sessionToken: 'integration_token_001',
        );
        session1.active = true;
        session1.isLogin = true;

        final session2 = Session.create(
          sessionKey: 'integration_session_002',
          sessionId: 'integration_server_002',
          sessionToken: 'integration_token_002',
        );
        session2.active = false;
        session2.isLogin = false;

        final session3 = Session.create(
          sessionKey: 'integration_session_003',
          sessionId: 'integration_server_003',
          sessionToken: 'integration_token_003',
        );

        sessionBox.putMany([session1, session2, session3]);

        // Create multiple metadata entries with different relationship states
        final metadata1 = SessionLocalMetadata.create(
          sessionKey: 'integration_session_001',
          introChannelView: true,
          introTranslateEntireChat: false,
          speechLang: 'en',
          speechToTextEnable: true,
        );

        final metadata2 = SessionLocalMetadata.create(
          sessionKey: 'integration_session_002',
          introChannelView: false,
          introCallLog: true,
          speechLang: 'vi',
          resumeId: 'resume_002',
        );

        final metadata3 = SessionLocalMetadata.create(
          sessionKey: 'orphan_session',
          introCreateChannel: true,
          speechLang: 'ja',
        );

        // ACT 1 - Establish relationships
        metadata1.session.target = session1;
        metadata2.session.target = session2;
        // metadata3 intentionally has no relationship (orphan)

        final metadataIds = sessionLocalMetadataBox.putMany([metadata1, metadata2, metadata3]);

        // ASSERT 1 - Verify initial relationship establishment
        expect(metadataIds.length, equals(3));
        expect(metadataIds.every((id) => id > 0), isTrue);

        // Verify metadata1 ↔ session1 relationship
        final retrievedMetadata1 = sessionLocalMetadataBox.get(metadata1.id)!;
        expect(retrievedMetadata1.session.target, isNotNull);
        expect(retrievedMetadata1.session.target!.sessionKey, equals('integration_session_001'));
        expect(retrievedMetadata1.session.target!.active, isTrue);
        expect(retrievedMetadata1.effectiveSessionKey, equals('integration_session_001'));
        expect(retrievedMetadata1.hasSessionRelationship, isTrue);
        expect(retrievedMetadata1.relatedSessionId, equals('integration_server_001'));
        expect(retrievedMetadata1.relatedSessionToken, equals('integration_token_001'));

        // Verify metadata2 ↔ session2 relationship
        final retrievedMetadata2 = sessionLocalMetadataBox.get(metadata2.id)!;
        expect(retrievedMetadata2.session.target, isNotNull);
        expect(retrievedMetadata2.session.target!.sessionKey, equals('integration_session_002'));
        expect(retrievedMetadata2.session.target!.active, isFalse);
        expect(retrievedMetadata2.effectiveSessionKey, equals('integration_session_002'));

        // Verify orphan metadata3
        final retrievedMetadata3 = sessionLocalMetadataBox.get(metadata3.id)!;
        expect(retrievedMetadata3.session.target, isNull);
        expect(retrievedMetadata3.hasSessionRelationship, isFalse);
        expect(retrievedMetadata3.effectiveSessionKey, equals('orphan_session'));
        expect(retrievedMetadata3.relatedSessionId, equals(''));
        expect(retrievedMetadata3.relatedSessionToken, equals(''));

        // ACT 2 - Test relationship updates and transfers
        // Transfer metadata2 from session2 to session3
        metadata2.session.target = session3;
        metadata2.sessionKey = 'integration_session_003';
        sessionLocalMetadataBox.put(metadata2);

        // Update metadata1's session properties and verify relationship integrity
        session1.sessionToken = 'updated_token_001';
        session1.active = false;
        sessionBox.put(session1);

        // ASSERT 2 - Verify relationship updates
        final updatedMetadata2 = sessionLocalMetadataBox.get(metadata2.id)!;
        expect(updatedMetadata2.session.target!.sessionKey, equals('integration_session_003'));
        expect(updatedMetadata2.sessionKey, equals('integration_session_003'));
        expect(updatedMetadata2.relatedSessionId, equals('integration_server_003'));

        // Verify that metadata1's relationship reflects session1 updates
        final updatedMetadata1 = sessionLocalMetadataBox.get(metadata1.id)!;
        expect(updatedMetadata1.session.target!.sessionToken, equals('updated_token_001'));
        expect(updatedMetadata1.session.target!.active, isFalse);
        expect(updatedMetadata1.relatedSessionToken, equals('updated_token_001'));

        // ACT 3 - Test complex queries across relationships
        // Query metadata by session properties
        final activeSessionMetadata = sessionLocalMetadataBox.query(
          SessionLocalMetadata_.session.equals(session1.id)
        ).build().find();

        final metadataWithEnglish = sessionLocalMetadataBox.getAll()
            .where((m) => m.speechLang == 'en')
            .toList();

        // ASSERT 3 - Verify complex relationship queries
        expect(activeSessionMetadata.length, equals(1));
        expect(activeSessionMetadata.first.sessionKey, equals('integration_session_001'));

        expect(metadataWithEnglish.length, equals(1));
        expect(metadataWithEnglish.first.session.target!.sessionKey, equals('integration_session_001'));

        // ACT 4 - Test cascade behavior and relationship cleanup
        // Remove session2 and verify metadata behavior
        sessionBox.remove(session2.id);

        // ASSERT 4 - Verify cascade behavior
        // metadata2 should still exist but relationship should be null since it was transferred to session3
        final finalMetadata2 = sessionLocalMetadataBox.get(metadata2.id)!;
        expect(finalMetadata2.session.target, isNotNull); // Still linked to session3
        expect(finalMetadata2.session.target!.sessionKey, equals('integration_session_003'));

        // ACT 5 - Test helper method functionality in integration context
        final availableSessions = sessionBox.getAll();
        metadata3.setSessionByKey('integration_session_001', availableSessions);
        sessionLocalMetadataBox.put(metadata3);

        // ASSERT 5 - Verify helper method integration
        final finalMetadata3 = sessionLocalMetadataBox.get(metadata3.id)!;
        expect(finalMetadata3.session.target, isNotNull);
        expect(finalMetadata3.session.target!.sessionKey, equals('integration_session_001'));
        expect(finalMetadata3.sessionKey, equals('integration_session_001'));
        expect(finalMetadata3.hasSessionRelationship, isTrue);

        // FINAL VERIFICATION - Ensure data integrity across all operations
        final allMetadata = sessionLocalMetadataBox.getAll();
        final allSessions = sessionBox.getAll();

        expect(allMetadata.length, equals(3));
        expect(allSessions.length, equals(2)); // session2 was deleted

        // Verify no orphaned relationships
        final metadataWithValidRelationships = allMetadata
            .where((m) => m.session.target != null)
            .length;
        expect(metadataWithValidRelationships, equals(3)); // All should have valid relationships now

        print('✅ Comprehensive Integration Test completed successfully');
        print('   - Created 3 sessions and 3 metadata entries');
        print('   - Tested relationship establishment, updates, and transfers');
        print('   - Verified complex queries and cascade behavior');
        print('   - Validated helper method integration');
        print('   - Ensured data integrity across all operations');
      });
    });
  });
}
