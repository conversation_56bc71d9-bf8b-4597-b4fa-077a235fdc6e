import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/session_local_metadata.dart';
import 'package:data_router/src/data/database/entities/history.dart';
import 'package:data_router/src/data/database/entities/manager.dart';
import 'package:data_router/src/data/database/entities/private_data.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<SessionLocalMetadata> sessionLocalMetadataBox;
  late Box<History> historyBox;
  late Box<Manager> managerBox;
  late Box<PrivateData> privateDataBox;
  late Box<Session> sessionBox;
  late Box<User> userBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir = Directory.systemTemp.createTempSync('metadata_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    sessionLocalMetadataBox = store.box<SessionLocalMetadata>();
    historyBox = store.box<History>();
    managerBox = store.box<Manager>();
    privateDataBox = store.box<PrivateData>();
    sessionBox = store.box<Session>();
    userBox = store.box<User>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    sessionLocalMetadataBox.removeAll();
    historyBox.removeAll();
    managerBox.removeAll();
    privateDataBox.removeAll();
    sessionBox.removeAll();
    userBox.removeAll();
  });

  group('🔗 ObjectBox Metadata Entities Relationships Integration Tests', () {
    test(
        'should create SessionLocalMetadata with ToOne relationship to Session',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'metadata_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final metadata = SessionLocalMetadata.create(
        sessionKey: 'metadata_session_001',
        introChannelView: true,
        speechLang: 'en',
        speechToTextEnable: true,
      );

      // Set relationship
      metadata.session.target = session;

      // Act
      final metadataId = sessionLocalMetadataBox.put(metadata);
      final retrievedMetadata = sessionLocalMetadataBox.get(metadataId)!;

      // Assert
      expect(retrievedMetadata.sessionKey, 'metadata_session_001');
      expect(retrievedMetadata.introChannelView, true);
      expect(retrievedMetadata.speechLang, 'en');
      expect(retrievedMetadata.speechToTextEnable, true);

      // Verify ToOne relationship
      expect(retrievedMetadata.session.target, isNotNull);
      expect(
          retrievedMetadata.session.target!.sessionKey, 'metadata_session_001');
      expect(retrievedMetadata.hasSessionRelationship, true);
      expect(retrievedMetadata.relatedSessionId, 'session_001');
    });

    test('should create History with ToOne relationship to Session', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'history_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final history = History.create(
        sessionKey: 'history_session_001',
        historyId: 'history_001',
        entityType: 'User',
        entityId: 'user_001',
        action: 'create',
        changeData: '{"name": "test"}',
      );

      // Set relationship
      history.session.target = session;

      // Act
      final historyId = historyBox.put(history);
      final retrievedHistory = historyBox.get(historyId)!;

      // Assert
      expect(retrievedHistory.sessionKey, 'history_session_001');
      expect(retrievedHistory.historyId, 'history_001');
      expect(retrievedHistory.entityType, 'User');
      expect(retrievedHistory.action, 'create');
      expect(retrievedHistory.isCreateAction, true);
      expect(retrievedHistory.hasChangeData, true);

      // Verify ToOne relationship
      expect(retrievedHistory.session.target, isNotNull);
      expect(
          retrievedHistory.session.target!.sessionKey, 'history_session_001');
      expect(retrievedHistory.hasSessionRelationship, true);
      expect(retrievedHistory.relatedSessionId, 'session_001');
    });

    test('should create Manager with ToOne relationship to Session', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'manager_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final manager = Manager.create(
        sessionKey: 'manager_session_001',
        loadedAllChannels: true,
        loadedAllFriends: false,
        userStatusEmojis: '😀😃😄',
      );

      // Set relationship
      manager.session.target = session;

      // Act
      final managerId = managerBox.put(manager);
      final retrievedManager = managerBox.get(managerId)!;

      // Assert
      expect(retrievedManager.sessionKey, 'manager_session_001');
      expect(retrievedManager.loadedAllChannels, true);
      expect(retrievedManager.loadedAllFriends, false);
      expect(retrievedManager.userStatusEmojis, '😀😃😄');
      expect(retrievedManager.isAllDataLoaded, false);

      // Verify ToOne relationship
      expect(retrievedManager.session.target, isNotNull);
      expect(
          retrievedManager.session.target!.sessionKey, 'manager_session_001');
      expect(retrievedManager.hasSessionRelationship, true);
      expect(retrievedManager.relatedSessionId, 'session_001');
    });

    test(
        'should create PrivateData with dual ToOne relationships to Session and User',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'private_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final user = User.create(
        sessionKey: 'private_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      final privateData = PrivateData.create(
        sessionKey: 'private_session_001',
        userId: 'user_001',
      );

      // Set relationships
      privateData.session.target = session;
      privateData.user.target = user;

      // Act
      final privateDataId = privateDataBox.put(privateData);
      final retrievedPrivateData = privateDataBox.get(privateDataId)!;

      // Assert
      expect(retrievedPrivateData.sessionKey, 'private_session_001');
      expect(retrievedPrivateData.userId, 'user_001');

      // Verify ToOne relationships
      expect(retrievedPrivateData.session.target, isNotNull);
      expect(retrievedPrivateData.session.target!.sessionKey,
          'private_session_001');
      expect(retrievedPrivateData.hasSessionRelationship, true);
      expect(retrievedPrivateData.relatedSessionId, 'session_001');

      expect(retrievedPrivateData.user.target, isNotNull);
      expect(retrievedPrivateData.user.target!.userId, 'user_001');
      expect(retrievedPrivateData.hasUserRelationship, true);
      expect(retrievedPrivateData.relatedUserName, 'testuser');
    });

    test('should create Session with backlinks to all metadata entities', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final user = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      // Create metadata entities
      final metadata = SessionLocalMetadata.create(
        sessionKey: 'backlink_session_001',
        introChannelView: true,
      );
      metadata.session.target = session;

      final history = History.create(
        sessionKey: 'backlink_session_001',
        historyId: 'history_001',
        entityType: 'User',
        entityId: 'user_001',
        action: 'update',
      );
      history.session.target = session;

      final manager = Manager.create(
        sessionKey: 'backlink_session_001',
        loadedAllChannels: true,
      );
      manager.session.target = session;

      final privateData = PrivateData.create(
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
      );
      privateData.session.target = session;
      privateData.user.target = user;

      // Act
      sessionLocalMetadataBox.put(metadata);
      historyBox.put(history);
      managerBox.put(manager);
      privateDataBox.put(privateData);

      final retrievedSession = sessionBox.get(sessionId)!;

      // Assert
      expect(retrievedSession.sessionKey, 'backlink_session_001');

      // Verify backlink relationships using queries (1:1 simulation)
      final sessionMetadataQuery = sessionLocalMetadataBox
          .query(
              SessionLocalMetadata_.sessionKey.equals('backlink_session_001'))
          .build();
      final sessionMetadata = sessionMetadataQuery.findFirst();
      sessionMetadataQuery.close();

      expect(sessionMetadata, isNotNull); // 1:1 relationship
      expect(sessionMetadata!.introChannelView, true);

      expect(retrievedSession.histories.length, 1);
      expect(retrievedSession.histories.first.action, 'update');

      final sessionManagerQuery = managerBox
          .query(Manager_.sessionKey.equals('backlink_session_001'))
          .build();
      final sessionManager = sessionManagerQuery.findFirst();
      sessionManagerQuery.close();

      expect(sessionManager, isNotNull); // 1:1 relationship
      expect(sessionManager!.loadedAllChannels, true);

      expect(retrievedSession.privateDataRecords.length, 1);
      expect(retrievedSession.privateDataRecords.first.userId, 'user_001');
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'delete_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final metadata = SessionLocalMetadata.create(
        sessionKey: 'delete_session_001',
        introChannelView: true,
      );
      metadata.session.target = session;
      final metadataId = sessionLocalMetadataBox.put(metadata);

      // Act - Delete session
      sessionBox.remove(sessionId);

      // Assert
      expect(sessionBox.get(sessionId), isNull);

      // Verify metadata still exists but relationship is orphaned
      final retrievedMetadata = sessionLocalMetadataBox.get(metadataId);
      expect(retrievedMetadata, isNotNull);
      expect(retrievedMetadata!.sessionKey,
          'delete_session_001'); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedMetadata.session.target, isNull);
      expect(retrievedMetadata.hasSessionRelationship, false);
      expect(retrievedMetadata.relatedSessionId, '');
    });
  });

  group('🔄 Metadata Comprehensive Integration Tests', () {
    test('Complete Metadata Entities Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Complex Metadata Scenario ====================

      // Create session
      final session = Session.create(
        sessionKey: 'comprehensive_metadata_session_001',
        sessionId: 'comprehensive_session_001',
        sessionToken: 'comprehensive_token_001',
      );
      final sessionId = sessionBox.put(session);

      // Create user
      final user = User.create(
        sessionKey: 'comprehensive_metadata_session_001',
        userId: 'comprehensive_user_001',
        username: 'comprehensiveuser',
      );
      final userId = userBox.put(user);

      // Create all metadata entities
      final metadata = SessionLocalMetadata.create(
        sessionKey: 'comprehensive_metadata_session_001',
        introChannelView: false,
        speechLang: 'vi',
        speechToTextEnable: false,
      );

      final history1 = History.create(
        sessionKey: 'comprehensive_metadata_session_001',
        historyId: 'history_001',
        entityType: 'User',
        entityId: 'comprehensive_user_001',
        action: 'create',
        changeData: '{"username": "comprehensiveuser"}',
      );

      final history2 = History.create(
        sessionKey: 'comprehensive_metadata_session_001',
        historyId: 'history_002',
        entityType: 'User',
        entityId: 'comprehensive_user_001',
        action: 'update',
        changeData: '{"username": "updated_user"}',
      );

      final manager = Manager.create(
        sessionKey: 'comprehensive_metadata_session_001',
        loadedAllChannels: false,
        loadedAllFriends: false,
        loadedAllFriendRequests: false,
        userStatusEmojis: '🎉🎊🎈',
      );

      final privateData = PrivateData.create(
        sessionKey: 'comprehensive_metadata_session_001',
        userId: 'comprehensive_user_001',
      );

      // Set relationships
      metadata.session.target = session;
      history1.session.target = session;
      history2.session.target = session;
      manager.session.target = session;
      privateData.session.target = session;
      privateData.user.target = user;

      final metadataIds = [
        sessionLocalMetadataBox.put(metadata),
        historyBox.put(history1),
        historyBox.put(history2),
        managerBox.put(manager),
        privateDataBox.put(privateData),
      ];

      // ==================== PHASE 2: READ - Verify All Relationships ====================

      final retrievedSession = sessionBox.get(sessionId)!;
      final retrievedUser = userBox.get(userId)!;

      // Verify session relationships using queries (1:1 simulation)
      final comprehensiveMetadataQuery = sessionLocalMetadataBox
          .query(SessionLocalMetadata_.sessionKey
              .equals('comprehensive_metadata_session_001'))
          .build();
      final comprehensiveMetadata = comprehensiveMetadataQuery.findFirst();
      comprehensiveMetadataQuery.close();

      expect(comprehensiveMetadata, isNotNull); // 1:1 relationship
      expect(retrievedSession.histories.length, 2);

      final comprehensiveManagerQuery = managerBox
          .query(
              Manager_.sessionKey.equals('comprehensive_metadata_session_001'))
          .build();
      final comprehensiveManager = comprehensiveManagerQuery.findFirst();
      comprehensiveManagerQuery.close();

      expect(comprehensiveManager, isNotNull); // 1:1 relationship
      expect(retrievedSession.privateDataRecords.length, 1);

      // Verify user relationships
      expect(retrievedUser.generalPrivateDataRecords.length, 1);

      // Verify individual entities
      final retrievedMetadata = sessionLocalMetadataBox.get(metadataIds[0])!;
      expect(retrievedMetadata.hasSessionRelationship, true);
      expect(retrievedMetadata.relatedSessionToken, 'comprehensive_token_001');

      final retrievedHistory1 = historyBox.get(metadataIds[1])!;
      expect(retrievedHistory1.isCreateAction, true);
      expect(retrievedHistory1.hasChangeData, true);

      final retrievedManager = managerBox.get(metadataIds[3])!;
      expect(retrievedManager.isAllDataLoaded, false);

      final retrievedPrivateData = privateDataBox.get(metadataIds[4])!;
      expect(retrievedPrivateData.hasSessionRelationship, true);
      expect(retrievedPrivateData.hasUserRelationship, true);

      // ==================== PHASE 3: UPDATE - Test Updates ====================

      // Update metadata
      retrievedMetadata.markAllIntrosViewed();
      sessionLocalMetadataBox.put(retrievedMetadata);

      // Update manager
      retrievedManager.markAllChannelsLoaded();
      retrievedManager.markAllFriendsLoaded();
      retrievedManager.markAllFriendRequestsLoaded();
      managerBox.put(retrievedManager);

      // Update private data
      retrievedPrivateData.update();
      privateDataBox.put(retrievedPrivateData);

      // Verify updates
      final finalMetadata = sessionLocalMetadataBox.get(metadataIds[0])!;
      expect(finalMetadata.introChannelView, true);
      expect(finalMetadata.introChannelList, true);

      final finalManager = managerBox.get(metadataIds[3])!;
      expect(finalManager.isAllDataLoaded, true);

      final finalPrivateData = privateDataBox.get(metadataIds[4])!;
      expect(finalPrivateData.updateTime, isNotNull);

      // ==================== PHASE 4: DELETE - Test Deletion Scenarios ====================

      // Delete one history record
      historyBox.remove(metadataIds[2]);

      // Verify deletion
      expect(historyBox.get(metadataIds[2]), isNull);

      // Verify session relationship updated
      final finalSession = sessionBox.get(sessionId)!;
      expect(finalSession.histories.length, 1);
      expect(finalSession.histories.first.action, 'create');

      // ==================== PHASE 5: FINAL VERIFICATION ====================

      expect(sessionLocalMetadataBox.count(), 1);
      expect(historyBox.count(), 1);
      expect(managerBox.count(), 1);
      expect(privateDataBox.count(), 1);
      expect(sessionBox.count(), 1);
      expect(userBox.count(), 1);

      print(
          '✅ Metadata Comprehensive Integration Test PASSED - All metadata entity relationships working correctly');
    });
  });
}
