import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/visited_profile.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<VisitedProfile> visitedProfileBox;
  late Box<User> userBox;
  late Box<Session> sessionBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir =
        Directory.systemTemp.createTempSync('visited_profile_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    visitedProfileBox = store.box<VisitedProfile>();
    userBox = store.box<User>();
    sessionBox = store.box<Session>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    visitedProfileBox.removeAll();
    userBox.removeAll();
    sessionBox.removeAll();
  });

  group('🔗 ObjectBox VisitedProfile-User Relationships Integration Tests', () {
    test('should create VisitedProfile with ToOne relationship to User', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'visited_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final visitedUser = User.create(
        sessionKey: 'visited_session_001',
        userId: 'visited_user_001',
        username: 'visiteduser',
      );
      final visitedUserId = userBox.put(visitedUser);

      final visitedProfile = VisitedProfile.create(
        sessionKey: 'visited_session_001',
        visitedUserId: 'visited_user_001',
        isRead: false,
      );

      // Set relationship
      visitedProfile.visitedUser.target = visitedUser;

      // Act
      final visitedProfileId = visitedProfileBox.put(visitedProfile);
      final retrievedVisitedProfile = visitedProfileBox.get(visitedProfileId)!;

      // Assert
      expect(retrievedVisitedProfile.sessionKey, 'visited_session_001');
      expect(retrievedVisitedProfile.visitedUserId, 'visited_user_001');
      expect(retrievedVisitedProfile.isRead, false);

      // Verify ToOne relationship
      expect(retrievedVisitedProfile.visitedUser.target, isNotNull);
      expect(
        retrievedVisitedProfile.visitedUser.target!.userId,
        'visited_user_001',
      );
      expect(retrievedVisitedProfile.hasVisitedUserRelationship, true);
      expect(retrievedVisitedProfile.visitedUserName, 'visiteduser');
      expect(retrievedVisitedProfile.effectiveVisitedUserName, 'visiteduser');
    });

    test('should create User with backlink to VisitedProfile', () {
      // Arrange
      final visitedUser = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'backlink_visited_user_001',
        username: 'backlinkvisiteduser',
      );
      final visitedUserId = userBox.put(visitedUser);

      final visitedProfile1 = VisitedProfile.create(
        sessionKey: 'backlink_session_001',
        visitedUserId: 'backlink_visited_user_001',
        isRead: false,
      );

      final visitedProfile2 = VisitedProfile.create(
        sessionKey: 'backlink_session_001',
        visitedUserId: 'backlink_visited_user_001',
        isRead: true,
      );

      // Set relationships
      visitedProfile1.visitedUser.target = visitedUser;
      visitedProfile2.visitedUser.target = visitedUser;

      // Act
      final visitedProfileIds =
          visitedProfileBox.putMany([visitedProfile1, visitedProfile2]);
      final retrievedUser = userBox.get(visitedUserId)!;

      // Assert
      expect(retrievedUser.userId, 'backlink_visited_user_001');
      expect(retrievedUser.username, 'backlinkvisiteduser');

      // Verify backlink relationship
      expect(retrievedUser.visitedProfiles.length, 2);
      expect(retrievedUser.visitedProfiles[0].isRead, false);
      expect(retrievedUser.visitedProfiles[1].isRead, true);
    });

    test('should handle VisitedProfile updates correctly', () async {
      // Arrange
      final visitedUser = User.create(
        sessionKey: 'update_session_001',
        userId: 'update_visited_user_001',
        username: 'updatevisiteduser',
      );
      final visitedUserId = userBox.put(visitedUser);

      final visitedProfile = VisitedProfile.create(
        sessionKey: 'update_session_001',
        visitedUserId: 'update_visited_user_001',
        isRead: false,
      );

      // Set relationship
      visitedProfile.visitedUser.target = visitedUser;
      final visitedProfileId = visitedProfileBox.put(visitedProfile);

      // Act - Update visited profile
      final retrievedVisitedProfile = visitedProfileBox.get(visitedProfileId)!;
      final originalUpdateTime = retrievedVisitedProfile.updateTime;

      // Wait a bit to ensure time difference
      await Future.delayed(Duration(milliseconds: 10));

      retrievedVisitedProfile.markAsRead();
      visitedProfileBox.put(retrievedVisitedProfile);

      // Assert
      final finalVisitedProfile = visitedProfileBox.get(visitedProfileId)!;
      expect(finalVisitedProfile.isRead, true);
      expect(
        finalVisitedProfile.updateTime!.isAfter(originalUpdateTime!),
        true,
      );

      // Test unread
      finalVisitedProfile.markAsUnread();
      visitedProfileBox.put(finalVisitedProfile);

      final unreadVisitedProfile = visitedProfileBox.get(visitedProfileId)!;
      expect(unreadVisitedProfile.isRead, false);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final visitedUser = User.create(
        sessionKey: 'delete_session_001',
        userId: 'delete_visited_user_001',
        username: 'deletevisiteduser',
      );
      final visitedUserId = userBox.put(visitedUser);

      final visitedProfile = VisitedProfile.create(
        sessionKey: 'delete_session_001',
        visitedUserId: 'delete_visited_user_001',
        isRead: false,
      );

      // Set relationship
      visitedProfile.visitedUser.target = visitedUser;
      final visitedProfileId = visitedProfileBox.put(visitedProfile);

      // Act - Delete visited user
      userBox.remove(visitedUserId);

      // Assert
      expect(userBox.get(visitedUserId), isNull);

      // Verify visited profile still exists but relationship is orphaned
      final retrievedVisitedProfile = visitedProfileBox.get(visitedProfileId);
      expect(retrievedVisitedProfile, isNotNull);
      expect(
        retrievedVisitedProfile!.visitedUserId,
        'delete_visited_user_001',
      ); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedVisitedProfile.visitedUser.target, isNull);
      expect(retrievedVisitedProfile.hasVisitedUserRelationship, false);
      expect(retrievedVisitedProfile.visitedUserName, '');
      expect(
        retrievedVisitedProfile.effectiveVisitedUserName,
        'delete_visited_user_001',
      );
    });

    test('should handle time-based functionality correctly', () {
      // Arrange
      final visitedUser = User.create(
        sessionKey: 'time_session_001',
        userId: 'time_visited_user_001',
        username: 'timevisiteduser',
      );
      final visitedUserId = userBox.put(visitedUser);

      final visitedProfile = VisitedProfile.create(
        sessionKey: 'time_session_001',
        visitedUserId: 'time_visited_user_001',
        isRead: false,
      );

      // Set relationship
      visitedProfile.visitedUser.target = visitedUser;
      final visitedProfileId = visitedProfileBox.put(visitedProfile);

      // Act & Assert - Test time functionality
      final retrievedVisitedProfile = visitedProfileBox.get(visitedProfileId)!;

      // Should be recent visit (just created)
      expect(retrievedVisitedProfile.isRecentVisit, true);

      // Test time since visit
      final timeSinceVisit = retrievedVisitedProfile.timeSinceVisit;
      expect(timeSinceVisit, 'Just now');

      // Update visit time
      retrievedVisitedProfile.updateVisitTime();
      visitedProfileBox.put(retrievedVisitedProfile);

      final updatedVisitedProfile = visitedProfileBox.get(visitedProfileId)!;
      expect(updatedVisitedProfile.isRecentVisit, true);
    });
  });

  group('🔄 VisitedProfile Comprehensive Integration Tests', () {
    test('Complete VisitedProfile-User Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Complex VisitedProfile Scenario ====================

      // Create session
      final session = Session.create(
        sessionKey: 'comprehensive_visited_session_001',
        sessionId: 'comprehensive_session_001',
        sessionToken: 'comprehensive_token_001',
      );
      final sessionId = sessionBox.put(session);

      // Create multiple visited users
      final visitedUser1 = User.create(
        sessionKey: 'comprehensive_visited_session_001',
        userId: 'comprehensive_visited_user_001',
        username: 'visiteduser1',
      );

      final visitedUser2 = User.create(
        sessionKey: 'comprehensive_visited_session_001',
        userId: 'comprehensive_visited_user_002',
        username: 'visiteduser2',
      );

      final visitedUserIds = userBox.putMany([visitedUser1, visitedUser2]);

      // Create visited profiles
      final visitedProfile1 = VisitedProfile.create(
        sessionKey: 'comprehensive_visited_session_001',
        visitedUserId: 'comprehensive_visited_user_001',
        isRead: false,
      );

      final visitedProfile2 = VisitedProfile.create(
        sessionKey: 'comprehensive_visited_session_001',
        visitedUserId: 'comprehensive_visited_user_002',
        isRead: true,
      );

      // Set relationships
      visitedProfile1.visitedUser.target = visitedUser1;
      visitedProfile2.visitedUser.target = visitedUser2;

      final visitedProfileIds =
          visitedProfileBox.putMany([visitedProfile1, visitedProfile2]);

      // ==================== PHASE 2: READ - Verify All Relationships ====================

      final retrievedUser1 = userBox.get(visitedUserIds[0])!;
      final retrievedUser2 = userBox.get(visitedUserIds[1])!;
      final retrievedVisitedProfile1 =
          visitedProfileBox.get(visitedProfileIds[0])!;
      final retrievedVisitedProfile2 =
          visitedProfileBox.get(visitedProfileIds[1])!;

      // Verify user relationships
      expect(retrievedUser1.visitedProfiles.length, 1);
      expect(retrievedUser2.visitedProfiles.length, 1);
      expect(retrievedUser1.visitedProfiles.first.isRead, false);
      expect(retrievedUser2.visitedProfiles.first.isRead, true);

      // Verify visited profile relationships
      expect(retrievedVisitedProfile1.hasVisitedUserRelationship, true);
      expect(retrievedVisitedProfile1.visitedUserName, 'visiteduser1');
      expect(retrievedVisitedProfile1.isRecentVisit, true);

      expect(retrievedVisitedProfile2.hasVisitedUserRelationship, true);
      expect(retrievedVisitedProfile2.visitedUserName, 'visiteduser2');
      expect(retrievedVisitedProfile2.isRecentVisit, true);

      // ==================== PHASE 3: UPDATE - Test Updates ====================

      // Update visited profiles
      retrievedVisitedProfile1.markAsRead();
      visitedProfileBox.put(retrievedVisitedProfile1);

      retrievedVisitedProfile2.markAsUnread();
      visitedProfileBox.put(retrievedVisitedProfile2);

      // Verify updates
      final finalVisitedProfile1 = visitedProfileBox.get(visitedProfileIds[0])!;
      expect(finalVisitedProfile1.isRead, true);

      final finalVisitedProfile2 = visitedProfileBox.get(visitedProfileIds[1])!;
      expect(finalVisitedProfile2.isRead, false);

      // ==================== PHASE 4: DELETE - Test Deletion Scenarios ====================

      // Delete one visited profile
      visitedProfileBox.remove(visitedProfileIds[1]);

      // Verify deletion
      expect(visitedProfileBox.get(visitedProfileIds[1]), isNull);
      expect(visitedProfileBox.get(visitedProfileIds[0]), isNotNull);

      // Verify user relationship updated
      final finalUser1 = userBox.get(visitedUserIds[0])!;
      final finalUser2 = userBox.get(visitedUserIds[1])!;
      expect(finalUser1.visitedProfiles.length, 1);
      expect(finalUser2.visitedProfiles.length, 0);

      // ==================== PHASE 5: FINAL VERIFICATION ====================

      expect(visitedProfileBox.count(), 1);
      expect(userBox.count(), 2);
      expect(sessionBox.count(), 1);

      print(
        '✅ VisitedProfile Comprehensive Integration Test PASSED - All VisitedProfile-User relationships working correctly',
      );
    });
  });
}
