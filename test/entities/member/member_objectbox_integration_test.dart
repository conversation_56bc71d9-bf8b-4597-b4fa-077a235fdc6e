import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/member.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/entities/channel.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/entities/profile.dart';
import 'package:data_router/src/data/database/entities/user_presence.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<Member> memberBox;
  late Box<Session> sessionBox;
  late Box<Channel> channelBox;
  late Box<User> userBox;
  late Box<Profile> profileBox;
  late Box<UserPresence> userPresenceBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir = Directory.systemTemp.createTempSync('member_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    memberBox = store.box<Member>();
    sessionBox = store.box<Session>();
    channelBox = store.box<Channel>();
    userBox = store.box<User>();
    profileBox = store.box<Profile>();
    userPresenceBox = store.box<UserPresence>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    memberBox.removeAll();
    sessionBox.removeAll();
    channelBox.removeAll();
    userBox.removeAll();
    profileBox.removeAll();
    userPresenceBox.removeAll();
  });

  group('🔗 ObjectBox Member Entity Relationships Integration Tests', () {
    test(
        'should create Member with triple ToOne relationships to Session, Channel, and User',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'member_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'member_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'General Channel',
        channelTypeRaw: 0,
      );
      final channelId = channelBox.put(channel);

      final user = User.create(
        sessionKey: 'member_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      // Create profile for user
      final profile = Profile.create(
        sessionKey: 'member_session_001',
        userId: 'user_001',
        displayName: 'Test User',
        avatar: 'avatar.jpg',
      );
      profile.user.target = user;
      profileBox.put(profile);

      // Create presence for user
      final presence = UserPresence.create(
        sessionKey: 'member_session_001',
        userId: 'user_001',
        presenceStatus: 1, // Online
      );
      presence.user.target = user;
      userPresenceBox.put(presence);

      final member = Member.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        userId: 'user_001',
        sessionKey: 'member_session_001',
        nickname: 'TestNick',
        role: 'admin',
        rolesRaw: 'admin,moderator',
      );

      // Set relationships
      member.session.target = session;
      member.channel.target = channel;
      member.user.target = user;

      // Act
      final memberId = memberBox.put(member);
      final retrievedMember = memberBox.get(memberId)!;

      // Assert
      expect(retrievedMember.workspaceId, 'workspace_001');
      expect(retrievedMember.channelId, 'channel_001');
      expect(retrievedMember.userId, 'user_001');
      expect(retrievedMember.sessionKey, 'member_session_001');
      expect(retrievedMember.nickname, 'TestNick');
      expect(retrievedMember.role, 'admin');
      expect(retrievedMember.rolesRaw, 'admin,moderator');
      expect(retrievedMember.hasNickname, true);

      // Verify ToOne relationships
      expect(retrievedMember.session.target, isNotNull);
      expect(retrievedMember.session.target!.sessionKey, 'member_session_001');
      expect(retrievedMember.hasSessionRelationship, true);
      expect(retrievedMember.relatedSessionId, 'session_001');

      expect(retrievedMember.channel.target, isNotNull);
      expect(retrievedMember.channel.target!.channelId, 'channel_001');
      expect(retrievedMember.hasChannelRelationship, true);
      expect(retrievedMember.relatedChannelName, 'General Channel');

      expect(retrievedMember.user.target, isNotNull);
      expect(retrievedMember.user.target!.userId, 'user_001');
      expect(retrievedMember.hasUserRelationship, true);
      expect(retrievedMember.relatedUserUsername, 'testuser');

      // Verify helper methods
      expect(retrievedMember.effectiveDisplayName, 'TestNick');
      expect(retrievedMember.finalDisplayName, 'TestNick');
      expect(retrievedMember.roleDescription, 'Administrator');
      expect(retrievedMember.isAdminOrOwner, true);
      expect(retrievedMember.isModeratorOrHigher, true);
      expect(retrievedMember.hasSpecialPermissions, true);
      expect(retrievedMember.memberStatusDescription, 'Complete');

      // Verify relationship helper methods
      expect(retrievedMember.relatedUserDisplayName, 'Test User');
      expect(retrievedMember.relatedEffectiveUserName, 'Test User');
      expect(retrievedMember.isRelatedUserOnline, true);
      expect(retrievedMember.relatedChannelTypeDescription, 'Text Channel');
    });

    test('should create Channel with backlink to Member', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'backlink_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Team Channel',
        channelTypeRaw: 0,
      );
      final channelId = channelBox.put(channel);

      final user1 = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
        username: 'user1',
      );
      userBox.put(user1);

      final user2 = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'user_002',
        username: 'user2',
      );
      userBox.put(user2);

      // Create multiple members for the channel
      final member1 = Member.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        userId: 'user_001',
        sessionKey: 'backlink_session_001',
        role: 'owner',
      );
      member1.session.target = session;
      member1.channel.target = channel;
      member1.user.target = user1;

      final member2 = Member.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        userId: 'user_002',
        sessionKey: 'backlink_session_001',
        role: 'member',
        nickname: 'User Two',
      );
      member2.session.target = session;
      member2.channel.target = channel;
      member2.user.target = user2;

      // Act
      memberBox.put(member1);
      memberBox.put(member2);

      final retrievedChannel = channelBox.get(channelId)!;

      // Assert
      expect(retrievedChannel.channelId, 'channel_001');
      expect(retrievedChannel.name, 'Team Channel');

      // Verify backlink relationship
      expect(retrievedChannel.members.length, 2);
      expect(retrievedChannel.membersCount, 2);
      expect(retrievedChannel.hasMembers, true);

      // Verify member filtering
      expect(retrievedChannel.ownerMember, isNotNull);
      expect(retrievedChannel.ownerMember!.userId, 'user_001');
      expect(retrievedChannel.adminMembers.length, 0);
      expect(retrievedChannel.regularMembers.length, 1);
      expect(retrievedChannel.membersWithNicknames.length, 1);
      expect(retrievedChannel.membersWithSpecialPermissions.length, 1);

      // Verify member queries
      expect(retrievedChannel.isUserMember('user_001'), true);
      expect(retrievedChannel.isUserMember('user_003'), false);
      expect(retrievedChannel.isUserAdminOrOwner('user_001'), true);
      expect(retrievedChannel.isUserAdminOrOwner('user_002'), false);

      final foundMember = retrievedChannel.getMemberByUserId('user_002');
      expect(foundMember, isNotNull);
      expect(foundMember!.nickname, 'User Two');
    });

    test('should create User with backlink to Member (memberships)', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'user_backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final user = User.create(
        sessionKey: 'user_backlink_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      final channel1 = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'user_backlink_session_001',
        channelOwnerUserId: 'user_001',
        recipientId: 'user_002',
        name: 'Channel 1',
      );
      channelBox.put(channel1);

      final channel2 = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_002',
        sessionKey: 'user_backlink_session_001',
        channelOwnerUserId: 'other_user',
        recipientId: 'user_001',
        name: 'Channel 2',
        isArchived: true,
      );
      channelBox.put(channel2);

      // Create multiple memberships for the user
      final membership1 = Member.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        userId: 'user_001',
        sessionKey: 'user_backlink_session_001',
        role: 'owner',
        nickname: 'Boss',
      );
      membership1.session.target = session;
      membership1.channel.target = channel1;
      membership1.user.target = user;

      final membership2 = Member.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_002',
        userId: 'user_001',
        sessionKey: 'user_backlink_session_001',
        role: 'member',
      );
      membership2.session.target = session;
      membership2.channel.target = channel2;
      membership2.user.target = user;

      // Act
      memberBox.put(membership1);
      memberBox.put(membership2);

      final retrievedUser = userBox.get(userId)!;

      // Assert
      expect(retrievedUser.userId, 'user_001');
      expect(retrievedUser.username, 'testuser');

      // Verify backlink relationship
      expect(retrievedUser.memberships.length, 2);
      expect(retrievedUser.membershipsCount, 2);
      expect(retrievedUser.hasMemberships, true);

      // Verify membership filtering
      expect(retrievedUser.ownerMemberships.length, 1);
      expect(retrievedUser.adminMemberships.length, 0);
      expect(retrievedUser.regularMemberships.length, 1);
      expect(retrievedUser.membershipsWithNicknames.length, 1);
      expect(retrievedUser.activeMemberships.length, 1);
      expect(retrievedUser.archivedMemberships.length, 1);

      // Verify membership counts
      expect(retrievedUser.ownerMembershipsCount, 1);
      expect(retrievedUser.adminMembershipsCount, 0);
      expect(retrievedUser.activeMembershipsCount, 1);
      expect(retrievedUser.hasOwnerMemberships, true);
      expect(retrievedUser.hasAdminMemberships, false);
      expect(retrievedUser.hasSpecialRoleMemberships, true);

      // Verify membership queries
      expect(retrievedUser.isMemberOfChannel('channel_001'), true);
      expect(retrievedUser.isMemberOfChannel('channel_003'), false);
      expect(retrievedUser.isAdminOrOwnerInChannel('channel_001'), true);
      expect(retrievedUser.isAdminOrOwnerInChannel('channel_002'), false);

      final foundMembership =
          retrievedUser.getMembershipInChannel('channel_001');
      expect(foundMembership, isNotNull);
      expect(foundMembership!.role, 'owner');
      expect(foundMembership.nickname, 'Boss');

      final workspaceMemberships =
          retrievedUser.getMembershipsByWorkspace('workspace_001');
      expect(workspaceMemberships.length, 2);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'delete_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'delete_session_001',
        channelOwnerUserId: 'user_001',
        recipientId: 'user_002',
        name: 'Test Channel',
      );
      final channelId = channelBox.put(channel);

      final user = User.create(
        sessionKey: 'delete_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      final member = Member.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        userId: 'user_001',
        sessionKey: 'delete_session_001',
        role: 'member',
      );
      member.session.target = session;
      member.channel.target = channel;
      member.user.target = user;
      final memberId = memberBox.put(member);

      // Act - Delete channel
      channelBox.remove(channelId);

      // Assert
      expect(channelBox.get(channelId), isNull);

      // Verify member still exists but relationship is orphaned
      final retrievedMember = memberBox.get(memberId);
      expect(retrievedMember, isNotNull);
      expect(
          retrievedMember!.channelId, 'channel_001'); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedMember.channel.target, isNull);
      expect(retrievedMember.hasChannelRelationship, false);
      expect(retrievedMember.relatedChannelName, '');

      // Verify user still exists but backlinks are updated
      final retrievedUser = userBox.get(userId)!;
      expect(retrievedUser.memberships.length, 1); // Member still exists
      expect(retrievedUser.memberChannels.length,
          0); // But no valid channel relationships
    });
  });

  group('🔄 Member Comprehensive Integration Tests', () {
    test('Complete Member Entity Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Complex Member Scenario ====================

      // Create session
      final session = Session.create(
        sessionKey: 'comprehensive_member_session_001',
        sessionId: 'comprehensive_session_001',
        sessionToken: 'comprehensive_token_001',
      );
      sessionBox.put(session);

      // Create channels
      final generalChannel = Channel.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'general_channel_001',
        sessionKey: 'comprehensive_member_session_001',
        channelOwnerUserId: 'admin_001',
        recipientId: 'dev_001',
        name: 'General Discussion',
        channelTypeRaw: 0,
        topic: 'General chat for everyone',
      );
      channelBox.put(generalChannel);

      final devChannel = Channel.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'dev_channel_001',
        sessionKey: 'comprehensive_member_session_001',
        channelOwnerUserId: 'admin_001',
        recipientId: 'dev_001',
        name: 'Development',
        channelTypeRaw: 0,
        topic: 'Development discussions',
      );
      channelBox.put(devChannel);

      // Create users
      final adminUser = User.create(
        sessionKey: 'comprehensive_member_session_001',
        userId: 'admin_001',
        username: 'admin',
      );
      userBox.put(adminUser);

      final devUser = User.create(
        sessionKey: 'comprehensive_member_session_001',
        userId: 'dev_001',
        username: 'developer',
      );
      userBox.put(devUser);

      final regularUser = User.create(
        sessionKey: 'comprehensive_member_session_001',
        userId: 'user_001',
        username: 'regularuser',
      );
      userBox.put(regularUser);

      // Create profiles
      final adminProfile = Profile.create(
        sessionKey: 'comprehensive_member_session_001',
        userId: 'admin_001',
        displayName: 'System Administrator',
        avatar: 'admin_avatar.jpg',
      );
      adminProfile.user.target = adminUser;
      profileBox.put(adminProfile);

      final devProfile = Profile.create(
        sessionKey: 'comprehensive_member_session_001',
        userId: 'dev_001',
        displayName: 'Senior Developer',
        avatar: 'dev_avatar.jpg',
      );
      devProfile.user.target = devUser;
      profileBox.put(devProfile);

      // Create presences
      final adminPresence = UserPresence.create(
        sessionKey: 'comprehensive_member_session_001',
        userId: 'admin_001',
        presenceStatus: 1, // Online
      );
      adminPresence.user.target = adminUser;
      userPresenceBox.put(adminPresence);

      final devPresence = UserPresence.create(
        sessionKey: 'comprehensive_member_session_001',
        userId: 'dev_001',
        presenceStatus: 0, // Offline
      );
      devPresence.user.target = devUser;
      userPresenceBox.put(devPresence);

      // Create members with different roles
      final adminGeneralMember = Member.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'general_channel_001',
        userId: 'admin_001',
        sessionKey: 'comprehensive_member_session_001',
        role: 'owner',
        nickname: 'Admin',
      );
      adminGeneralMember.session.target = session;
      adminGeneralMember.channel.target = generalChannel;
      adminGeneralMember.user.target = adminUser;

      final devGeneralMember = Member.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'general_channel_001',
        userId: 'dev_001',
        sessionKey: 'comprehensive_member_session_001',
        role: 'moderator',
        nickname: 'Dev Lead',
      );
      devGeneralMember.session.target = session;
      devGeneralMember.channel.target = generalChannel;
      devGeneralMember.user.target = devUser;

      final userGeneralMember = Member.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'general_channel_001',
        userId: 'user_001',
        sessionKey: 'comprehensive_member_session_001',
        role: 'member',
      );
      userGeneralMember.session.target = session;
      userGeneralMember.channel.target = generalChannel;
      userGeneralMember.user.target = regularUser;

      final devChannelMember = Member.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'dev_channel_001',
        userId: 'dev_001',
        sessionKey: 'comprehensive_member_session_001',
        role: 'admin',
        nickname: 'Tech Lead',
      );
      devChannelMember.session.target = session;
      devChannelMember.channel.target = devChannel;
      devChannelMember.user.target = devUser;

      final memberIds = [
        memberBox.put(adminGeneralMember),
        memberBox.put(devGeneralMember),
        memberBox.put(userGeneralMember),
        memberBox.put(devChannelMember),
      ];

      // ==================== PHASE 2: READ - Verify All Relationships ====================

      // Verify members
      final retrievedAdminMember = memberBox.get(memberIds[0])!;
      final retrievedDevMember = memberBox.get(memberIds[1])!;
      final retrievedUserMember = memberBox.get(memberIds[2])!;
      final retrievedDevChannelMember = memberBox.get(memberIds[3])!;

      // Verify admin member relationships
      expect(retrievedAdminMember.hasSessionRelationship, true);
      expect(retrievedAdminMember.hasChannelRelationship, true);
      expect(retrievedAdminMember.hasUserRelationship, true);
      expect(
          retrievedAdminMember.relatedSessionToken, 'comprehensive_token_001');
      expect(retrievedAdminMember.relatedChannelName, 'General Discussion');
      expect(retrievedAdminMember.relatedUserUsername, 'admin');
      expect(
          retrievedAdminMember.relatedUserDisplayName, 'System Administrator');
      expect(retrievedAdminMember.finalDisplayName, 'Admin');
      expect(retrievedAdminMember.roleDescription, 'Channel Owner');
      expect(retrievedAdminMember.isAdminOrOwner, true);
      expect(retrievedAdminMember.isRelatedUserOnline, true);

      // Verify dev member relationships
      expect(retrievedDevMember.roleDescription, 'Moderator');
      expect(retrievedDevMember.isModeratorOrHigher, true);
      expect(retrievedDevMember.isRelatedUserOnline, false);
      expect(retrievedDevMember.finalDisplayName, 'Dev Lead');

      // Verify regular member
      expect(retrievedUserMember.roleDescription, 'Member');
      expect(retrievedUserMember.isAdminOrOwner, false);
      expect(retrievedUserMember.hasSpecialPermissions, false);
      expect(retrievedUserMember.finalDisplayName,
          'regularuser'); // No nickname, no profile, returns username

      // ==================== PHASE 3: UPDATE - Test Updates ====================

      // Promote regular user to moderator
      retrievedUserMember.role = 'moderator';
      retrievedUserMember.nickname = 'Promoted User';
      memberBox.put(retrievedUserMember);

      // Update dev member nickname
      retrievedDevChannelMember.nickname = 'Senior Tech Lead';
      memberBox.put(retrievedDevChannelMember);

      // Verify updates
      final finalUserMember = memberBox.get(memberIds[2])!;
      expect(finalUserMember.role, 'moderator');
      expect(finalUserMember.nickname, 'Promoted User');
      expect(finalUserMember.roleDescription, 'Moderator');
      expect(finalUserMember.isModeratorOrHigher, true);
      expect(finalUserMember.finalDisplayName, 'Promoted User');

      final finalDevChannelMember = memberBox.get(memberIds[3])!;
      expect(finalDevChannelMember.nickname, 'Senior Tech Lead');

      // ==================== PHASE 4: VERIFY BACKLINKS ====================

      // Verify channel backlinks
      final finalGeneralChannel = channelBox
          .getAll()
          .firstWhere((c) => c.channelId == 'general_channel_001');
      expect(finalGeneralChannel.membersCount, 3);
      expect(finalGeneralChannel.hasMembers, true);
      expect(finalGeneralChannel.ownerMember!.userId, 'admin_001');
      expect(finalGeneralChannel.moderatorMembers.length,
          2); // dev and promoted user
      expect(finalGeneralChannel.regularMembers.length, 0);
      expect(finalGeneralChannel.onlineMembersCount, 1); // Only admin is online

      // Verify user backlinks
      final finalDevUser =
          userBox.getAll().firstWhere((u) => u.userId == 'dev_001');
      expect(finalDevUser.membershipsCount, 2);
      expect(finalDevUser.ownerMembershipsCount, 0);
      expect(finalDevUser.adminMembershipsCount, 1);
      expect(finalDevUser.moderatorMembershipsCount, 1);
      expect(finalDevUser.hasSpecialRoleMemberships, true);

      // ==================== PHASE 5: DELETE - Test Deletion Scenarios ====================

      // Remove one member
      memberBox.remove(memberIds[2]);

      // Verify deletion
      expect(memberBox.get(memberIds[2]), isNull);

      // ==================== PHASE 6: FINAL VERIFICATION ====================

      expect(memberBox.count(), 3);
      expect(channelBox.count(), 2);
      expect(userBox.count(), 3);
      expect(sessionBox.count(), 1);

      print(
          '✅ Member Comprehensive Integration Test PASSED - All member entity relationships working correctly');
    });
  });
}
