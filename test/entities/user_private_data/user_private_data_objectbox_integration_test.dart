import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/user_private_data.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<UserPrivateData> userPrivateDataBox;
  late Box<User> userBox;
  late Box<Session> sessionBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir = Directory.systemTemp.createTempSync('user_private_data_objectbox_test');
    
    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    userPrivateDataBox = store.box<UserPrivateData>();
    userBox = store.box<User>();
    sessionBox = store.box<Session>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    userPrivateDataBox.removeAll();
    userBox.removeAll();
    sessionBox.removeAll();
  });

  group('🔗 ObjectBox UserPrivateData-User Relationships Integration Tests', () {
    test('should create UserPrivateData with ToOne relationship to User', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'user_private_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final user = User.create(
        sessionKey: 'user_private_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      final userPrivateData = UserPrivateData.create(
        sessionKey: 'user_private_session_001',
        userId: 'user_001',
        aliasName: 'Test Alias',
        blocked: false,
        notificationStatus: true,
      );

      // Set relationship
      userPrivateData.user.target = user;

      // Act
      final userPrivateDataId = userPrivateDataBox.put(userPrivateData);
      final retrievedUserPrivateData = userPrivateDataBox.get(userPrivateDataId)!;

      // Assert
      expect(retrievedUserPrivateData.sessionKey, 'user_private_session_001');
      expect(retrievedUserPrivateData.userId, 'user_001');
      expect(retrievedUserPrivateData.aliasName, 'Test Alias');
      expect(retrievedUserPrivateData.blocked, false);
      expect(retrievedUserPrivateData.notificationStatus, true);

      // Verify ToOne relationship
      expect(retrievedUserPrivateData.user.target, isNotNull);
      expect(retrievedUserPrivateData.user.target!.userId, 'user_001');
      expect(retrievedUserPrivateData.hasUserRelationship, true);
      expect(retrievedUserPrivateData.userName, 'testuser');
    });

    test('should create User with backlink to UserPrivateData', () {
      // Arrange
      final user = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'backlink_user_001',
        username: 'backlinkuser',
      );
      final userId = userBox.put(user);

      final userPrivateData1 = UserPrivateData.create(
        sessionKey: 'backlink_session_001',
        userId: 'backlink_user_001',
        aliasName: 'Alias 1',
        source: 'source1',
      );

      final userPrivateData2 = UserPrivateData.create(
        sessionKey: 'backlink_session_001',
        userId: 'backlink_user_001',
        aliasName: 'Alias 2',
        source: 'source2',
      );

      // Set relationships
      userPrivateData1.user.target = user;
      userPrivateData2.user.target = user;

      // Act
      final privateDataIds = userPrivateDataBox.putMany([userPrivateData1, userPrivateData2]);
      final retrievedUser = userBox.get(userId)!;

      // Assert
      expect(retrievedUser.userId, 'backlink_user_001');
      expect(retrievedUser.username, 'backlinkuser');

      // Verify backlink relationship
      expect(retrievedUser.privateDataRecords.length, 2);
      expect(retrievedUser.privateDataRecords[0].aliasName, 'Alias 1');
      expect(retrievedUser.privateDataRecords[1].aliasName, 'Alias 2');
      expect(retrievedUser.privateDataRecords[0].source, 'source1');
      expect(retrievedUser.privateDataRecords[1].source, 'source2');
    });

    test('should handle UserPrivateData updates correctly', () {
      // Arrange
      final user = User.create(
        sessionKey: 'update_session_001',
        userId: 'update_user_001',
        username: 'updateuser',
      );
      final userId = userBox.put(user);

      final userPrivateData = UserPrivateData.create(
        sessionKey: 'update_session_001',
        userId: 'update_user_001',
        aliasName: 'Original Alias',
        blocked: false,
        notificationStatus: true,
      );

      // Set relationship
      userPrivateData.user.target = user;
      final userPrivateDataId = userPrivateDataBox.put(userPrivateData);

      // Act - Update private data
      final retrievedUserPrivateData = userPrivateDataBox.get(userPrivateDataId)!;
      retrievedUserPrivateData.updateAliasName('Updated Alias');
      retrievedUserPrivateData.blockUser();
      retrievedUserPrivateData.disableNotifications();
      userPrivateDataBox.put(retrievedUserPrivateData);

      // Assert
      final finalUserPrivateData = userPrivateDataBox.get(userPrivateDataId)!;
      expect(finalUserPrivateData.aliasName, 'Updated Alias');
      expect(finalUserPrivateData.blocked, true);
      expect(finalUserPrivateData.notificationStatus, false);
      expect(finalUserPrivateData.version, 3); // Should be incremented 3 times
      expect(finalUserPrivateData.hasAlias, true);
      expect(finalUserPrivateData.effectiveDisplayName, 'Updated Alias');
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final user = User.create(
        sessionKey: 'delete_session_001',
        userId: 'delete_user_001',
        username: 'deleteuser',
      );
      final userId = userBox.put(user);

      final userPrivateData = UserPrivateData.create(
        sessionKey: 'delete_session_001',
        userId: 'delete_user_001',
        aliasName: 'Delete Test Alias',
      );

      // Set relationship
      userPrivateData.user.target = user;
      final userPrivateDataId = userPrivateDataBox.put(userPrivateData);

      // Act - Delete user
      userBox.remove(userId);

      // Assert
      expect(userBox.get(userId), isNull);

      // Verify private data still exists but relationship is orphaned
      final retrievedUserPrivateData = userPrivateDataBox.get(userPrivateDataId);
      expect(retrievedUserPrivateData, isNotNull);
      expect(retrievedUserPrivateData!.userId, 'delete_user_001'); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedUserPrivateData.user.target, isNull);
      expect(retrievedUserPrivateData.hasUserRelationship, false);
      expect(retrievedUserPrivateData.userName, '');
    });

    test('should handle alias and display name logic correctly', () {
      // Arrange
      final user = User.create(
        sessionKey: 'alias_session_001',
        userId: 'alias_user_001',
        username: 'aliasuser',
      );
      final userId = userBox.put(user);

      final userPrivateData = UserPrivateData.create(
        sessionKey: 'alias_session_001',
        userId: 'alias_user_001',
        aliasName: '',
      );

      // Set relationship
      userPrivateData.user.target = user;
      final userPrivateDataId = userPrivateDataBox.put(userPrivateData);

      // Act & Assert - No alias initially
      expect(userPrivateData.hasAlias, false);
      expect(userPrivateData.effectiveDisplayName, 'alias_user_001');

      // Act - Set alias
      userPrivateData.updateAliasName('My Custom Alias');
      userPrivateDataBox.put(userPrivateData);

      final updatedUserPrivateData = userPrivateDataBox.get(userPrivateDataId)!;
      expect(updatedUserPrivateData.hasAlias, true);
      expect(updatedUserPrivateData.effectiveDisplayName, 'My Custom Alias');
      expect(updatedUserPrivateData.effectiveName, 'My Custom Alias');
    });
  });

  group('🔄 UserPrivateData Comprehensive Integration Tests', () {
    test('Complete UserPrivateData-User Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Complex UserPrivateData Scenario ====================
      
      // Create session
      final session = Session.create(
        sessionKey: 'comprehensive_private_session_001',
        sessionId: 'comprehensive_session_001',
        sessionToken: 'comprehensive_token_001',
      );
      final sessionId = sessionBox.put(session);
      
      // Create user
      final user = User.create(
        sessionKey: 'comprehensive_private_session_001',
        userId: 'comprehensive_user_001',
        username: 'comprehensiveuser',
      );
      final userId = userBox.put(user);
      
      // Create multiple private data records
      final privateData1 = UserPrivateData.create(
        sessionKey: 'comprehensive_private_session_001',
        userId: 'comprehensive_user_001',
        aliasName: 'Work Alias',
        source: 'work',
        dmId: 'dm_work_001',
        blocked: false,
        notificationStatus: true,
      );
      
      final privateData2 = UserPrivateData.create(
        sessionKey: 'comprehensive_private_session_001',
        userId: 'comprehensive_user_001',
        aliasName: 'Personal Alias',
        source: 'personal',
        dmId: 'dm_personal_001',
        blocked: false,
        notificationStatus: false,
      );
      
      // Set relationships
      privateData1.user.target = user;
      privateData2.user.target = user;
      
      final privateDataIds = userPrivateDataBox.putMany([privateData1, privateData2]);

      // ==================== PHASE 2: READ - Verify All Relationships ====================
      
      final retrievedUser = userBox.get(userId)!;
      final retrievedPrivateData1 = userPrivateDataBox.get(privateDataIds[0])!;
      final retrievedPrivateData2 = userPrivateDataBox.get(privateDataIds[1])!;
      
      // Verify user relationships
      expect(retrievedUser.privateDataRecords.length, 2);
      expect(retrievedUser.privateDataRecords[0].source, 'work');
      expect(retrievedUser.privateDataRecords[1].source, 'personal');
      
      // Verify private data relationships
      expect(retrievedPrivateData1.hasUserRelationship, true);
      expect(retrievedPrivateData1.userName, 'comprehensiveuser');
      expect(retrievedPrivateData1.effectiveName, 'Work Alias');
      
      expect(retrievedPrivateData2.hasUserRelationship, true);
      expect(retrievedPrivateData2.userName, 'comprehensiveuser');
      expect(retrievedPrivateData2.effectiveName, 'Personal Alias');

      // ==================== PHASE 3: UPDATE - Test Updates ====================
      
      // Update private data
      retrievedPrivateData1.blockUser();
      retrievedPrivateData1.updateDmId('dm_work_updated');
      userPrivateDataBox.put(retrievedPrivateData1);
      
      retrievedPrivateData2.enableNotifications();
      retrievedPrivateData2.updateAliasName('Updated Personal Alias');
      userPrivateDataBox.put(retrievedPrivateData2);
      
      // Verify updates
      final finalPrivateData1 = userPrivateDataBox.get(privateDataIds[0])!;
      expect(finalPrivateData1.blocked, true);
      expect(finalPrivateData1.dmId, 'dm_work_updated');
      expect(finalPrivateData1.version, 2); // Incremented twice
      
      final finalPrivateData2 = userPrivateDataBox.get(privateDataIds[1])!;
      expect(finalPrivateData2.notificationStatus, true);
      expect(finalPrivateData2.aliasName, 'Updated Personal Alias');
      expect(finalPrivateData2.version, 2); // Incremented twice

      // ==================== PHASE 4: DELETE - Test Deletion Scenarios ====================
      
      // Delete one private data record
      userPrivateDataBox.remove(privateDataIds[1]);
      
      // Verify deletion
      expect(userPrivateDataBox.get(privateDataIds[1]), isNull);
      expect(userPrivateDataBox.get(privateDataIds[0]), isNotNull);
      
      // Verify user relationship updated
      final finalUser = userBox.get(userId)!;
      expect(finalUser.privateDataRecords.length, 1);
      expect(finalUser.privateDataRecords.first.source, 'work');

      // ==================== PHASE 5: FINAL VERIFICATION ====================
      
      expect(userPrivateDataBox.count(), 1);
      expect(userBox.count(), 1);
      expect(sessionBox.count(), 1);
      
      print('✅ UserPrivateData Comprehensive Integration Test PASSED - All UserPrivateData-User relationships working correctly');
    });
  });
}
