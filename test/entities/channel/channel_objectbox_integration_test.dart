import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/channel.dart';
import 'package:data_router/src/data/database/entities/channel_private_data.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<Channel> channelBox;
  late Box<ChannelPrivateData> channelPrivateDataBox;
  late Box<Session> sessionBox;
  late Box<User> userBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir = Directory.systemTemp.createTempSync('channel_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    channelBox = store.box<Channel>();
    channelPrivateDataBox = store.box<ChannelPrivateData>();
    sessionBox = store.box<Session>();
    userBox = store.box<User>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    channelBox.removeAll();
    channelPrivateDataBox.removeAll();
    sessionBox.removeAll();
    userBox.removeAll();
  });

  group('🔗 ObjectBox Channel Entities Relationships Integration Tests', () {
    test(
        'should create Channel with triple ToOne relationships to Session and Users',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'channel_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final ownerUser = User.create(
        sessionKey: 'channel_session_001',
        userId: 'owner_001',
        username: 'channel_owner',
      );
      final ownerUserId = userBox.put(ownerUser);

      final recipientUser = User.create(
        sessionKey: 'channel_session_001',
        userId: 'recipient_001',
        username: 'channel_recipient',
      );
      final recipientUserId = userBox.put(recipientUser);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'channel_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'recipient_001',
        name: 'Test Channel',
        channelTypeRaw: 0,
        dmChannelId: 'dm_001',
      );

      // Set relationships
      channel.session.target = session;
      channel.ownerUser.target = ownerUser;
      channel.recipientUser.target = recipientUser;

      // Act
      final channelId = channelBox.put(channel);
      final retrievedChannel = channelBox.get(channelId)!;

      // Assert
      expect(retrievedChannel.workspaceId, 'workspace_001');
      expect(retrievedChannel.channelId, 'channel_001');
      expect(retrievedChannel.sessionKey, 'channel_session_001');
      expect(retrievedChannel.channelOwnerUserId, 'owner_001');
      expect(retrievedChannel.recipientId, 'recipient_001');
      expect(retrievedChannel.name, 'Test Channel');
      expect(retrievedChannel.channelTypeRaw, 0);
      expect(retrievedChannel.dmChannelId, 'dm_001');
      expect(retrievedChannel.isDM, true);

      // Verify ToOne relationships
      expect(retrievedChannel.session.target, isNotNull);
      expect(
          retrievedChannel.session.target!.sessionKey, 'channel_session_001');
      expect(retrievedChannel.hasSessionRelationship, true);
      expect(retrievedChannel.relatedSessionId, 'session_001');

      expect(retrievedChannel.ownerUser.target, isNotNull);
      expect(retrievedChannel.ownerUser.target!.userId, 'owner_001');
      expect(retrievedChannel.hasOwnerRelationship, true);
      expect(retrievedChannel.ownerUsername, 'channel_owner');

      expect(retrievedChannel.recipientUser.target, isNotNull);
      expect(retrievedChannel.recipientUser.target!.userId, 'recipient_001');
      expect(retrievedChannel.hasRecipientRelationship, true);
      expect(retrievedChannel.recipientUsername, 'channel_recipient');

      // Verify helper methods
      expect(retrievedChannel.effectiveOwnerName, 'channel_owner');
      expect(retrievedChannel.effectiveRecipientName, 'channel_recipient');
      expect(retrievedChannel.channelTypeDescription, 'Text Channel');
      expect(retrievedChannel.effectiveChannelName, 'Test Channel');
      expect(retrievedChannel.channelStatusDescription, 'Active');
    });

    test('should create ChannelPrivateData with ToOne relationship to Channel',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'private_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final ownerUser = User.create(
        sessionKey: 'private_session_001',
        userId: 'owner_001',
        username: 'channel_owner',
      );
      final ownerUserId = userBox.put(ownerUser);

      final recipientUser = User.create(
        sessionKey: 'private_session_001',
        userId: 'recipient_001',
        username: 'channel_recipient',
      );
      final recipientUserId = userBox.put(recipientUser);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'private_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'recipient_001',
        name: 'Private Channel',
        channelTypeRaw: 2,
      );
      channel.session.target = session;
      channel.ownerUser.target = ownerUser;
      channel.recipientUser.target = recipientUser;
      final channelId = channelBox.put(channel);

      final privateData = ChannelPrivateData.create(
        sessionKey: 'private_session_001',
        channelId: 'channel_001',
        unreadCount: 5,
        pinned: true,
        sort: 1,
        source: 'mobile',
      );

      // Set relationship
      privateData.channel.target = channel;

      // Act
      final privateDataId = channelPrivateDataBox.put(privateData);
      final retrievedPrivateData = channelPrivateDataBox.get(privateDataId)!;

      // Assert
      expect(retrievedPrivateData.sessionKey, 'private_session_001');
      expect(retrievedPrivateData.channelId, 'channel_001');
      expect(retrievedPrivateData.unreadCount, 5);
      expect(retrievedPrivateData.pinned, true);
      expect(retrievedPrivateData.sort, 1);
      expect(retrievedPrivateData.source, 'mobile');
      expect(retrievedPrivateData.hasUnreadMessages, true);

      // Verify ToOne relationship
      expect(retrievedPrivateData.channel.target, isNotNull);
      expect(retrievedPrivateData.channel.target!.channelId, 'channel_001');
      expect(retrievedPrivateData.hasChannelRelationship, true);
      expect(
          retrievedPrivateData.relatedChannelSessionKey, 'private_session_001');
      expect(retrievedPrivateData.relatedChannelWorkspaceId, 'workspace_001');
      expect(retrievedPrivateData.relatedChannelName, 'Private Channel');

      // Verify helper methods
      expect(
          retrievedPrivateData.relatedEffectiveChannelName, 'Private Channel');
      expect(retrievedPrivateData.relatedChannelTypeDescription, 'DM Channel');
      expect(retrievedPrivateData.isRelatedChannelDM,
          false); // dmChannelId is empty
      expect(retrievedPrivateData.unreadStatusDescription, '5 unread messages');
      expect(retrievedPrivateData.pinStatusDescription, 'Pinned');
      expect(retrievedPrivateData.isPrioritized, true);
    });

    test('should create Channel with backlink to ChannelPrivateData (1:1)', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final ownerUser = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'owner_001',
        username: 'channel_owner',
      );
      final ownerUserId = userBox.put(ownerUser);

      final recipientUser = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'recipient_001',
        username: 'channel_recipient',
      );
      final recipientUserId = userBox.put(recipientUser);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'backlink_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'recipient_001',
        name: 'Backlink Channel',
        isArchived: true,
      );
      channel.session.target = session;
      channel.ownerUser.target = ownerUser;
      channel.recipientUser.target = recipientUser;
      final channelId = channelBox.put(channel);

      // Create one private data record (1:1 relationship)
      final privateData = ChannelPrivateData.create(
        sessionKey: 'backlink_session_001',
        channelId: 'channel_001',
        source: 'mobile',
        version: 1,
        unreadCount: 3,
        pinned: true,
      );
      privateData.channel.target = channel;

      // Act
      channelPrivateDataBox.put(privateData);

      final retrievedChannel = channelBox.get(channelId)!;

      // Assert
      expect(retrievedChannel.channelId, 'channel_001');
      expect(retrievedChannel.name, 'Backlink Channel');
      expect(retrievedChannel.isArchived, true);
      expect(retrievedChannel.channelStatusDescription, 'Archived');

      // Verify backlink relationship (simulated 1:1 using ToMany)
      expect(retrievedChannel.privateDataRecords.length, 1);
      expect(retrievedChannel.hasPrivateData, true);
      expect(retrievedChannel.privateDataVersion, greaterThan(0));
      expect(retrievedChannel.privateDataUnreadCount, 3);
      expect(retrievedChannel.isPinned, true);

      // Verify private data properties
      expect(retrievedChannel.privateDataRecords.first.source, 'mobile');
      expect(retrievedChannel.privateDataRecords.first.unreadCount, 3);
      expect(retrievedChannel.privateDataRecords.first.pinned, true);
    });

    test('should create User with backlinks to Channel as owner and recipient',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'user_backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final user1 = User.create(
        sessionKey: 'user_backlink_session_001',
        userId: 'user_001',
        username: 'user1',
      );
      final user1Id = userBox.put(user1);

      final user2 = User.create(
        sessionKey: 'user_backlink_session_001',
        userId: 'user_002',
        username: 'user2',
      );
      final user2Id = userBox.put(user2);

      // Create channels where user1 is owner
      final channel1 = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'user_backlink_session_001',
        channelOwnerUserId: 'user_001',
        recipientId: 'user_002',
        name: 'Channel 1',
        channelTypeRaw: 0,
      );
      channel1.session.target = session;
      channel1.ownerUser.target = user1;
      channel1.recipientUser.target = user2;

      // Create channels where user1 is recipient
      final channel2 = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_002',
        sessionKey: 'user_backlink_session_001',
        channelOwnerUserId: 'user_002',
        recipientId: 'user_001',
        name: 'Channel 2',
        channelTypeRaw: 2,
        dmChannelId: 'dm_002',
      );
      channel2.session.target = session;
      channel2.ownerUser.target = user2;
      channel2.recipientUser.target = user1;

      // Act
      channelBox.put(channel1);
      channelBox.put(channel2);

      final retrievedUser1 = userBox.get(user1Id)!;
      final retrievedUser2 = userBox.get(user2Id)!;

      // Assert
      expect(retrievedUser1.userId, 'user_001');
      expect(retrievedUser1.username, 'user1');

      // Verify user1 backlink relationships
      expect(retrievedUser1.ownedChannels.length, 1);
      expect(retrievedUser1.recipientChannels.length, 1);
      expect(retrievedUser1.ownedChannelsCount, 1);
      expect(retrievedUser1.recipientChannelsCount, 1);
      expect(retrievedUser1.totalChannelsCount, 2);
      expect(retrievedUser1.hasChannels, true);

      // Verify channel types
      expect(retrievedUser1.dmChannels.length, 1);
      expect(retrievedUser1.ownedNonDMChannels.length, 1);
      expect(retrievedUser1.activeChannels.length, 2);
      expect(retrievedUser1.archivedChannels.length, 0);
      expect(retrievedUser1.dmChannelsCount, 1);
      expect(retrievedUser1.hasDMChannels, true);
      expect(retrievedUser1.hasArchivedChannels, false);

      // Verify user2 backlink relationships
      expect(retrievedUser2.ownedChannels.length, 1);
      expect(retrievedUser2.recipientChannels.length, 1);
      expect(retrievedUser2.totalChannelsCount, 2);

      // Test channel filtering methods
      final textChannels = retrievedUser1.getChannelsByType(0);
      expect(textChannels.length, 1);
      expect(textChannels.first.name, 'Channel 1');

      final workspaceChannels =
          retrievedUser1.getChannelsByWorkspace('workspace_001');
      expect(workspaceChannels.length, 2);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'delete_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final ownerUser = User.create(
        sessionKey: 'delete_session_001',
        userId: 'owner_001',
        username: 'channel_owner',
      );
      final ownerUserId = userBox.put(ownerUser);

      final recipientUser = User.create(
        sessionKey: 'delete_session_001',
        userId: 'recipient_001',
        username: 'channel_recipient',
      );
      final recipientUserId = userBox.put(recipientUser);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'delete_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'recipient_001',
      );
      channel.session.target = session;
      channel.ownerUser.target = ownerUser;
      channel.recipientUser.target = recipientUser;
      final channelId = channelBox.put(channel);

      final privateData = ChannelPrivateData.create(
        sessionKey: 'delete_session_001',
        channelId: 'channel_001',
      );
      privateData.channel.target = channel;
      final privateDataId = channelPrivateDataBox.put(privateData);

      // Act - Delete channel
      channelBox.remove(channelId);

      // Assert
      expect(channelBox.get(channelId), isNull);

      // Verify private data still exists but relationship is orphaned
      final retrievedPrivateData = channelPrivateDataBox.get(privateDataId);
      expect(retrievedPrivateData, isNotNull);
      expect(retrievedPrivateData!.channelId,
          'channel_001'); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedPrivateData.channel.target, isNull);
      expect(retrievedPrivateData.hasChannelRelationship, false);
      expect(retrievedPrivateData.relatedChannelSessionKey, '');

      // Verify users still exist but backlinks are updated
      final retrievedOwnerUser = userBox.get(ownerUserId)!;
      final retrievedRecipientUser = userBox.get(recipientUserId)!;
      expect(retrievedOwnerUser.ownedChannels.length, 0);
      expect(retrievedRecipientUser.recipientChannels.length, 0);
    });
  });

  group('🔄 Channel Comprehensive Integration Tests', () {
    test('Complete Channel Entities Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Complex Channel Scenario ====================

      // Create session
      final session = Session.create(
        sessionKey: 'comprehensive_channel_session_001',
        sessionId: 'comprehensive_session_001',
        sessionToken: 'comprehensive_token_001',
      );
      sessionBox.put(session);

      // Create users
      final ownerUser = User.create(
        sessionKey: 'comprehensive_channel_session_001',
        userId: 'comprehensive_owner_001',
        username: 'comprehensiveowner',
      );
      userBox.put(ownerUser);

      final recipientUser = User.create(
        sessionKey: 'comprehensive_channel_session_001',
        userId: 'comprehensive_recipient_001',
        username: 'comprehensiverecipient',
      );
      userBox.put(recipientUser);

      // Create channels
      final textChannel = Channel.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'text_channel_001',
        sessionKey: 'comprehensive_channel_session_001',
        channelOwnerUserId: 'comprehensive_owner_001',
        recipientId: 'comprehensive_recipient_001',
        name: 'General Discussion',
        channelTypeRaw: 0,
        topic: 'General chat for everyone',
        description: 'Main channel for team discussions',
      );

      final dmChannel = Channel.create(
        workspaceId: 'comprehensive_workspace_001',
        channelId: 'dm_channel_001',
        sessionKey: 'comprehensive_channel_session_001',
        channelOwnerUserId: 'comprehensive_owner_001',
        recipientId: 'comprehensive_recipient_001',
        name: '',
        channelTypeRaw: 2,
        dmChannelId: 'dm_001',
      );

      // Set relationships
      textChannel.session.target = session;
      textChannel.ownerUser.target = ownerUser;
      textChannel.recipientUser.target = recipientUser;

      dmChannel.session.target = session;
      dmChannel.ownerUser.target = ownerUser;
      dmChannel.recipientUser.target = recipientUser;

      final channelIds = [
        channelBox.put(textChannel),
        channelBox.put(dmChannel),
      ];

      // Create private data for channels
      final textPrivateData = ChannelPrivateData.create(
        sessionKey: 'comprehensive_channel_session_001',
        channelId: 'text_channel_001',
        unreadCount: 10,
        pinned: true,
        sort: 1,
        source: 'mobile',
      );
      textPrivateData.channel.target = textChannel;

      final dmPrivateData = ChannelPrivateData.create(
        sessionKey: 'comprehensive_channel_session_001',
        channelId: 'dm_channel_001',
        unreadCount: 0,
        pinned: false,
        sort: 2,
        source: 'web',
      );
      dmPrivateData.channel.target = dmChannel;

      final privateDataIds = [
        channelPrivateDataBox.put(textPrivateData),
        channelPrivateDataBox.put(dmPrivateData),
      ];

      // ==================== PHASE 2: READ - Verify All Relationships ====================

      // Verify channels
      final retrievedTextChannel = channelBox.get(channelIds[0])!;
      final retrievedDMChannel = channelBox.get(channelIds[1])!;

      // Verify text channel relationships
      expect(retrievedTextChannel.hasSessionRelationship, true);
      expect(retrievedTextChannel.hasOwnerRelationship, true);
      expect(retrievedTextChannel.hasRecipientRelationship, true);
      expect(
          retrievedTextChannel.relatedSessionToken, 'comprehensive_token_001');
      expect(retrievedTextChannel.ownerUsername, 'comprehensiveowner');
      expect(retrievedTextChannel.recipientUsername, 'comprehensiverecipient');
      expect(retrievedTextChannel.channelTypeDescription, 'Text Channel');
      expect(retrievedTextChannel.effectiveChannelName, 'General Discussion');
      expect(retrievedTextChannel.isDM, false);

      // Verify DM channel relationships
      expect(retrievedDMChannel.hasSessionRelationship, true);
      expect(retrievedDMChannel.channelTypeDescription, 'DM Channel');
      expect(retrievedDMChannel.isDM, true);
      expect(retrievedDMChannel.effectiveChannelName, 'comprehensiverecipient');

      // Verify private data relationships
      final retrievedTextPrivateData =
          channelPrivateDataBox.get(privateDataIds[0])!;
      final retrievedDMPrivateData =
          channelPrivateDataBox.get(privateDataIds[1])!;

      expect(retrievedTextPrivateData.hasChannelRelationship, true);
      expect(retrievedTextPrivateData.relatedEffectiveChannelName,
          'General Discussion');
      expect(retrievedTextPrivateData.hasUnreadMessages, true);
      expect(retrievedTextPrivateData.isPrioritized, true);

      expect(retrievedDMPrivateData.hasChannelRelationship, true);
      expect(retrievedDMPrivateData.isRelatedChannelDM, true);
      expect(retrievedDMPrivateData.hasUnreadMessages, false);

      // ==================== PHASE 3: UPDATE - Test Updates ====================

      // Archive text channel
      retrievedTextChannel.archiveChannel();
      channelBox.put(retrievedTextChannel);

      // Update DM channel name
      final updatedDMChannel =
          retrievedDMChannel.copyWith(name: 'Private Chat');
      channelBox.put(updatedDMChannel);

      // Update private data
      retrievedTextPrivateData.markAllAsRead();
      channelPrivateDataBox.put(retrievedTextPrivateData);

      // Increment unread count for DM channel (3 times)
      retrievedDMPrivateData.incrementUnreadCount();
      retrievedDMPrivateData.incrementUnreadCount();
      retrievedDMPrivateData.incrementUnreadCount();
      channelPrivateDataBox.put(retrievedDMPrivateData);

      // Verify updates
      final finalTextChannel = channelBox.get(channelIds[0])!;
      expect(finalTextChannel.isArchived, true);
      expect(finalTextChannel.channelStatusDescription, 'Archived');

      final finalDMChannel = channelBox.get(channelIds[1])!;
      expect(finalDMChannel.name, 'Private Chat');
      expect(finalDMChannel.effectiveChannelName, 'Private Chat');

      final finalTextPrivateData =
          channelPrivateDataBox.get(privateDataIds[0])!;
      expect(finalTextPrivateData.unreadCount, 0);
      expect(finalTextPrivateData.hasUnreadMessages, false);

      final finalDMPrivateData = channelPrivateDataBox.get(privateDataIds[1])!;
      expect(finalDMPrivateData.unreadCount, 3);
      expect(finalDMPrivateData.hasUnreadMessages, true);

      // ==================== PHASE 4: DELETE - Test Deletion Scenarios ====================

      // Delete text channel
      channelBox.remove(channelIds[0]);

      // Verify deletion
      expect(channelBox.get(channelIds[0]), isNull);

      // ==================== PHASE 5: FINAL VERIFICATION ====================

      expect(channelBox.count(), 1);
      expect(channelPrivateDataBox.count(), 2); // Private data still exists
      expect(sessionBox.count(), 1);
      expect(userBox.count(), 2);

      print(
          '✅ Channel Comprehensive Integration Test PASSED - All channel entity relationships working correctly');
    });
  });
}
