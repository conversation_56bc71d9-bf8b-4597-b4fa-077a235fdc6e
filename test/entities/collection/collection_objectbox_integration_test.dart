import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/collection.dart';
import 'package:data_router/src/data/database/entities/sticker.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<Collection> collectionBox;
  late Box<Sticker> stickerBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir =
        Directory.systemTemp.createTempSync('collection_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    collectionBox = store.box<Collection>();
    stickerBox = store.box<Sticker>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    collectionBox.removeAll();
    stickerBox.removeAll();
  });

  group('🔗 ObjectBox Collection Relationships Integration Tests', () {
    test('should create Collection with ToMany relationships initialized', () {
      // Arrange
      final collection = Collection.create(
        sessionKey: 'collection_session_001',
        collectionId: 'collection_001',
        name: 'Test Collection',
        description: 'A test sticker collection',
        isPremium: false,
        stickerCount: 0,
      );

      // Act
      final collectionId = collectionBox.put(collection);
      final retrievedCollection = collectionBox.get(collectionId)!;

      // Assert
      expect(retrievedCollection.sessionKey, 'collection_session_001');
      expect(retrievedCollection.collectionId, 'collection_001');
      expect(retrievedCollection.name, 'Test Collection');
      expect(retrievedCollection.description, 'A test sticker collection');
      expect(retrievedCollection.isPremium, false);
      expect(retrievedCollection.stickerCount, 0);
      expect(retrievedCollection.hasName, true);
      expect(retrievedCollection.hasDescription, true);
      expect(retrievedCollection.hasStickers, false);

      // Verify ToMany relationships are initialized
      expect(retrievedCollection.stickers.length, 0);
      expect(retrievedCollection.actualStickerCount, 0);
      expect(retrievedCollection.hasStickersRelationship, false);
    });

    test('should create Sticker with ToOne relationship to Collection', () {
      // Arrange
      final collection = Collection.create(
        sessionKey: 'collection_session_001',
        collectionId: 'collection_001',
        name: 'Test Collection',
      );
      final collectionId = collectionBox.put(collection);

      final sticker = Sticker.create(
        sessionKey: 'collection_session_001',
        stickerId: 'sticker_001',
        collectionId: 'collection_001',
        name: 'Test Sticker',
        url: 'https://example.com/sticker.png',
        width: 100,
        height: 100,
        isAnimated: false,
        frameCount: 1,
      );

      // Set relationship
      sticker.collection.target = collection;

      // Act
      final stickerId = stickerBox.put(sticker);
      final retrievedSticker = stickerBox.get(stickerId)!;

      // Assert
      expect(retrievedSticker.sessionKey, 'collection_session_001');
      expect(retrievedSticker.stickerId, 'sticker_001');
      expect(retrievedSticker.collectionId, 'collection_001');
      expect(retrievedSticker.name, 'Test Sticker');
      expect(retrievedSticker.hasUrl, true);
      expect(retrievedSticker.hasDimensions, true);
      expect(retrievedSticker.isAnimated, false);
      expect(retrievedSticker.aspectRatio, 1.0);

      // Verify ToOne relationship
      expect(retrievedSticker.collection.target, isNotNull);
      expect(
        retrievedSticker.collection.target!.collectionId,
        'collection_001',
      );
      expect(retrievedSticker.hasCollectionRelationship, true);
      expect(retrievedSticker.collectionName, 'Test Collection');
    });

    test('should handle multiple stickers in one collection correctly', () {
      // Arrange
      final collection = Collection.create(
        sessionKey: 'multi_session_001',
        collectionId: 'multi_collection_001',
        name: 'Multi Sticker Collection',
        stickerCount: 0,
      );
      final collectionId = collectionBox.put(collection);

      final sticker1 = Sticker.create(
        sessionKey: 'multi_session_001',
        stickerId: 'sticker_001',
        collectionId: 'multi_collection_001',
        name: 'Sticker 1',
        isAnimated: false,
        frameCount: 1,
      );

      final sticker2 = Sticker.create(
        sessionKey: 'multi_session_001',
        stickerId: 'sticker_002',
        collectionId: 'multi_collection_001',
        name: 'Sticker 2',
        isAnimated: true,
        frameCount: 10,
      );

      final sticker3 = Sticker.create(
        sessionKey: 'multi_session_001',
        stickerId: 'sticker_003',
        collectionId: 'multi_collection_001',
        name: 'Sticker 3',
        isAnimated: false,
        frameCount: 1,
      );

      // Set relationships
      sticker1.collection.target = collection;
      sticker2.collection.target = collection;
      sticker3.collection.target = collection;

      // Act
      final stickerIds = stickerBox.putMany([sticker1, sticker2, sticker3]);

      // Update collection sticker count
      final retrievedCollection = collectionBox.get(collectionId)!;
      retrievedCollection.setStickerCount(3);
      collectionBox.put(retrievedCollection);

      // Assert
      expect(stickerIds.length, 3);

      final updatedCollection = collectionBox.get(collectionId)!;
      expect(updatedCollection.stickerCount, 3);
      expect(updatedCollection.hasStickers, true);

      // Verify all stickers belong to the same collection
      final allStickers = stickerBox.getAll();
      expect(allStickers.length, 3);
      for (final sticker in allStickers) {
        expect(sticker.collectionId, 'multi_collection_001');
        expect(sticker.sessionKey, 'multi_session_001');
      }

      // Verify ToMany relationship
      expect(updatedCollection.stickers.length, 3);
      expect(updatedCollection.actualStickerCount, 3);
      expect(updatedCollection.hasStickersRelationship, true);
      expect(updatedCollection.isStickerCountSynced, true);

      // Test relationship helper methods
      expect(updatedCollection.animatedStickers.length, 1);
      expect(updatedCollection.staticStickers.length, 2);
      expect(updatedCollection.getStickersByName('sticker').length, 3);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final collection = Collection.create(
        sessionKey: 'delete_session_001',
        collectionId: 'delete_collection_001',
        name: 'Delete Test Collection',
      );
      final collectionId = collectionBox.put(collection);

      final sticker = Sticker.create(
        sessionKey: 'delete_session_001',
        stickerId: 'delete_sticker_001',
        collectionId: 'delete_collection_001',
        name: 'Delete Test Sticker',
      );
      // Set relationship
      sticker.collection.target = collection;
      final stickerId = stickerBox.put(sticker);

      // Act - Delete collection
      collectionBox.remove(collectionId);

      // Assert
      expect(collectionBox.get(collectionId), isNull);

      // Verify sticker still exists but relationship is orphaned
      final retrievedSticker = stickerBox.get(stickerId);
      expect(retrievedSticker, isNotNull);
      expect(
        retrievedSticker!.collectionId,
        'delete_collection_001',
      ); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedSticker.collection.target, isNull);
      expect(retrievedSticker.hasCollectionRelationship, false);
      expect(retrievedSticker.collectionName, '');
    });
  });

  group('🔄 Collection Comprehensive Integration Tests', () {
    test('Complete Collection-Sticker Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Collection with Multiple Stickers ====================

      // Create collection
      final collection = Collection.create(
        sessionKey: 'comprehensive_session_001',
        collectionId: 'comprehensive_collection_001',
        name: 'Comprehensive Test Collection',
        description: 'A comprehensive test collection with various stickers',
        isPremium: true,
        stickerCount: 0,
      );
      final collectionId = collectionBox.put(collection);

      // Create various types of stickers
      final staticSticker = Sticker.create(
        sessionKey: 'comprehensive_session_001',
        stickerId: 'static_sticker_001',
        collectionId: 'comprehensive_collection_001',
        name: 'Static Sticker',
        url: 'https://example.com/static.png',
        thumbnailUrl: 'https://example.com/static_thumb.png',
        width: 128,
        height: 128,
        isAnimated: false,
        frameCount: 1,
      );

      final animatedSticker = Sticker.create(
        sessionKey: 'comprehensive_session_001',
        stickerId: 'animated_sticker_001',
        collectionId: 'comprehensive_collection_001',
        name: 'Animated Sticker',
        url: 'https://example.com/animated.gif',
        thumbnailUrl: 'https://example.com/animated_thumb.png',
        width: 256,
        height: 256,
        isAnimated: true,
        frameCount: 20,
      );

      final largeSticker = Sticker.create(
        sessionKey: 'comprehensive_session_001',
        stickerId: 'large_sticker_001',
        collectionId: 'comprehensive_collection_001',
        name: 'Large Sticker',
        url: 'https://example.com/large.png',
        width: 512,
        height: 256,
        isAnimated: false,
        frameCount: 1,
      );

      // Set relationships before putting stickers
      staticSticker.collection.target = collection;
      animatedSticker.collection.target = collection;
      largeSticker.collection.target = collection;

      final stickerIds =
          stickerBox.putMany([staticSticker, animatedSticker, largeSticker]);

      // Update collection sticker count
      final retrievedCollection = collectionBox.get(collectionId)!;
      retrievedCollection.setStickerCount(3);
      collectionBox.put(retrievedCollection);

      // ==================== PHASE 2: READ - Verify All Relationships ====================

      final updatedCollection = collectionBox.get(collectionId)!;

      // Verify collection properties
      expect(updatedCollection.collectionId, 'comprehensive_collection_001');
      expect(updatedCollection.name, 'Comprehensive Test Collection');
      expect(updatedCollection.isPremium, true);
      expect(updatedCollection.stickerCount, 3);
      expect(updatedCollection.hasStickers, true);

      // Verify all stickers
      final allStickers = stickerBox.getAll();
      expect(allStickers.length, 3);

      // Verify sticker properties
      final staticStickerRetrieved = stickerBox.get(stickerIds[0])!;
      expect(staticStickerRetrieved.isAnimated, false);
      expect(staticStickerRetrieved.aspectRatio, 1.0);
      expect(staticStickerRetrieved.hasUrl, true);
      expect(staticStickerRetrieved.hasThumbnail, true);

      final animatedStickerRetrieved = stickerBox.get(stickerIds[1])!;
      expect(animatedStickerRetrieved.isAnimated, true);
      expect(animatedStickerRetrieved.frameCount, 20);
      expect(animatedStickerRetrieved.aspectRatio, 1.0);

      final largeStickerRetrieved = stickerBox.get(stickerIds[2])!;
      expect(largeStickerRetrieved.aspectRatio, 2.0); // 512/256
      expect(largeStickerRetrieved.hasDimensions, true);

      // ==================== PHASE 3: UPDATE - Test Updates ====================

      // Update collection info
      updatedCollection.updateInfo(
        name: 'Updated Collection Name',
        description: 'Updated description',
        isPremium: false,
      );
      collectionBox.put(updatedCollection);

      // Update sticker info
      staticStickerRetrieved.updateInfo(
        name: 'Updated Static Sticker',
        width: 256,
        height: 256,
      );
      stickerBox.put(staticStickerRetrieved);

      // Verify updates
      final finalCollection = collectionBox.get(collectionId)!;
      expect(finalCollection.name, 'Updated Collection Name');
      expect(finalCollection.isPremium, false);

      final finalSticker = stickerBox.get(stickerIds[0])!;
      expect(finalSticker.name, 'Updated Static Sticker');
      expect(finalSticker.width, 256);
      expect(finalSticker.aspectRatio, 1.0);

      // ==================== PHASE 4: DELETE - Test Deletion Scenarios ====================

      // Delete one sticker
      stickerBox.remove(stickerIds[0]);
      finalCollection.decrementStickerCount();
      collectionBox.put(finalCollection);

      // Verify partial deletion
      expect(stickerBox.count(), 2);
      expect(finalCollection.stickerCount, 2);

      // Delete collection
      collectionBox.remove(collectionId);

      // Verify collection is deleted but stickers remain
      expect(collectionBox.get(collectionId), isNull);
      expect(stickerBox.count(), 2);

      // Verify orphaned stickers still have collectionId field
      final orphanedStickers = stickerBox.getAll();
      for (final sticker in orphanedStickers) {
        expect(sticker.collectionId, 'comprehensive_collection_001');
      }

      // ==================== PHASE 5: FINAL VERIFICATION ====================

      expect(collectionBox.count(), 0);
      expect(stickerBox.count(), 2);

      print(
        '✅ Collection Comprehensive Integration Test PASSED - All Collection-Sticker relationships working correctly',
      );
    });
  });
}
