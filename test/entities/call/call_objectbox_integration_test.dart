import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/call_log.dart';
import 'package:data_router/src/data/database/entities/call_log_private_data.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<CallLog> callLogBox;
  late Box<CallLogPrivateData> callLogPrivateDataBox;
  late Box<Session> sessionBox;
  late Box<User> userBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir = Directory.systemTemp.createTempSync('call_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    callLogBox = store.box<CallLog>();
    callLogPrivateDataBox = store.box<CallLogPrivateData>();
    sessionBox = store.box<Session>();
    userBox = store.box<User>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    callLogBox.removeAll();
    callLogPrivateDataBox.removeAll();
    sessionBox.removeAll();
    userBox.removeAll();
  });

  group('🔗 ObjectBox Call Entities Relationships Integration Tests', () {
    test(
        'should create CallLog with triple ToOne relationships to Session and Users',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'call_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final callerUser = User.create(
        sessionKey: 'call_session_001',
        userId: 'caller_001',
        username: 'caller_user',
      );
      final callerUserId = userBox.put(callerUser);

      final calleeUser = User.create(
        sessionKey: 'call_session_001',
        userId: 'callee_001',
        username: 'callee_user',
      );
      final calleeUserId = userBox.put(calleeUser);

      final callLog = CallLog.create(
        sessionKey: 'call_session_001',
        callId: 'call_001',
        callerId: 'caller_001',
        calleeId: 'callee_001',
        isVideoCall: true,
        isInComingCall: true,
        callTimeInSeconds: 120,
      );

      // Set relationships
      callLog.session.target = session;
      callLog.callerUser.target = callerUser;
      callLog.calleeUser.target = calleeUser;

      // Act
      final callLogId = callLogBox.put(callLog);
      final retrievedCallLog = callLogBox.get(callLogId)!;

      // Assert
      expect(retrievedCallLog.sessionKey, 'call_session_001');
      expect(retrievedCallLog.callId, 'call_001');
      expect(retrievedCallLog.callerId, 'caller_001');
      expect(retrievedCallLog.calleeId, 'callee_001');
      expect(retrievedCallLog.isVideoCall, true);
      expect(retrievedCallLog.isInComingCall, true);
      expect(retrievedCallLog.callTimeInSeconds, 120);
      expect(retrievedCallLog.formattedDuration, '02:00');

      // Verify ToOne relationships
      expect(retrievedCallLog.session.target, isNotNull);
      expect(retrievedCallLog.session.target!.sessionKey, 'call_session_001');
      expect(retrievedCallLog.hasSessionRelationship, true);
      expect(retrievedCallLog.relatedSessionId, 'session_001');

      expect(retrievedCallLog.callerUser.target, isNotNull);
      expect(retrievedCallLog.callerUser.target!.userId, 'caller_001');
      expect(retrievedCallLog.hasCallerRelationship, true);
      expect(retrievedCallLog.callerUsername, 'caller_user');

      expect(retrievedCallLog.calleeUser.target, isNotNull);
      expect(retrievedCallLog.calleeUser.target!.userId, 'callee_001');
      expect(retrievedCallLog.hasCalleeRelationship, true);
      expect(retrievedCallLog.calleeUsername, 'callee_user');

      // Verify helper methods
      expect(retrievedCallLog.effectiveCallerName, 'caller_user');
      expect(retrievedCallLog.effectiveCalleeName, 'callee_user');
      expect(retrievedCallLog.callTypeDescription, 'Video Call');
      expect(retrievedCallLog.callDirectionDescription, 'Incoming');
    });

    test('should create CallLogPrivateData with ToOne relationship to CallLog',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'private_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final callerUser = User.create(
        sessionKey: 'private_session_001',
        userId: 'caller_001',
        username: 'caller_user',
      );
      final callerUserId = userBox.put(callerUser);

      final calleeUser = User.create(
        sessionKey: 'private_session_001',
        userId: 'callee_001',
        username: 'callee_user',
      );
      final calleeUserId = userBox.put(calleeUser);

      final callLog = CallLog.create(
        sessionKey: 'private_session_001',
        callId: 'call_001',
        callerId: 'caller_001',
        calleeId: 'callee_001',
        isVideoCall: false,
        isOutgoing: true,
      );
      callLog.session.target = session;
      callLog.callerUser.target = callerUser;
      callLog.calleeUser.target = calleeUser;
      final callLogId = callLogBox.put(callLog);

      final privateData = CallLogPrivateData.create(
        sessionKey: 'private_session_001',
        callId: 'call_001',
        callState: 1,
        source: 'mobile',
        version: 1,
        callType: 0,
      );

      // Set relationship
      privateData.callLog.target = callLog;

      // Act
      final privateDataId = callLogPrivateDataBox.put(privateData);
      final retrievedPrivateData = callLogPrivateDataBox.get(privateDataId)!;

      // Assert
      expect(retrievedPrivateData.sessionKey, 'private_session_001');
      expect(retrievedPrivateData.callId, 'call_001');
      expect(retrievedPrivateData.callState, 1);
      expect(retrievedPrivateData.source, 'mobile');
      expect(retrievedPrivateData.version, 1);
      expect(retrievedPrivateData.callType, 0);

      // Verify ToOne relationship
      expect(retrievedPrivateData.callLog.target, isNotNull);
      expect(retrievedPrivateData.callLog.target!.callId, 'call_001');
      expect(retrievedPrivateData.hasCallLogRelationship, true);
      expect(
          retrievedPrivateData.relatedCallLogSessionKey, 'private_session_001');
      expect(retrievedPrivateData.relatedCallLogCallerId, 'caller_001');
      expect(retrievedPrivateData.relatedCallLogCalleeId, 'callee_001');

      // Verify helper methods
      expect(retrievedPrivateData.relatedCallerUsername, 'caller_user');
      expect(retrievedPrivateData.relatedCalleeUsername, 'callee_user');
      expect(retrievedPrivateData.isRelatedVideoCall, false);
      expect(retrievedPrivateData.isRelatedOutgoingCall, true);
      expect(retrievedPrivateData.relatedCallTypeDescription, 'Voice Call');
      expect(retrievedPrivateData.relatedCallDirectionDescription, 'Outgoing');
    });

    test('should create CallLog with backlink to CallLogPrivateData (1:1)', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final callerUser = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'caller_001',
        username: 'caller_user',
      );
      final callerUserId = userBox.put(callerUser);

      final calleeUser = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'callee_001',
        username: 'callee_user',
      );
      final calleeUserId = userBox.put(calleeUser);

      final callLog = CallLog.create(
        sessionKey: 'backlink_session_001',
        callId: 'call_001',
        callerId: 'caller_001',
        calleeId: 'callee_001',
        isMissedCall: true,
      );
      callLog.session.target = session;
      callLog.callerUser.target = callerUser;
      callLog.calleeUser.target = calleeUser;
      final callLogId = callLogBox.put(callLog);

      // Create one private data record (1:1 relationship)
      final privateData = CallLogPrivateData.create(
        sessionKey: 'backlink_session_001',
        callId: 'call_001',
        source: 'mobile',
        version: 1,
        callState: 0, // Missed
        endedReason: 1,
      );
      privateData.callLog.target = callLog;

      // Act
      callLogPrivateDataBox.put(privateData);

      final retrievedCallLog = callLogBox.get(callLogId)!;

      // Assert
      expect(retrievedCallLog.callId, 'call_001');
      expect(retrievedCallLog.isMissedCall, true);
      expect(retrievedCallLog.callStatusDescription, 'Missed');

      // Verify backlink relationship (simulated 1:1 using ToMany)
      expect(retrievedCallLog.privateDataRecords.length, 1);
      expect(retrievedCallLog.hasPrivateData, true);
      expect(retrievedCallLog.privateDataCallState, 0);
      expect(retrievedCallLog.privateDataEndedReason, 1);
      expect(retrievedCallLog.isPrivateDataRead, false);

      // Verify private data properties
      expect(retrievedCallLog.privateDataRecords.first.source, 'mobile');
      expect(retrievedCallLog.privateDataRecords.first.version, 1);
      expect(retrievedCallLog.privateDataRecords.first.callState, 0);
    });

    test('should create User with backlinks to CallLog as caller and callee',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'user_backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final user1 = User.create(
        sessionKey: 'user_backlink_session_001',
        userId: 'user_001',
        username: 'user1',
      );
      final user1Id = userBox.put(user1);

      final user2 = User.create(
        sessionKey: 'user_backlink_session_001',
        userId: 'user_002',
        username: 'user2',
      );
      final user2Id = userBox.put(user2);

      // Create call logs where user1 is caller
      final callLog1 = CallLog.create(
        sessionKey: 'user_backlink_session_001',
        callId: 'call_001',
        callerId: 'user_001',
        calleeId: 'user_002',
        isVideoCall: true,
        isOutgoing: true,
      );
      callLog1.session.target = session;
      callLog1.callerUser.target = user1;
      callLog1.calleeUser.target = user2;

      // Create call logs where user1 is callee
      final callLog2 = CallLog.create(
        sessionKey: 'user_backlink_session_001',
        callId: 'call_002',
        callerId: 'user_002',
        calleeId: 'user_001',
        isInComingCall: true,
        isMissedCall: true,
      );
      callLog2.session.target = session;
      callLog2.callerUser.target = user2;
      callLog2.calleeUser.target = user1;

      // Act
      callLogBox.put(callLog1);
      callLogBox.put(callLog2);

      final retrievedUser1 = userBox.get(user1Id)!;
      final retrievedUser2 = userBox.get(user2Id)!;

      // Assert
      expect(retrievedUser1.userId, 'user_001');
      expect(retrievedUser1.username, 'user1');

      // Verify user1 backlink relationships
      expect(retrievedUser1.callerCallLogs.length, 1);
      expect(retrievedUser1.calleeCallLogs.length, 1);
      expect(retrievedUser1.callerCallLogsCount, 1);
      expect(retrievedUser1.calleeCallLogsCount, 1);
      expect(retrievedUser1.totalCallLogsCount, 2);
      expect(retrievedUser1.hasCallLogs, true);

      // Verify call log types
      expect(retrievedUser1.videoCallLogs.length, 1);
      expect(retrievedUser1.voiceCallLogs.length, 1);
      expect(retrievedUser1.outgoingCallLogs.length, 1);
      expect(retrievedUser1.incomingCallLogs.length, 1);
      expect(retrievedUser1.missedCallLogs.length, 1);
      expect(retrievedUser1.missedCallsCount, 1);
      expect(retrievedUser1.hasMissedCalls, true);

      // Verify user2 backlink relationships
      expect(retrievedUser2.callerCallLogs.length, 1);
      expect(retrievedUser2.calleeCallLogs.length, 1);
      expect(retrievedUser2.totalCallLogsCount, 2);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'delete_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final callerUser = User.create(
        sessionKey: 'delete_session_001',
        userId: 'caller_001',
        username: 'caller_user',
      );
      final callerUserId = userBox.put(callerUser);

      final calleeUser = User.create(
        sessionKey: 'delete_session_001',
        userId: 'callee_001',
        username: 'callee_user',
      );
      final calleeUserId = userBox.put(calleeUser);

      final callLog = CallLog.create(
        sessionKey: 'delete_session_001',
        callId: 'call_001',
        callerId: 'caller_001',
        calleeId: 'callee_001',
      );
      callLog.session.target = session;
      callLog.callerUser.target = callerUser;
      callLog.calleeUser.target = calleeUser;
      final callLogId = callLogBox.put(callLog);

      final privateData = CallLogPrivateData.create(
        sessionKey: 'delete_session_001',
        callId: 'call_001',
      );
      privateData.callLog.target = callLog;
      final privateDataId = callLogPrivateDataBox.put(privateData);

      // Act - Delete call log
      callLogBox.remove(callLogId);

      // Assert
      expect(callLogBox.get(callLogId), isNull);

      // Verify private data still exists but relationship is orphaned
      final retrievedPrivateData = callLogPrivateDataBox.get(privateDataId);
      expect(retrievedPrivateData, isNotNull);
      expect(retrievedPrivateData!.callId, 'call_001'); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedPrivateData.callLog.target, isNull);
      expect(retrievedPrivateData.hasCallLogRelationship, false);
      expect(retrievedPrivateData.relatedCallLogSessionKey, '');

      // Verify users still exist but backlinks are updated
      final retrievedCallerUser = userBox.get(callerUserId)!;
      final retrievedCalleeUser = userBox.get(calleeUserId)!;
      expect(retrievedCallerUser.callerCallLogs.length, 0);
      expect(retrievedCalleeUser.calleeCallLogs.length, 0);
    });
  });

  group('🔄 Call Comprehensive Integration Tests', () {
    test('Complete Call Entities Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Complex Call Scenario ====================

      // Create session
      final session = Session.create(
        sessionKey: 'comprehensive_call_session_001',
        sessionId: 'comprehensive_session_001',
        sessionToken: 'comprehensive_token_001',
      );
      final sessionId = sessionBox.put(session);

      // Create users
      final callerUser = User.create(
        sessionKey: 'comprehensive_call_session_001',
        userId: 'comprehensive_caller_001',
        username: 'comprehensivecaller',
      );
      final callerUserId = userBox.put(callerUser);

      final calleeUser = User.create(
        sessionKey: 'comprehensive_call_session_001',
        userId: 'comprehensive_callee_001',
        username: 'comprehensivecallee',
      );
      final calleeUserId = userBox.put(calleeUser);

      // Create call logs
      final videoCall = CallLog.create(
        sessionKey: 'comprehensive_call_session_001',
        callId: 'video_call_001',
        callerId: 'comprehensive_caller_001',
        calleeId: 'comprehensive_callee_001',
        isVideoCall: true,
        isOutgoing: true,
        callTimeInSeconds: 300,
      );

      final voiceCall = CallLog.create(
        sessionKey: 'comprehensive_call_session_001',
        callId: 'voice_call_001',
        callerId: 'comprehensive_callee_001',
        calleeId: 'comprehensive_caller_001',
        isVideoCall: false,
        isInComingCall: true,
        isMissedCall: true,
      );

      // Set relationships
      videoCall.session.target = session;
      videoCall.callerUser.target = callerUser;
      videoCall.calleeUser.target = calleeUser;

      voiceCall.session.target = session;
      voiceCall.callerUser.target = calleeUser;
      voiceCall.calleeUser.target = callerUser;

      final videoCallIds = [
        callLogBox.put(videoCall),
        callLogBox.put(voiceCall),
      ];

      // Create private data for calls
      final videoPrivateData = CallLogPrivateData.create(
        sessionKey: 'comprehensive_call_session_001',
        callId: 'video_call_001',
        callState: 2, // Ended
        source: 'mobile',
        version: 1,
        callType: 1, // Video
      );
      videoPrivateData.callLog.target = videoCall;

      final voicePrivateData = CallLogPrivateData.create(
        sessionKey: 'comprehensive_call_session_001',
        callId: 'voice_call_001',
        callState: 0, // Missed
        source: 'web',
        version: 1,
        callType: 0, // Voice
      );
      voicePrivateData.callLog.target = voiceCall;

      final privateDataIds = [
        callLogPrivateDataBox.put(videoPrivateData),
        callLogPrivateDataBox.put(voicePrivateData),
      ];

      // ==================== PHASE 2: READ - Verify All Relationships ====================

      final retrievedSession = sessionBox.get(sessionId)!;
      final retrievedCallerUser = userBox.get(callerUserId)!;
      final retrievedCalleeUser = userBox.get(calleeUserId)!;

      // Verify call logs
      final retrievedVideoCall = callLogBox.get(videoCallIds[0])!;
      final retrievedVoiceCall = callLogBox.get(videoCallIds[1])!;

      // Verify video call relationships
      expect(retrievedVideoCall.hasSessionRelationship, true);
      expect(retrievedVideoCall.hasCallerRelationship, true);
      expect(retrievedVideoCall.hasCalleeRelationship, true);
      expect(retrievedVideoCall.relatedSessionToken, 'comprehensive_token_001');
      expect(retrievedVideoCall.callerUsername, 'comprehensivecaller');
      expect(retrievedVideoCall.calleeUsername, 'comprehensivecallee');
      expect(retrievedVideoCall.callTypeDescription, 'Video Call');
      expect(retrievedVideoCall.callDirectionDescription, 'Outgoing');
      expect(retrievedVideoCall.formattedDuration, '05:00');

      // Verify voice call relationships
      expect(retrievedVoiceCall.hasSessionRelationship, true);
      expect(retrievedVoiceCall.isMissedCall, true);
      expect(retrievedVoiceCall.callStatusDescription, 'Missed');
      expect(retrievedVoiceCall.callTypeDescription, 'Voice Call');
      expect(retrievedVoiceCall.callDirectionDescription, 'Incoming');

      // Verify user relationships
      expect(retrievedCallerUser.callerCallLogs.length, 1);
      expect(retrievedCallerUser.calleeCallLogs.length, 1);
      expect(retrievedCallerUser.totalCallLogsCount, 2);
      expect(retrievedCallerUser.videoCallLogs.length, 1);
      expect(retrievedCallerUser.voiceCallLogs.length, 1);
      expect(retrievedCallerUser.missedCallsCount, 1);
      expect(retrievedCallerUser.hasMissedCalls, true);

      // Verify private data relationships
      final retrievedVideoPrivateData =
          callLogPrivateDataBox.get(privateDataIds[0])!;
      final retrievedVoicePrivateData =
          callLogPrivateDataBox.get(privateDataIds[1])!;

      expect(retrievedVideoPrivateData.hasCallLogRelationship, true);
      expect(retrievedVideoPrivateData.isRelatedVideoCall, true);
      expect(retrievedVideoPrivateData.isRelatedOutgoingCall, true);
      expect(retrievedVideoPrivateData.relatedEffectiveCallerName,
          'comprehensivecaller');

      expect(retrievedVoicePrivateData.hasCallLogRelationship, true);
      expect(retrievedVoicePrivateData.isRelatedVideoCall, false);
      expect(retrievedVoicePrivateData.isRelatedMissedCall, true);

      // ==================== PHASE 3: UPDATE - Test Updates ====================

      // End video call
      retrievedVideoCall.endCall(endedReason: 1, callTimeInSeconds: 350);
      callLogBox.put(retrievedVideoCall);

      // Mark voice call as read
      retrievedVoiceCall.markAsRead();
      callLogBox.put(retrievedVoiceCall);

      // Update private data - sync with updated call log
      final updatedVideoCall = callLogBox.get(videoCallIds[0])!;
      retrievedVideoPrivateData.callLog.target =
          updatedVideoCall; // Update relationship first
      retrievedVideoPrivateData.syncWithCallLog();
      callLogPrivateDataBox.put(retrievedVideoPrivateData);

      retrievedVoicePrivateData.markAsRead();
      callLogPrivateDataBox.put(retrievedVoicePrivateData);

      // Verify updates
      final finalVideoCall = callLogBox.get(videoCallIds[0])!;
      expect(finalVideoCall.isEnded, true);
      expect(finalVideoCall.endedReason, 1);
      expect(finalVideoCall.callTimeInSeconds, 350);
      expect(finalVideoCall.formattedDuration, '05:50');

      final finalVoiceCall = callLogBox.get(videoCallIds[1])!;
      expect(finalVoiceCall.readTime, isNotNull);

      final finalVideoPrivateData =
          callLogPrivateDataBox.get(privateDataIds[0])!;
      expect(finalVideoPrivateData.isSyncedWithCallLog, true);
      expect(finalVideoPrivateData.callTimeInSeconds, 350);

      final finalVoicePrivateData =
          callLogPrivateDataBox.get(privateDataIds[1])!;
      expect(finalVoicePrivateData.isRead, true);
      expect(finalVoicePrivateData.version, 2);

      // ==================== PHASE 4: DELETE - Test Deletion Scenarios ====================

      // Delete voice call
      callLogBox.remove(videoCallIds[1]);

      // Verify deletion
      expect(callLogBox.get(videoCallIds[1]), isNull);

      // Verify user relationship updated
      final finalCallerUser = userBox.get(callerUserId)!;
      expect(finalCallerUser.totalCallLogsCount, 1);
      expect(finalCallerUser.missedCallsCount, 0);
      expect(finalCallerUser.hasMissedCalls, false);

      // ==================== PHASE 5: FINAL VERIFICATION ====================

      expect(callLogBox.count(), 1);
      expect(callLogPrivateDataBox.count(), 2); // Private data still exists
      expect(sessionBox.count(), 1);
      expect(userBox.count(), 2);

      print(
          '✅ Call Comprehensive Integration Test PASSED - All call entity relationships working correctly');
    });
  });
}
