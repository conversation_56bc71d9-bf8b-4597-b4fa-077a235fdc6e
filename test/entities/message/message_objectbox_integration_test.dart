import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/message.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/entities/channel.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/entities/profile.dart';
import 'package:data_router/src/data/database/entities/user_presence.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<Message> messageBox;
  late Box<Session> sessionBox;
  late Box<Channel> channelBox;
  late Box<User> userBox;
  late Box<Profile> profileBox;
  late Box<UserPresence> userPresenceBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir = Directory.systemTemp.createTempSync('message_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    messageBox = store.box<Message>();
    sessionBox = store.box<Session>();
    channelBox = store.box<Channel>();
    userBox = store.box<User>();
    profileBox = store.box<Profile>();
    userPresenceBox = store.box<UserPresence>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    messageBox.removeAll();
    sessionBox.removeAll();
    channelBox.removeAll();
    userBox.removeAll();
    profileBox.removeAll();
    userPresenceBox.removeAll();
  });

  group('🔗 ObjectBox Message Entity Relationships Integration Tests', () {
    test(
        'should create Message with triple ToOne relationships to Session, Channel, and User',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'message_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'message_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'General Channel',
        channelTypeRaw: 0,
      );
      final channelId = channelBox.put(channel);

      final user = User.create(
        sessionKey: 'message_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      // Create profile for user
      final profile = Profile.create(
        sessionKey: 'message_session_001',
        userId: 'user_001',
        displayName: 'Test User',
        avatar: 'avatar.jpg',
      );
      profile.user.target = user;
      profileBox.put(profile);

      // Create presence for user
      final presence = UserPresence.create(
        sessionKey: 'message_session_001',
        userId: 'user_001',
        presenceStatus: 1, // Online
      );
      presence.user.target = user;
      userPresenceBox.put(presence);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'message_session_001',
        userId: 'user_001',
        content: 'Hello, World!',
        messageTypeRaw: 0,
        messageStatusRaw: 1,
        attachmentCount: 2,
        reactionsRaw: '{"like": 5}',
        mentionsRaw: '["user_002"]',
        isPinned: true,
      );

      // Set relationships
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;

      // Act
      final messageId = messageBox.put(message);
      final retrievedMessage = messageBox.get(messageId)!;

      // Assert
      expect(retrievedMessage.workspaceId, 'workspace_001');
      expect(retrievedMessage.channelId, 'channel_001');
      expect(retrievedMessage.messageId, 'message_001');
      expect(retrievedMessage.sessionKey, 'message_session_001');
      expect(retrievedMessage.userId, 'user_001');
      expect(retrievedMessage.content, 'Hello, World!');
      expect(retrievedMessage.messageTypeRaw, 0);
      expect(retrievedMessage.messageStatusRaw, 1);
      expect(retrievedMessage.attachmentCount, 2);
      expect(retrievedMessage.reactionsRaw, '{"like": 5}');
      expect(retrievedMessage.mentionsRaw, '["user_002"]');
      expect(retrievedMessage.isPinned, true);

      // Verify basic properties
      expect(retrievedMessage.hasContent, true);
      expect(retrievedMessage.hasAttachments, true);
      expect(retrievedMessage.hasReactions, true);
      expect(retrievedMessage.hasMentions, true);

      // Verify ToOne relationships
      expect(retrievedMessage.session.target, isNotNull);
      expect(
          retrievedMessage.session.target!.sessionKey, 'message_session_001');
      expect(retrievedMessage.hasSessionRelationship, true);
      expect(retrievedMessage.relatedSessionId, 'session_001');

      expect(retrievedMessage.channel.target, isNotNull);
      expect(retrievedMessage.channel.target!.channelId, 'channel_001');
      expect(retrievedMessage.hasChannelRelationship, true);
      expect(retrievedMessage.relatedChannelName, 'General Channel');

      expect(retrievedMessage.sender.target, isNotNull);
      expect(retrievedMessage.sender.target!.userId, 'user_001');
      expect(retrievedMessage.hasSenderRelationship, true);
      expect(retrievedMessage.relatedSenderUsername, 'testuser');

      // Verify helper methods
      expect(retrievedMessage.messageTypeDescription, 'Text Message');
      expect(retrievedMessage.messageStatusDescription, 'Sent');
      expect(retrievedMessage.attachmentTypeDescription, 'No Attachment');
      expect(retrievedMessage.messageViewTypeDescription, 'Normal');
      expect(retrievedMessage.isSuccessful, true);
      expect(retrievedMessage.isFailed, false);
      expect(retrievedMessage.isPending, false);
      expect(retrievedMessage.isDelivered, false);
      expect(retrievedMessage.isRead, false);

      // Verify relationship helper methods
      expect(retrievedMessage.relatedSenderDisplayName, 'Test User');
      expect(retrievedMessage.relatedEffectiveSenderName, 'Test User');
      expect(retrievedMessage.isSenderOnline, true);
      expect(retrievedMessage.relatedChannelTypeDescription, 'Text Channel');
      expect(retrievedMessage.shortContent, 'Hello, World!');
    });

    test('should create Channel with backlink to Message', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'backlink_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Chat Channel',
        channelTypeRaw: 0,
      );
      final channelId = channelBox.put(channel);

      final user = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
        username: 'user1',
      );
      userBox.put(user);

      // Create multiple messages for the channel
      final message1 = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
        content: 'First message',
        messageTypeRaw: 0,
        messageStatusRaw: 1,
        isPinned: true,
      );
      message1.session.target = session;
      message1.channel.target = channel;
      message1.sender.target = user;

      final message2 = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_002',
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
        content: 'Second message',
        messageTypeRaw: 0,
        messageStatusRaw: 4, // Failed
        attachmentCount: 1,
      );
      message2.session.target = session;
      message2.channel.target = channel;
      message2.sender.target = user;

      // Act
      messageBox.put(message1);
      messageBox.put(message2);

      final retrievedChannel = channelBox.get(channelId)!;

      // Assert
      expect(retrievedChannel.channelId, 'channel_001');
      expect(retrievedChannel.name, 'Chat Channel');

      // Verify backlink relationship
      expect(retrievedChannel.messages.length, 2);
      expect(retrievedChannel.messagesCount, 2);
      expect(retrievedChannel.hasMessages, true);

      // Verify message filtering
      expect(retrievedChannel.pinnedMessages.length, 1);
      expect(retrievedChannel.failedMessages.length, 1);
      expect(retrievedChannel.successfulMessages.length, 1);
      expect(retrievedChannel.messagesWithAttachments.length, 1);
      expect(retrievedChannel.pinnedMessagesCount, 1);
      expect(retrievedChannel.failedMessagesCount, 1);
      expect(retrievedChannel.hasPinnedMessages, true);
      expect(retrievedChannel.hasFailedMessages, true);

      // Verify message queries
      final foundMessage = retrievedChannel.getMessageById('message_001');
      expect(foundMessage, isNotNull);
      expect(foundMessage!.content, 'First message');

      final userMessages = retrievedChannel.getMessagesBySender('user_001');
      expect(userMessages.length, 2);

      final textMessages = retrievedChannel.getMessagesByType(0);
      expect(textMessages.length, 2);

      final failedMessages = retrievedChannel.getMessagesByStatus(4);
      expect(failedMessages.length, 1);
    });

    test('should create User with backlink to Message (sentMessages)', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'user_backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final user = User.create(
        sessionKey: 'user_backlink_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      final channel1 = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'user_backlink_session_001',
        channelOwnerUserId: 'user_001',
        recipientId: 'user_002',
        name: 'Channel 1',
      );
      channelBox.put(channel1);

      final channel2 = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_002',
        sessionKey: 'user_backlink_session_001',
        channelOwnerUserId: 'other_user',
        recipientId: 'user_001',
        name: 'Channel 2',
      );
      channelBox.put(channel2);

      // Create multiple messages sent by the user
      final message1 = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'user_backlink_session_001',
        userId: 'user_001',
        content: 'Message in channel 1',
        messageTypeRaw: 0,
        messageStatusRaw: 1,
        isPinned: true,
      );
      message1.session.target = session;
      message1.channel.target = channel1;
      message1.sender.target = user;

      final message2 = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_002',
        messageId: 'message_002',
        sessionKey: 'user_backlink_session_001',
        userId: 'user_001',
        content: 'Message in channel 2',
        messageTypeRaw: 0,
        messageStatusRaw: 4, // Failed
        attachmentCount: 1,
      );
      message2.session.target = session;
      message2.channel.target = channel2;
      message2.sender.target = user;

      // Act
      messageBox.put(message1);
      messageBox.put(message2);

      final retrievedUser = userBox.get(userId)!;

      // Assert
      expect(retrievedUser.userId, 'user_001');
      expect(retrievedUser.username, 'testuser');

      // Verify backlink relationship
      expect(retrievedUser.sentMessages.length, 2);
      expect(retrievedUser.sentMessagesCount, 2);
      expect(retrievedUser.hasSentMessages, true);

      // Verify message filtering
      expect(retrievedUser.successfulSentMessages.length, 1);
      expect(retrievedUser.failedSentMessages.length, 1);
      expect(retrievedUser.pendingSentMessages.length, 0);
      expect(retrievedUser.pinnedSentMessages.length, 1);
      expect(retrievedUser.sentMessagesWithAttachments.length, 1);

      // Verify message counts
      expect(retrievedUser.successfulSentMessagesCount, 1);
      expect(retrievedUser.failedSentMessagesCount, 1);
      expect(retrievedUser.pendingSentMessagesCount, 0);
      expect(retrievedUser.sentMessagesWithAttachmentsCount, 1);
      expect(retrievedUser.hasFailedSentMessages, true);
      expect(retrievedUser.hasPendingSentMessages, false);
      expect(retrievedUser.hasPinnedSentMessages, true);

      // Verify message queries
      final channel1Messages =
          retrievedUser.getSentMessagesInChannel('channel_001');
      expect(channel1Messages.length, 1);
      expect(channel1Messages.first.content, 'Message in channel 1');

      final textMessages = retrievedUser.getSentMessagesByType(0);
      expect(textMessages.length, 2);

      final failedMessages = retrievedUser.getSentMessagesByStatus(4);
      expect(failedMessages.length, 1);

      final workspaceMessages =
          retrievedUser.getSentMessagesByWorkspace('workspace_001');
      expect(workspaceMessages.length, 2);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'deletion_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'deletion_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Test Channel',
      );
      final channelId = channelBox.put(channel);

      final user = User.create(
        sessionKey: 'deletion_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      final userId = userBox.put(user);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'deletion_session_001',
        userId: 'user_001',
        content: 'Test message',
      );
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      final messageId = messageBox.put(message);

      // Verify relationships exist
      expect(messageBox.get(messageId)!.hasSessionRelationship, true);
      expect(messageBox.get(messageId)!.hasChannelRelationship, true);
      expect(messageBox.get(messageId)!.hasSenderRelationship, true);

      // Act - Delete related entities
      sessionBox.remove(sessionId);
      channelBox.remove(channelId);
      userBox.remove(userId);

      // Assert - Message should still exist but relationships should be null
      final retrievedMessage = messageBox.get(messageId)!;
      expect(retrievedMessage.hasSessionRelationship, false);
      expect(retrievedMessage.hasChannelRelationship, false);
      expect(retrievedMessage.hasSenderRelationship, false);

      // Verify fallback to field values
      expect(retrievedMessage.sessionKey, 'deletion_session_001');
      expect(retrievedMessage.channelId, 'channel_001');
      expect(retrievedMessage.userId, 'user_001');
    });
  });

  group('🔄 Message Comprehensive Integration Tests', () {
    test('Complete Message Entity Relationship Lifecycle', () {
      // ==================== PHASE 1: SETUP - Create Related Entities ====================

      final session = Session.create(
        sessionKey: 'comprehensive_message_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      // Create multiple channels
      final generalChannel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'general_channel_001',
        sessionKey: 'comprehensive_message_session_001',
        channelOwnerUserId: 'admin_001',
        recipientId: 'user_001',
        name: 'General',
        channelTypeRaw: 0,
      );
      generalChannel.session.target = session;
      channelBox.put(generalChannel);

      final devChannel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'dev_channel_001',
        sessionKey: 'comprehensive_message_session_001',
        channelOwnerUserId: 'admin_001',
        recipientId: 'dev_001',
        name: 'Development',
        channelTypeRaw: 0,
      );
      devChannel.session.target = session;
      channelBox.put(devChannel);

      // Create multiple users
      final adminUser = User.create(
        sessionKey: 'comprehensive_message_session_001',
        userId: 'admin_001',
        username: 'admin',
      );
      adminUser.session.target = session;
      userBox.put(adminUser);

      final devUser = User.create(
        sessionKey: 'comprehensive_message_session_001',
        userId: 'dev_001',
        username: 'developer',
      );
      devUser.session.target = session;
      userBox.put(devUser);

      final regularUser = User.create(
        sessionKey: 'comprehensive_message_session_001',
        userId: 'user_001',
        username: 'regularuser',
      );
      regularUser.session.target = session;
      userBox.put(regularUser);

      // Create profiles for users
      final adminProfile = Profile.create(
        sessionKey: 'comprehensive_message_session_001',
        userId: 'admin_001',
        displayName: 'Admin User',
        avatar: 'admin_avatar.jpg',
      );
      adminProfile.user.target = adminUser;
      profileBox.put(adminProfile);

      final devProfile = Profile.create(
        sessionKey: 'comprehensive_message_session_001',
        userId: 'dev_001',
        displayName: 'Dev User',
        avatar: 'dev_avatar.jpg',
      );
      devProfile.user.target = devUser;
      profileBox.put(devProfile);

      // Create presences for users
      final adminPresence = UserPresence.create(
        sessionKey: 'comprehensive_message_session_001',
        userId: 'admin_001',
        presenceStatus: 1, // Online
      );
      adminPresence.user.target = adminUser;
      userPresenceBox.put(adminPresence);

      final devPresence = UserPresence.create(
        sessionKey: 'comprehensive_message_session_001',
        userId: 'dev_001',
        presenceStatus: 0, // Offline
      );
      devPresence.user.target = devUser;
      userPresenceBox.put(devPresence);

      // ==================== PHASE 2: CREATE - Test Message Creation ====================

      // Create various types of messages
      final textMessage = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'general_channel_001',
        messageId: 'text_message_001',
        sessionKey: 'comprehensive_message_session_001',
        userId: 'admin_001',
        content: 'Welcome to the general channel!',
        messageTypeRaw: 0, // Text
        messageStatusRaw: 3, // Read
        createTime: DateTime.now().subtract(Duration(hours: 2)),
      );
      textMessage.session.target = session;
      textMessage.channel.target = generalChannel;
      textMessage.sender.target = adminUser;

      final imageMessage = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'general_channel_001',
        messageId: 'image_message_001',
        sessionKey: 'comprehensive_message_session_001',
        userId: 'dev_001',
        content: 'Check out this screenshot',
        messageTypeRaw: 1, // Image
        messageStatusRaw: 2, // Delivered
        attachmentTypeRaw: 1, // Image
        attachmentCount: 1,
        reactionsRaw: '{"like": 3, "love": 1}',
        isPinned: true,
        createTime: DateTime.now().subtract(Duration(hours: 1)),
      );
      imageMessage.session.target = session;
      imageMessage.channel.target = generalChannel;
      imageMessage.sender.target = devUser;

      final failedMessage = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'dev_channel_001',
        messageId: 'failed_message_001',
        sessionKey: 'comprehensive_message_session_001',
        userId: 'user_001',
        content: 'This message failed to send',
        messageTypeRaw: 0, // Text
        messageStatusRaw: 4, // Failed
        messageErrorReasonRaw: 1,
        createTime: DateTime.now().subtract(Duration(minutes: 30)),
      );
      failedMessage.session.target = session;
      failedMessage.channel.target = devChannel;
      failedMessage.sender.target = regularUser;

      final pendingMessage = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'dev_channel_001',
        messageId: 'pending_message_001',
        sessionKey: 'comprehensive_message_session_001',
        userId: 'dev_001',
        content: 'Sending...',
        messageTypeRaw: 0, // Text
        messageStatusRaw: 0, // Pending
        isTemp: true,
        createTime: DateTime.now(),
      );
      pendingMessage.session.target = session;
      pendingMessage.channel.target = devChannel;
      pendingMessage.sender.target = devUser;

      // Act
      final messageIds = [
        messageBox.put(textMessage),
        messageBox.put(imageMessage),
        messageBox.put(failedMessage),
        messageBox.put(pendingMessage),
      ];

      // Assert - Verify message creation
      expect(messageBox.count(), 4);

      final retrievedTextMessage = messageBox.get(messageIds[0])!;
      final retrievedImageMessage = messageBox.get(messageIds[1])!;
      final retrievedFailedMessage = messageBox.get(messageIds[2])!;
      final retrievedPendingMessage = messageBox.get(messageIds[3])!;

      // Verify text message
      expect(retrievedTextMessage.messageTypeDescription, 'Text Message');
      expect(retrievedTextMessage.messageStatusDescription, 'Read');
      expect(retrievedTextMessage.isSuccessful, true);
      expect(retrievedTextMessage.isRead, true);
      expect(retrievedTextMessage.relatedSenderDisplayName, 'Admin User');
      expect(retrievedTextMessage.relatedEffectiveSenderName, 'Admin User');
      expect(retrievedTextMessage.isSenderOnline, true);

      // Verify image message
      expect(retrievedImageMessage.messageTypeDescription, 'Image Message');
      expect(retrievedImageMessage.messageStatusDescription, 'Delivered');
      expect(retrievedImageMessage.attachmentTypeDescription, 'Image');
      expect(retrievedImageMessage.hasAttachments, true);
      expect(retrievedImageMessage.hasReactions, true);
      expect(retrievedImageMessage.isPinned, true);
      expect(retrievedImageMessage.isDelivered, true);
      expect(retrievedImageMessage.relatedSenderDisplayName, 'Dev User');
      expect(retrievedImageMessage.isSenderOnline, false);

      // Verify failed message
      expect(retrievedFailedMessage.messageStatusDescription, 'Failed');
      expect(retrievedFailedMessage.isFailed, true);
      expect(retrievedFailedMessage.isSuccessful, false);
      expect(retrievedFailedMessage.relatedEffectiveSenderName,
          'regularuser'); // No profile

      // Verify pending message
      expect(retrievedPendingMessage.messageStatusDescription, 'Pending');
      expect(retrievedPendingMessage.isPending, true);
      expect(retrievedPendingMessage.isTemp, true);
      expect(retrievedPendingMessage.isRecent, true);

      print(
          '✅ Message Comprehensive Integration Test PASSED - All message entity relationships working correctly');
    });
  });
}
