import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/translated_result.dart';
import 'package:data_router/src/data/database/entities/message.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/entities/channel.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/entities/profile.dart';
import 'package:data_router/src/data/database/entities/user_presence.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<TranslatedResult> translatedResultBox;
  late Box<Message> messageBox;
  late Box<Session> sessionBox;
  late Box<Channel> channelBox;
  late Box<User> userBox;
  late Box<Profile> profileBox;
  late Box<UserPresence> userPresenceBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir =
        Directory.systemTemp.createTempSync('translated_result_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    translatedResultBox = store.box<TranslatedResult>();
    messageBox = store.box<Message>();
    sessionBox = store.box<Session>();
    channelBox = store.box<Channel>();
    userBox = store.box<User>();
    profileBox = store.box<Profile>();
    userPresenceBox = store.box<UserPresence>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    translatedResultBox.removeAll();
    messageBox.removeAll();
    sessionBox.removeAll();
    channelBox.removeAll();
    userBox.removeAll();
    profileBox.removeAll();
    userPresenceBox.removeAll();
  });

  group('🔗 ObjectBox TranslatedResult Entity Relationships Integration Tests',
      () {
    test(
        'should create TranslatedResult with ToOne relationship to Message (1:1)',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'translation_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'translation_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'International Channel',
        channelTypeRaw: 0,
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'translation_session_001',
        userId: 'user_001',
        username: 'translator',
      );
      userBox.put(user);

      // Create profile for user
      final profile = Profile.create(
        sessionKey: 'translation_session_001',
        userId: 'user_001',
        displayName: 'Translator User',
        avatar: 'translator_avatar.jpg',
      );
      profile.user.target = user;
      profileBox.put(profile);

      // Create presence for user
      final presence = UserPresence.create(
        sessionKey: 'translation_session_001',
        userId: 'user_001',
        presenceStatus: 1, // Online
      );
      presence.user.target = user;
      userPresenceBox.put(presence);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'translation_session_001',
        userId: 'user_001',
        content: 'Hello, how are you?',
        messageTypeRaw: 0, // Text message
        messageStatusRaw: 1, // Sent
      );

      // Set message relationships
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      messageBox.put(message);

      final translatedResult = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'translation_session_001',
        messageId: 'message_001',
        originalContent: 'Hello, how are you?',
        translatedContent: 'Xin chào, bạn khỏe không?',
        originalLanguage: 'en',
        targetLanguage: 'vi',
        statusRaw: 1, // Completed
        isShowTranslateResult: true,
      );

      // Set relationship
      translatedResult.message.target = message;

      // Act
      final translatedResultId = translatedResultBox.put(translatedResult);
      final retrievedTranslatedResult =
          translatedResultBox.get(translatedResultId)!;

      // Assert
      expect(retrievedTranslatedResult.workspaceId, 'workspace_001');
      expect(retrievedTranslatedResult.channelId, 'channel_001');
      expect(retrievedTranslatedResult.sessionKey, 'translation_session_001');
      expect(retrievedTranslatedResult.messageId, 'message_001');
      expect(retrievedTranslatedResult.originalContent, 'Hello, how are you?');
      expect(retrievedTranslatedResult.translatedContent,
          'Xin chào, bạn khỏe không?');
      expect(retrievedTranslatedResult.originalLanguage, 'en');
      expect(retrievedTranslatedResult.targetLanguage, 'vi');
      expect(retrievedTranslatedResult.statusRaw, 1);
      expect(retrievedTranslatedResult.isShowTranslateResult, true);

      // Verify basic properties
      expect(retrievedTranslatedResult.isCompleted, true);
      expect(retrievedTranslatedResult.isInProgress, false);
      expect(retrievedTranslatedResult.isFailed, false);
      expect(retrievedTranslatedResult.hasTranslation, true);
      expect(retrievedTranslatedResult.hasOriginalContent, true);
      expect(retrievedTranslatedResult.hasBothContents, true);
      expect(retrievedTranslatedResult.hasLanguageInfo, true);
      expect(retrievedTranslatedResult.isDifferentLanguage, true);

      // Verify ToOne relationship
      expect(retrievedTranslatedResult.message.target, isNotNull);
      expect(
          retrievedTranslatedResult.message.target!.messageId, 'message_001');
      expect(retrievedTranslatedResult.hasMessageRelationship, true);

      // Verify helper methods
      expect(
          retrievedTranslatedResult.translationStatusDescription, 'Completed');
      expect(retrievedTranslatedResult.languagePairDescription, 'en → vi');
      expect(retrievedTranslatedResult.isSuccessful, true);
      expect(retrievedTranslatedResult.shouldShowTranslation, true);

      // Verify relationship helper methods
      expect(retrievedTranslatedResult.relatedMessageContent,
          'Hello, how are you?');
      expect(retrievedTranslatedResult.relatedMessageType, 0);
      expect(retrievedTranslatedResult.relatedMessageStatus, 1);
      expect(retrievedTranslatedResult.relatedMessageChannelId, 'channel_001');
      expect(retrievedTranslatedResult.relatedMessageSenderId, 'user_001');
      expect(retrievedTranslatedResult.relatedMessageSessionKey,
          'translation_session_001');
      expect(retrievedTranslatedResult.isRelatedMessageSuccessful, true);
      expect(
          retrievedTranslatedResult.relatedMessageSenderUsername, 'translator');
      expect(retrievedTranslatedResult.relatedMessageSenderDisplayName,
          'Translator User');
      expect(retrievedTranslatedResult.isRelatedMessageSenderOnline, true);

      // Verify quality metrics
      expect(
          retrievedTranslatedResult.translationQualityScore, greaterThan(0.0));
      expect(retrievedTranslatedResult.contentLengthDifference, greaterThan(0));
      expect(retrievedTranslatedResult.shortTranslatedContent,
          'Xin chào, bạn khỏe không?');
      expect(retrievedTranslatedResult.shortOriginalContent,
          'Hello, how are you?');
    });

    test(
        'should create Message with ToOne relationship to TranslatedResult (1:1)',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'message_translation_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'message_translation_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Multilingual Channel',
        channelTypeRaw: 0,
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'message_translation_session_001',
        userId: 'user_001',
        username: 'multiuser',
      );
      userBox.put(user);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'message_translation_session_001',
        userId: 'user_001',
        content: 'Bonjour, comment allez-vous?',
        messageTypeRaw: 0, // Text message
        messageStatusRaw: 1, // Sent
        isPinned: true,
      );

      // Set message relationships
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      final messageId = messageBox.put(message);

      final translatedResult = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'message_translation_session_001',
        messageId: 'message_001',
        originalContent: 'Bonjour, comment allez-vous?',
        translatedContent: 'Hello, how are you?',
        originalLanguage: 'fr',
        targetLanguage: 'en',
        statusRaw: 2, // Cached
        isShowTranslateResult: true,
      );

      // Set relationship
      translatedResult.message.target = message;
      translatedResultBox.put(translatedResult);

      // Set reverse relationship
      message.translatedResult.target = translatedResult;
      messageBox.put(message);

      // Act
      final retrievedMessage = messageBox.get(messageId)!;

      // Assert
      expect(retrievedMessage.messageId, 'message_001');
      expect(retrievedMessage.content, 'Bonjour, comment allez-vous?');

      // Verify ToOne relationship
      expect(retrievedMessage.translatedResult.target, isNotNull);
      expect(
          retrievedMessage.translatedResult.target!.messageId, 'message_001');
      expect(retrievedMessage.hasTranslationResult, true);

      // Verify translation helper methods
      expect(retrievedMessage.hasCompletedTranslation,
          false); // Status 2 is cached, not completed
      expect(retrievedMessage.hasSuccessfulTranslation,
          true); // Cached is considered successful
      expect(retrievedMessage.isTranslationInProgress, false);
      expect(retrievedMessage.hasTranslationFailed, false);
      expect(retrievedMessage.hasTranslationCached, true);
      expect(retrievedMessage.shouldShowTranslation, true);
      expect(retrievedMessage.translatedContent, 'Hello, how are you?');
      expect(retrievedMessage.originalContentForTranslation,
          'Bonjour, comment allez-vous?');
      expect(retrievedMessage.translationStatusDescription, 'Cached');
      expect(retrievedMessage.translationLanguagePair, 'fr → en');
      expect(retrievedMessage.hasGoodTranslationQuality,
          greaterThanOrEqualTo(false));
      expect(retrievedMessage.shortTranslatedContent, 'Hello, how are you?');
      expect(retrievedMessage.isTranslationFromDifferentLanguage, true);
      expect(retrievedMessage.needsTranslation, false);
      expect(retrievedMessage.canBeTranslated, false);
      expect(retrievedMessage.isTranslationAvailable, true);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'deletion_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'deletion_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Test Channel',
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'deletion_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      userBox.put(user);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'deletion_session_001',
        userId: 'user_001',
        content: 'Test message',
      );
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      final messageId = messageBox.put(message);

      final translatedResult = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'deletion_session_001',
        messageId: 'message_001',
        originalContent: 'Test message',
        translatedContent: 'Tin nhắn thử nghiệm',
        originalLanguage: 'en',
        targetLanguage: 'vi',
        statusRaw: 1,
      );
      translatedResult.message.target = message;
      final translatedResultId = translatedResultBox.put(translatedResult);

      // Verify relationship exists
      expect(
          translatedResultBox.get(translatedResultId)!.hasMessageRelationship,
          true);

      // Act - Delete message
      messageBox.remove(messageId);

      // Assert - TranslatedResult should still exist but relationship should be null
      final retrievedTranslatedResult =
          translatedResultBox.get(translatedResultId)!;
      expect(retrievedTranslatedResult.hasMessageRelationship, false);

      // Verify fallback to field value
      expect(retrievedTranslatedResult.messageId, 'message_001');
    });

    test('should handle various translation statuses correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'status_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'status_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Status Channel',
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'status_session_001',
        userId: 'user_001',
        username: 'statususer',
      );
      userBox.put(user);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'status_session_001',
        userId: 'user_001',
        content: 'Message with various translation statuses',
      );
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      messageBox.put(message);

      // Create translation results with different statuses
      final inProgressTranslation = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'status_session_001',
        messageId: 'message_001',
        originalContent: 'In progress translation',
        translatedContent: '',
        originalLanguage: 'en',
        targetLanguage: 'es',
        statusRaw: 0, // In progress
      );
      inProgressTranslation.message.target = message;

      final completedTranslation = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'status_session_001',
        messageId: 'message_001',
        originalContent: 'Completed translation',
        translatedContent: 'Traducción completada',
        originalLanguage: 'en',
        targetLanguage: 'es',
        statusRaw: 1, // Completed
        isShowTranslateResult: true,
      );
      completedTranslation.message.target = message;

      final failedTranslation = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'status_session_001',
        messageId: 'message_001',
        originalContent: 'Failed translation',
        translatedContent: '',
        originalLanguage: 'en',
        targetLanguage: 'zh',
        statusRaw: -1, // Failed
      );
      failedTranslation.message.target = message;

      final cachedTranslation = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'status_session_001',
        messageId: 'message_001',
        originalContent: 'Cached translation',
        translatedContent: 'Traduction mise en cache',
        originalLanguage: 'en',
        targetLanguage: 'fr',
        statusRaw: 2, // Cached
        isShowTranslateResult: true,
      );
      cachedTranslation.message.target = message;

      // Act
      final inProgressId = translatedResultBox.put(inProgressTranslation);
      final completedId = translatedResultBox.put(completedTranslation);
      final failedId = translatedResultBox.put(failedTranslation);
      final cachedId = translatedResultBox.put(cachedTranslation);

      // Assert
      final retrievedInProgress = translatedResultBox.get(inProgressId)!;
      final retrievedCompleted = translatedResultBox.get(completedId)!;
      final retrievedFailed = translatedResultBox.get(failedId)!;
      final retrievedCached = translatedResultBox.get(cachedId)!;

      // Verify in progress translation
      expect(retrievedInProgress.isInProgress, true);
      expect(retrievedInProgress.isCompleted, false);
      expect(retrievedInProgress.isFailed, false);
      expect(retrievedInProgress.isCached, false);
      expect(retrievedInProgress.isSuccessful, false);
      expect(retrievedInProgress.translationStatusDescription, 'In Progress');
      expect(retrievedInProgress.hasTranslation, false);
      expect(retrievedInProgress.shouldShowTranslation, false);

      // Verify completed translation
      expect(retrievedCompleted.isCompleted, true);
      expect(retrievedCompleted.isInProgress, false);
      expect(retrievedCompleted.isFailed, false);
      expect(retrievedCompleted.isCached, false);
      expect(retrievedCompleted.isSuccessful, true);
      expect(retrievedCompleted.translationStatusDescription, 'Completed');
      expect(retrievedCompleted.hasTranslation, true);
      expect(retrievedCompleted.shouldShowTranslation, true);

      // Verify failed translation
      expect(retrievedFailed.isFailed, true);
      expect(retrievedFailed.isCompleted, false);
      expect(retrievedFailed.isInProgress, false);
      expect(retrievedFailed.isCached, false);
      expect(retrievedFailed.isSuccessful, false);
      expect(retrievedFailed.translationStatusDescription, 'Failed');
      expect(retrievedFailed.hasTranslation, false);
      expect(retrievedFailed.shouldShowTranslation, false);

      // Verify cached translation
      expect(retrievedCached.isCached, true);
      expect(retrievedCached.isCompleted, false);
      expect(retrievedCached.isInProgress, false);
      expect(retrievedCached.isFailed, false);
      expect(retrievedCached.isSuccessful, true);
      expect(retrievedCached.translationStatusDescription, 'Cached');
      expect(retrievedCached.hasTranslation, true);
      expect(retrievedCached.shouldShowTranslation, true);
    });
  });

  group('🔄 TranslatedResult Comprehensive Integration Tests', () {
    test('Complete TranslatedResult Entity Relationship Lifecycle', () {
      // ==================== PHASE 1: SETUP - Create Related Entities ====================

      final session = Session.create(
        sessionKey: 'comprehensive_translation_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'translation_channel_001',
        sessionKey: 'comprehensive_translation_session_001',
        channelOwnerUserId: 'admin_001',
        recipientId: 'user_001',
        name: 'Global Communication',
        channelTypeRaw: 0,
      );
      channel.session.target = session;
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'comprehensive_translation_session_001',
        userId: 'user_001',
        username: 'globaluser',
      );
      user.session.target = session;
      userBox.put(user);

      // Create profile for user
      final profile = Profile.create(
        sessionKey: 'comprehensive_translation_session_001',
        userId: 'user_001',
        displayName: 'Global User',
        avatar: 'global_avatar.jpg',
      );
      profile.user.target = user;
      profileBox.put(profile);

      // Create presence for user
      final presence = UserPresence.create(
        sessionKey: 'comprehensive_translation_session_001',
        userId: 'user_001',
        presenceStatus: 1, // Online
      );
      presence.user.target = user;
      userPresenceBox.put(presence);

      // ==================== PHASE 2: CREATE - Test Message and Translation Creation ====================

      final internationalMessage = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'translation_channel_001',
        messageId: 'international_message_001',
        sessionKey: 'comprehensive_translation_session_001',
        userId: 'user_001',
        content: 'こんにちは、元気ですか？今日は良い天気ですね。',
        messageTypeRaw: 0, // Text message
        messageStatusRaw: 1, // Sent
        createTime: DateTime.now().subtract(Duration(hours: 2)),
        isPinned: true,
      );
      internationalMessage.session.target = session;
      internationalMessage.channel.target = channel;
      internationalMessage.sender.target = user;
      messageBox.put(internationalMessage);

      // Create high-quality translation result
      final highQualityTranslation = TranslatedResult.create(
        workspaceId: 'workspace_001',
        channelId: 'translation_channel_001',
        sessionKey: 'comprehensive_translation_session_001',
        messageId: 'international_message_001',
        originalContent: 'こんにちは、元気ですか？今日は良い天気ですね。',
        translatedContent: 'Hello, how are you? The weather is nice today.',
        originalLanguage: 'ja',
        targetLanguage: 'en',
        statusRaw: 1, // Completed
        isShowTranslateResult: true,
      );
      highQualityTranslation.message.target = internationalMessage;

      // Set bidirectional relationship
      internationalMessage.translatedResult.target = highQualityTranslation;

      // Act
      final translationId = translatedResultBox.put(highQualityTranslation);
      messageBox.put(
          internationalMessage); // Update message with translation relationship

      // Assert - Verify translation creation
      expect(translatedResultBox.count(), 1);

      final retrievedTranslation = translatedResultBox.get(translationId)!;

      // Verify translation properties
      expect(retrievedTranslation.workspaceId, 'workspace_001');
      expect(retrievedTranslation.channelId, 'translation_channel_001');
      expect(retrievedTranslation.sessionKey,
          'comprehensive_translation_session_001');
      expect(retrievedTranslation.messageId, 'international_message_001');
      expect(retrievedTranslation.originalLanguage, 'ja');
      expect(retrievedTranslation.targetLanguage, 'en');
      expect(retrievedTranslation.isCompleted, true);
      expect(retrievedTranslation.isSuccessful, true);
      expect(retrievedTranslation.hasTranslation, true);
      expect(retrievedTranslation.hasOriginalContent, true);
      expect(retrievedTranslation.hasBothContents, true);
      expect(retrievedTranslation.hasLanguageInfo, true);
      expect(retrievedTranslation.isDifferentLanguage, true);
      expect(retrievedTranslation.shouldShowTranslation, true);
      expect(retrievedTranslation.languagePairDescription, 'ja → en');
      expect(retrievedTranslation.translationStatusDescription, 'Completed');

      // Verify quality metrics
      expect(retrievedTranslation.translationQualityScore,
          greaterThanOrEqualTo(0.0));
      expect(retrievedTranslation.contentLengthDifference,
          greaterThanOrEqualTo(0));
      // Translation length comparison depends on actual content lengths
      expect(retrievedTranslation.isTranslationShorter, false);

      // Verify relationship information
      expect(retrievedTranslation.hasMessageRelationship, true);
      expect(retrievedTranslation.relatedMessageContent,
          'こんにちは、元気ですか？今日は良い天気ですね。');
      expect(retrievedTranslation.relatedMessageType, 0);
      expect(retrievedTranslation.relatedMessageStatus, 1);
      expect(retrievedTranslation.isRelatedMessageSuccessful, true);
      expect(retrievedTranslation.isRelatedMessagePinned, true);
      expect(retrievedTranslation.relatedMessageSenderUsername, 'globaluser');
      expect(
          retrievedTranslation.relatedMessageSenderDisplayName, 'Global User');
      expect(retrievedTranslation.isRelatedMessageSenderOnline, true);

      // Verify message translation integration
      final retrievedMessage = messageBox.query().build().findFirst()!;
      expect(retrievedMessage.hasTranslationResult, true);
      expect(retrievedMessage.hasCompletedTranslation, true);
      expect(retrievedMessage.hasSuccessfulTranslation, true);
      expect(retrievedMessage.shouldShowTranslation, true);
      expect(retrievedMessage.translatedContent,
          'Hello, how are you? The weather is nice today.');
      expect(retrievedMessage.originalContentForTranslation,
          'こんにちは、元気ですか？今日は良い天気ですね。');
      expect(retrievedMessage.translationLanguagePair, 'ja → en');
      expect(retrievedMessage.isTranslationFromDifferentLanguage, true);
      expect(retrievedMessage.isTranslationAvailable, true);
      expect(retrievedMessage.needsTranslation, false);
      expect(retrievedMessage.canBeTranslated, false);

      print(
          '✅ TranslatedResult Comprehensive Integration Test PASSED - All translation entity relationships working correctly');
    });
  });
}
