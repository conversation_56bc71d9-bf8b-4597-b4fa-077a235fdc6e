import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/sticker.dart';
import 'package:data_router/src/data/database/entities/sticker_frame_count.dart';
import 'package:data_router/src/data/database/entities/collection.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<Sticker> stickerBox;
  late Box<StickerFrameCount> stickerFrameCountBox;
  late Box<Collection> collectionBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir = Directory.systemTemp.createTempSync('sticker_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    stickerBox = store.box<Sticker>();
    stickerFrameCountBox = store.box<StickerFrameCount>();
    collectionBox = store.box<Collection>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    stickerBox.removeAll();
    stickerFrameCountBox.removeAll();
    collectionBox.removeAll();
  });

  group(
      '🔗 ObjectBox Sticker-StickerFrameCount Relationships Integration Tests',
      () {
    test('should create Sticker with ToOne relationship to StickerFrameCount',
        () {
      // Arrange
      final frameCount = StickerFrameCount.create(
        sessionKey: 'sticker_session_001',
        stickerId: 'sticker_001',
        frameCount: 10,
      );
      final frameCountId = stickerFrameCountBox.put(frameCount);

      final sticker = Sticker.create(
        sessionKey: 'sticker_session_001',
        stickerId: 'sticker_001',
        collectionId: 'collection_001',
        name: 'Animated Sticker',
        isAnimated: true,
        frameCount: 10,
      );

      // Set relationship
      sticker.frameCountData.target = frameCount;

      // Act
      final stickerId = stickerBox.put(sticker);
      final retrievedSticker = stickerBox.get(stickerId)!;

      // Assert
      expect(retrievedSticker.sessionKey, 'sticker_session_001');
      expect(retrievedSticker.stickerId, 'sticker_001');
      expect(retrievedSticker.isAnimated, true);
      expect(retrievedSticker.frameCount, 10);

      // Verify ToOne relationship
      expect(retrievedSticker.frameCountData.target, isNotNull);
      expect(retrievedSticker.frameCountData.target!.frameCount, 10);
      expect(retrievedSticker.hasFrameCountData, true);
      expect(retrievedSticker.effectiveFrameCount, 10);
      expect(retrievedSticker.isFrameCountSynced, true);
    });

    test('should create StickerFrameCount with backlink to Sticker', () {
      // Arrange
      final sticker = Sticker.create(
        sessionKey: 'frame_session_001',
        stickerId: 'frame_sticker_001',
        collectionId: 'collection_001',
        name: 'Frame Test Sticker',
        isAnimated: true,
        frameCount: 15,
      );
      final stickerId = stickerBox.put(sticker);

      final frameCount = StickerFrameCount.create(
        sessionKey: 'frame_session_001',
        stickerId: 'frame_sticker_001',
        frameCount: 15,
      );

      // Set relationship
      frameCount.sticker.target = sticker;

      // Act
      final frameCountId = stickerFrameCountBox.put(frameCount);
      final retrievedFrameCount = stickerFrameCountBox.get(frameCountId)!;

      // Assert
      expect(retrievedFrameCount.sessionKey, 'frame_session_001');
      expect(retrievedFrameCount.stickerId, 'frame_sticker_001');
      expect(retrievedFrameCount.frameCount, 15);
      expect(retrievedFrameCount.isAnimated, true);
      expect(retrievedFrameCount.isStatic, false);

      // Verify backlink relationship
      expect(retrievedFrameCount.sticker.target, isNotNull);
      expect(
        retrievedFrameCount.sticker.target!.stickerId,
        'frame_sticker_001',
      );
      expect(retrievedFrameCount.hasStickerRelationship, true);
      expect(retrievedFrameCount.stickerName, 'Frame Test Sticker');
    });

    test('should handle frame count updates correctly', () {
      // Arrange
      final sticker = Sticker.create(
        sessionKey: 'update_session_001',
        stickerId: 'update_sticker_001',
        collectionId: 'collection_001',
        name: 'Update Test Sticker',
        isAnimated: false,
        frameCount: 1,
      );
      final stickerId = stickerBox.put(sticker);

      final frameCount = StickerFrameCount.create(
        sessionKey: 'update_session_001',
        stickerId: 'update_sticker_001',
        frameCount: 1,
      );
      final frameCountId = stickerFrameCountBox.put(frameCount);

      // Act - Update frame count
      final retrievedFrameCount = stickerFrameCountBox.get(frameCountId)!;
      retrievedFrameCount.updateFrameCount(20);
      stickerFrameCountBox.put(retrievedFrameCount);

      final retrievedSticker = stickerBox.get(stickerId)!;
      retrievedSticker.updateFrameCount(20);
      stickerBox.put(retrievedSticker);

      // Assert
      final finalFrameCount = stickerFrameCountBox.get(frameCountId)!;
      expect(finalFrameCount.frameCount, 20);
      expect(finalFrameCount.isAnimated, true);

      final finalSticker = stickerBox.get(stickerId)!;
      expect(finalSticker.frameCount, 20);
      expect(finalSticker.isAnimated, true);
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final sticker = Sticker.create(
        sessionKey: 'delete_session_001',
        stickerId: 'delete_sticker_001',
        collectionId: 'collection_001',
        name: 'Delete Test Sticker',
        frameCount: 5,
      );
      final stickerId = stickerBox.put(sticker);

      final frameCount = StickerFrameCount.create(
        sessionKey: 'delete_session_001',
        stickerId: 'delete_sticker_001',
        frameCount: 5,
      );
      // Set relationship
      frameCount.sticker.target = sticker;
      final frameCountId = stickerFrameCountBox.put(frameCount);

      // Act - Delete sticker
      stickerBox.remove(stickerId);

      // Assert
      expect(stickerBox.get(stickerId), isNull);

      // Verify frame count still exists but relationship is orphaned
      final retrievedFrameCount = stickerFrameCountBox.get(frameCountId);
      expect(retrievedFrameCount, isNotNull);
      expect(
        retrievedFrameCount!.stickerId,
        'delete_sticker_001',
      ); // Field still has value

      // Verify ToOne relationship becomes null
      expect(retrievedFrameCount.sticker.target, isNull);
      expect(retrievedFrameCount.hasStickerRelationship, false);
      expect(retrievedFrameCount.stickerName, '');
    });
  });

  group('🔄 Sticker Comprehensive Integration Tests', () {
    test('Complete Sticker-Collection-FrameCount Relationship Lifecycle', () {
      // ==================== PHASE 1: CREATE - Setup Complex Sticker Scenario ====================

      // Create collection
      final collection = Collection.create(
        sessionKey: 'comprehensive_sticker_session_001',
        collectionId: 'comprehensive_collection_001',
        name: 'Comprehensive Sticker Collection',
        isPremium: true,
      );
      final collectionId = collectionBox.put(collection);

      // Create animated sticker
      final animatedSticker = Sticker.create(
        sessionKey: 'comprehensive_sticker_session_001',
        stickerId: 'comprehensive_animated_001',
        collectionId: 'comprehensive_collection_001',
        name: 'Comprehensive Animated Sticker',
        url: 'https://example.com/animated.gif',
        width: 256,
        height: 256,
        isAnimated: true,
        frameCount: 25,
      );

      // Create static sticker
      final staticSticker = Sticker.create(
        sessionKey: 'comprehensive_sticker_session_001',
        stickerId: 'comprehensive_static_001',
        collectionId: 'comprehensive_collection_001',
        name: 'Comprehensive Static Sticker',
        url: 'https://example.com/static.png',
        width: 128,
        height: 128,
        isAnimated: false,
        frameCount: 1,
      );

      // Set collection relationships
      animatedSticker.collection.target = collection;
      staticSticker.collection.target = collection;

      final stickerIds = stickerBox.putMany([animatedSticker, staticSticker]);

      // Create frame count data
      final animatedFrameCount = StickerFrameCount.create(
        sessionKey: 'comprehensive_sticker_session_001',
        stickerId: 'comprehensive_animated_001',
        frameCount: 25,
      );

      final staticFrameCount = StickerFrameCount.create(
        sessionKey: 'comprehensive_sticker_session_001',
        stickerId: 'comprehensive_static_001',
        frameCount: 1,
      );

      // Set frame count relationships
      animatedFrameCount.sticker.target = animatedSticker;
      staticFrameCount.sticker.target = staticSticker;

      // Set sticker frame count relationships
      animatedSticker.frameCountData.target = animatedFrameCount;
      staticSticker.frameCountData.target = staticFrameCount;

      final frameCountIds =
          stickerFrameCountBox.putMany([animatedFrameCount, staticFrameCount]);

      // ==================== PHASE 2: READ - Verify All Relationships ====================

      final retrievedAnimatedSticker = stickerBox.get(stickerIds[0])!;
      final retrievedStaticSticker = stickerBox.get(stickerIds[1])!;

      // Verify sticker properties
      expect(retrievedAnimatedSticker.isAnimated, true);
      expect(retrievedAnimatedSticker.frameCount, 25);
      expect(retrievedAnimatedSticker.hasCollectionRelationship, true);
      expect(
        retrievedAnimatedSticker.collectionName,
        'Comprehensive Sticker Collection',
      );
      expect(retrievedAnimatedSticker.isFromPremiumCollection, true);

      expect(retrievedStaticSticker.isAnimated, false);
      expect(retrievedStaticSticker.frameCount, 1);
      expect(retrievedStaticSticker.hasCollectionRelationship, true);

      // Verify frame count data
      final retrievedAnimatedFrameCount =
          stickerFrameCountBox.get(frameCountIds[0])!;
      final retrievedStaticFrameCount =
          stickerFrameCountBox.get(frameCountIds[1])!;

      expect(retrievedAnimatedFrameCount.isAnimated, true);
      expect(retrievedAnimatedFrameCount.frameCount, 25);
      expect(retrievedStaticFrameCount.isStatic, true);
      expect(retrievedStaticFrameCount.frameCount, 1);

      // Verify collection relationships
      final retrievedCollection = collectionBox.get(collectionId)!;
      expect(retrievedCollection.stickers.length, 2);
      expect(retrievedCollection.animatedStickers.length, 1);
      expect(retrievedCollection.staticStickers.length, 1);

      // ==================== PHASE 3: UPDATE - Test Updates ====================

      // Update animated sticker frame count
      retrievedAnimatedSticker.updateFrameCount(30);
      stickerBox.put(retrievedAnimatedSticker);

      retrievedAnimatedFrameCount.updateFrameCount(30);
      stickerFrameCountBox.put(retrievedAnimatedFrameCount);

      // Update static sticker to animated
      retrievedStaticSticker.updateFrameCount(10);
      stickerBox.put(retrievedStaticSticker);

      retrievedStaticFrameCount.updateFrameCount(10);
      stickerFrameCountBox.put(retrievedStaticFrameCount);

      // Verify updates
      final finalAnimatedSticker = stickerBox.get(stickerIds[0])!;
      expect(finalAnimatedSticker.frameCount, 30);
      expect(finalAnimatedSticker.isAnimated, true);

      final finalStaticSticker = stickerBox.get(stickerIds[1])!;
      expect(finalStaticSticker.frameCount, 10);
      expect(finalStaticSticker.isAnimated, true); // Now animated

      final finalAnimatedFrameCount =
          stickerFrameCountBox.get(frameCountIds[0])!;
      expect(finalAnimatedFrameCount.frameCount, 30);

      final finalStaticFrameCount = stickerFrameCountBox.get(frameCountIds[1])!;
      expect(finalStaticFrameCount.frameCount, 10);
      expect(finalStaticFrameCount.isAnimated, true); // Now animated

      // ==================== PHASE 4: DELETE - Test Deletion Scenarios ====================

      // Delete one sticker
      stickerBox.remove(stickerIds[1]);

      // Verify sticker is deleted but frame count remains
      expect(stickerBox.get(stickerIds[1]), isNull);
      expect(stickerFrameCountBox.get(frameCountIds[1]), isNotNull);

      // Verify collection relationship updated
      final finalCollection = collectionBox.get(collectionId)!;
      expect(finalCollection.stickers.length, 1);
      expect(finalCollection.animatedStickers.length, 1);
      expect(finalCollection.staticStickers.length, 0);

      // ==================== PHASE 5: FINAL VERIFICATION ====================

      expect(stickerBox.count(), 1);
      expect(stickerFrameCountBox.count(), 2);
      expect(collectionBox.count(), 1);

      print(
        '✅ Sticker Comprehensive Integration Test PASSED - All Sticker relationships working correctly',
      );
    });
  });
}
