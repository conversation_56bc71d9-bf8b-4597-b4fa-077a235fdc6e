import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/src/data/database/entities/attachment.dart';
import 'package:data_router/src/data/database/entities/message.dart';
import 'package:data_router/src/data/database/entities/session.dart';
import 'package:data_router/src/data/database/entities/channel.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/database/entities/profile.dart';
import 'package:data_router/src/data/database/entities/user_presence.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';
import 'package:objectbox/objectbox.dart';
import 'dart:io';

void main() {
  late Store store;
  late Box<Attachment> attachmentBox;
  late Box<Message> messageBox;
  late Box<Session> sessionBox;
  late Box<Channel> channelBox;
  late Box<User> userBox;
  late Box<Profile> profileBox;
  late Box<UserPresence> userPresenceBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final dir =
        Directory.systemTemp.createTempSync('attachment_objectbox_test');

    // Initialize ObjectBox store
    store = await openStore(directory: dir.path);
    attachmentBox = store.box<Attachment>();
    messageBox = store.box<Message>();
    sessionBox = store.box<Session>();
    channelBox = store.box<Channel>();
    userBox = store.box<User>();
    profileBox = store.box<Profile>();
    userPresenceBox = store.box<UserPresence>();
  });

  tearDownAll(() {
    // Clean up
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    attachmentBox.removeAll();
    messageBox.removeAll();
    sessionBox.removeAll();
    channelBox.removeAll();
    userBox.removeAll();
    profileBox.removeAll();
    userPresenceBox.removeAll();
  });

  group('🔗 ObjectBox Attachment Entity Relationships Integration Tests', () {
    test('should create Attachment with ToOne relationship to Message', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'attachment_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'attachment_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'General Channel',
        channelTypeRaw: 0,
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'attachment_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      userBox.put(user);

      // Create profile for user
      final profile = Profile.create(
        sessionKey: 'attachment_session_001',
        userId: 'user_001',
        displayName: 'Test User',
        avatar: 'avatar.jpg',
      );
      profile.user.target = user;
      profileBox.put(profile);

      // Create presence for user
      final presence = UserPresence.create(
        sessionKey: 'attachment_session_001',
        userId: 'user_001',
        presenceStatus: 1, // Online
      );
      presence.user.target = user;
      userPresenceBox.put(presence);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'attachment_session_001',
        userId: 'user_001',
        content: 'Message with attachment',
        messageTypeRaw: 1, // Image message
        messageStatusRaw: 2, // Delivered
        attachmentCount: 1,
        isPinned: true,
      );

      // Set message relationships
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      messageBox.put(message);

      final attachment = Attachment.create(
        attachmentId: 'attachment_001',
        messageId: 'message_001',
        ref: 'image_ref_001',
        photoRaw: '{"url": "https://example.com/image.jpg", "size": 1024000}',
        attachmentStatusRaw: 2, // Uploaded
      );

      // Set relationship
      attachment.message.target = message;

      // Act
      final attachmentId = attachmentBox.put(attachment);
      final retrievedAttachment = attachmentBox.get(attachmentId)!;

      // Assert
      expect(retrievedAttachment.attachmentId, 'attachment_001');
      expect(retrievedAttachment.messageId, 'message_001');
      expect(retrievedAttachment.ref, 'image_ref_001');
      expect(retrievedAttachment.photoRaw,
          '{"url": "https://example.com/image.jpg", "size": 1024000}');
      expect(retrievedAttachment.attachmentStatusRaw, 2);

      // Verify basic properties
      expect(retrievedAttachment.isPhoto, true);
      expect(retrievedAttachment.isUploaded, true);
      expect(retrievedAttachment.isSuccessful, true);
      expect(retrievedAttachment.hasContent, true);
      expect(retrievedAttachment.isMediaType, true);

      // Verify ToOne relationship
      expect(retrievedAttachment.message.target, isNotNull);
      expect(retrievedAttachment.message.target!.messageId, 'message_001');
      expect(retrievedAttachment.hasMessageRelationship, true);

      // Verify helper methods
      expect(retrievedAttachment.attachmentType, 'photo');
      expect(retrievedAttachment.attachmentTypeDescription, 'Photo');
      expect(retrievedAttachment.attachmentStatusDescription, 'Uploaded');

      // Verify relationship helper methods
      expect(
          retrievedAttachment.relatedMessageContent, 'Message with attachment');
      expect(retrievedAttachment.relatedMessageType, 1);
      expect(retrievedAttachment.relatedMessageStatus, 2);
      expect(retrievedAttachment.relatedMessageChannelId, 'channel_001');
      expect(retrievedAttachment.relatedMessageSenderId, 'user_001');
      expect(retrievedAttachment.relatedMessageSessionKey,
          'attachment_session_001');
      expect(retrievedAttachment.isRelatedMessageSuccessful, true);
      expect(retrievedAttachment.isRelatedMessagePinned, true);
      expect(retrievedAttachment.relatedMessageSenderUsername, 'testuser');
      expect(retrievedAttachment.relatedMessageSenderDisplayName, 'Test User');
      expect(retrievedAttachment.isRelatedMessageSenderOnline, true);
    });

    test(
        'should create Message with backlink to Attachment (1 Message has many Attachments)',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'backlink_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'backlink_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Media Channel',
        channelTypeRaw: 0,
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
        username: 'mediauser',
      );
      userBox.put(user);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'backlink_session_001',
        userId: 'user_001',
        content: 'Message with multiple attachments',
        messageTypeRaw: 1, // Image message
        messageStatusRaw: 1, // Sent
        attachmentCount: 3,
      );

      // Set message relationships
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      final messageId = messageBox.put(message);

      // Create multiple attachments for the message
      final photoAttachment = Attachment.create(
        attachmentId: 'photo_attachment_001',
        messageId: 'message_001',
        ref: 'photo_ref_001',
        photoRaw: '{"url": "https://example.com/photo.jpg", "size": 2048000}',
        attachmentStatusRaw: 2, // Uploaded
      );
      photoAttachment.message.target = message;

      final videoAttachment = Attachment.create(
        attachmentId: 'video_attachment_001',
        messageId: 'message_001',
        ref: 'video_ref_001',
        videoRaw: '{"url": "https://example.com/video.mp4", "size": 10240000}',
        attachmentStatusRaw: 1, // Uploading
      );
      videoAttachment.message.target = message;

      final audioAttachment = Attachment.create(
        attachmentId: 'audio_attachment_001',
        messageId: 'message_001',
        ref: 'audio_ref_001',
        audioRaw: '{"url": "https://example.com/audio.mp3", "size": 5120000}',
        attachmentStatusRaw: 3, // Failed
      );
      audioAttachment.message.target = message;

      // Act
      attachmentBox.put(photoAttachment);
      attachmentBox.put(videoAttachment);
      attachmentBox.put(audioAttachment);

      final retrievedMessage = messageBox.get(messageId)!;

      // Assert
      expect(retrievedMessage.messageId, 'message_001');
      expect(retrievedMessage.content, 'Message with multiple attachments');

      // Verify backlink relationship
      expect(retrievedMessage.attachments.length, 3);
      expect(retrievedMessage.attachmentsCount, 3);
      expect(retrievedMessage.hasAttachmentsFromRelationship, true);

      // Verify attachment filtering
      expect(retrievedMessage.photoAttachments.length, 1);
      expect(retrievedMessage.videoAttachments.length, 1);
      expect(retrievedMessage.audioAttachments.length, 1);
      expect(retrievedMessage.mediaAttachments.length, 3);
      expect(retrievedMessage.successfulAttachments.length, 1);
      expect(retrievedMessage.failedAttachments.length, 1);
      expect(retrievedMessage.uploadingAttachments.length, 1);

      // Verify attachment counts
      expect(retrievedMessage.photoAttachmentsCount, 1);
      expect(retrievedMessage.videoAttachmentsCount, 1);
      expect(retrievedMessage.audioAttachmentsCount, 1);
      expect(retrievedMessage.mediaAttachmentsCount, 3);
      expect(retrievedMessage.successfulAttachmentsCount, 1);
      expect(retrievedMessage.failedAttachmentsCount, 1);

      // Verify attachment status checks
      expect(retrievedMessage.hasPhotoAttachments, true);
      expect(retrievedMessage.hasVideoAttachments, true);
      expect(retrievedMessage.hasAudioAttachments, true);
      expect(retrievedMessage.hasMediaAttachments, true);
      expect(retrievedMessage.hasFailedAttachments, true);
      expect(retrievedMessage.hasPendingAttachments, false);

      // Verify attachment queries
      final foundAttachment =
          retrievedMessage.getAttachmentById('photo_attachment_001');
      expect(foundAttachment, isNotNull);
      expect(foundAttachment!.attachmentType, 'photo');

      final photoAttachments = retrievedMessage.getAttachmentsByType('photo');
      expect(photoAttachments.length, 1);

      final uploadedAttachments = retrievedMessage.getAttachmentsByStatus(2);
      expect(uploadedAttachments.length, 1);

      // Verify attachment status checks
      expect(retrievedMessage.allAttachmentsSuccessful, false);
      expect(retrievedMessage.anyAttachmentFailed, true);
      expect(retrievedMessage.anyAttachmentPending, false);

      // Verify total size calculation
      expect(retrievedMessage.totalAttachmentsSize, greaterThan(0));
    });

    test('should handle relationship deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'deletion_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      final sessionId = sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'deletion_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Test Channel',
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'deletion_session_001',
        userId: 'user_001',
        username: 'testuser',
      );
      userBox.put(user);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'deletion_session_001',
        userId: 'user_001',
        content: 'Test message',
      );
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      final messageId = messageBox.put(message);

      final attachment = Attachment.create(
        attachmentId: 'attachment_001',
        messageId: 'message_001',
        ref: 'test_ref',
        photoRaw: '{"url": "test.jpg"}',
      );
      attachment.message.target = message;
      final attachmentId = attachmentBox.put(attachment);

      // Verify relationship exists
      expect(attachmentBox.get(attachmentId)!.hasMessageRelationship, true);

      // Act - Delete message
      messageBox.remove(messageId);

      // Assert - Attachment should still exist but relationship should be null
      final retrievedAttachment = attachmentBox.get(attachmentId)!;
      expect(retrievedAttachment.hasMessageRelationship, false);

      // Verify fallback to field value
      expect(retrievedAttachment.messageId, 'message_001');
    });

    test('should handle various attachment types correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'types_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        sessionKey: 'types_session_001',
        channelOwnerUserId: 'owner_001',
        recipientId: 'user_001',
        name: 'Types Channel',
      );
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'types_session_001',
        userId: 'user_001',
        username: 'typeuser',
      );
      userBox.put(user);

      final message = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'channel_001',
        messageId: 'message_001',
        sessionKey: 'types_session_001',
        userId: 'user_001',
        content: 'Message with various attachment types',
      );
      message.session.target = session;
      message.channel.target = channel;
      message.sender.target = user;
      messageBox.put(message);

      // Create different types of attachments
      final stickerAttachment = Attachment.create(
        attachmentId: 'sticker_001',
        messageId: 'message_001',
        stickerRaw: '{"sticker_id": "happy_face"}',
        attachmentStatusRaw: 2,
      );
      stickerAttachment.message.target = message;

      final voiceAttachment = Attachment.create(
        attachmentId: 'voice_001',
        messageId: 'message_001',
        voiceMessageRaw: '{"duration": 30, "url": "voice.wav"}',
        attachmentStatusRaw: 2,
      );
      voiceAttachment.message.target = message;

      final fileAttachment = Attachment.create(
        attachmentId: 'file_001',
        messageId: 'message_001',
        fileMessageRaw: '{"filename": "document.pdf", "size": 2048000}',
        attachmentStatusRaw: 0, // Pending
      );
      fileAttachment.message.target = message;

      // Act
      final stickerAttachmentId = attachmentBox.put(stickerAttachment);
      final voiceAttachmentId = attachmentBox.put(voiceAttachment);
      final fileAttachmentId = attachmentBox.put(fileAttachment);

      // Assert
      final retrievedSticker = attachmentBox.get(stickerAttachmentId)!;
      final retrievedVoice = attachmentBox.get(voiceAttachmentId)!;
      final retrievedFile = attachmentBox.get(fileAttachmentId)!;

      // Verify sticker attachment
      expect(retrievedSticker.isSticker, true);
      expect(retrievedSticker.attachmentType, 'sticker');
      expect(retrievedSticker.attachmentTypeDescription, 'Sticker');
      expect(retrievedSticker.isSuccessful, true);
      expect(retrievedSticker.isMediaType, false);

      // Verify voice attachment
      expect(retrievedVoice.isVoiceMessage, true);
      expect(retrievedVoice.attachmentType, 'voice');
      expect(retrievedVoice.attachmentTypeDescription, 'Voice Message');
      expect(retrievedVoice.isMessageType, true);
      expect(retrievedVoice.isSuccessful, true);

      // Verify file attachment
      expect(retrievedFile.isFileMessage, true);
      expect(retrievedFile.attachmentType, 'file');
      expect(retrievedFile.attachmentTypeDescription, 'File');
      expect(retrievedFile.isPending, true);
      expect(retrievedFile.isSuccessful, false);

      // Verify message backlinks
      final retrievedMessage = messageBox.query().build().findFirst()!;
      expect(retrievedMessage.stickerAttachments.length, 1);
      expect(retrievedMessage.voiceMessageAttachments.length, 1);
      expect(retrievedMessage.fileAttachments.length, 1);
      expect(retrievedMessage.messageTypeAttachments.length, 1);
      expect(retrievedMessage.pendingAttachments.length, 1);
    });
  });

  group('🔄 Attachment Comprehensive Integration Tests', () {
    test('Complete Attachment Entity Relationship Lifecycle', () {
      // ==================== PHASE 1: SETUP - Create Related Entities ====================

      final session = Session.create(
        sessionKey: 'comprehensive_attachment_session_001',
        sessionId: 'session_001',
        sessionToken: 'token_001',
      );
      sessionBox.put(session);

      final channel = Channel.create(
        workspaceId: 'workspace_001',
        channelId: 'media_channel_001',
        sessionKey: 'comprehensive_attachment_session_001',
        channelOwnerUserId: 'admin_001',
        recipientId: 'user_001',
        name: 'Media Sharing',
        channelTypeRaw: 0,
      );
      channel.session.target = session;
      channelBox.put(channel);

      final user = User.create(
        sessionKey: 'comprehensive_attachment_session_001',
        userId: 'user_001',
        username: 'mediauser',
      );
      user.session.target = session;
      userBox.put(user);

      // Create profile for user
      final profile = Profile.create(
        sessionKey: 'comprehensive_attachment_session_001',
        userId: 'user_001',
        displayName: 'Media User',
        avatar: 'media_avatar.jpg',
      );
      profile.user.target = user;
      profileBox.put(profile);

      // Create presence for user
      final presence = UserPresence.create(
        sessionKey: 'comprehensive_attachment_session_001',
        userId: 'user_001',
        presenceStatus: 1, // Online
      );
      presence.user.target = user;
      userPresenceBox.put(presence);

      // ==================== PHASE 2: CREATE - Test Message and Attachment Creation ====================

      final mediaMessage = Message.create(
        workspaceId: 'workspace_001',
        channelId: 'media_channel_001',
        messageId: 'media_message_001',
        sessionKey: 'comprehensive_attachment_session_001',
        userId: 'user_001',
        content: 'Sharing some media files!',
        messageTypeRaw: 1, // Image message
        messageStatusRaw: 1, // Sent
        attachmentCount: 4,
        createTime: DateTime.now().subtract(Duration(hours: 1)),
      );
      mediaMessage.session.target = session;
      mediaMessage.channel.target = channel;
      mediaMessage.sender.target = user;
      messageBox.put(mediaMessage);

      // Create various attachments with different statuses
      final photoAttachment = Attachment.create(
        attachmentId: 'photo_attachment_001',
        messageId: 'media_message_001',
        ref: 'photo_ref_001',
        photoRaw:
            '{"url": "https://example.com/vacation.jpg", "width": 1920, "height": 1080, "size": 3145728}',
        attachmentStatusRaw: 2, // Uploaded
      );
      photoAttachment.message.target = mediaMessage;

      final videoAttachment = Attachment.create(
        attachmentId: 'video_attachment_001',
        messageId: 'media_message_001',
        ref: 'video_ref_001',
        videoRaw:
            '{"url": "https://example.com/vacation.mp4", "duration": 120, "size": 52428800}',
        attachmentStatusRaw: 1, // Uploading
      );
      videoAttachment.message.target = mediaMessage;

      final audioAttachment = Attachment.create(
        attachmentId: 'audio_attachment_001',
        messageId: 'media_message_001',
        ref: 'audio_ref_001',
        audioRaw:
            '{"url": "https://example.com/song.mp3", "duration": 180, "size": 7340032}',
        attachmentStatusRaw: 3, // Failed
      );
      audioAttachment.message.target = mediaMessage;

      final documentAttachment = Attachment.create(
        attachmentId: 'document_attachment_001',
        messageId: 'media_message_001',
        ref: 'document_ref_001',
        fileMessageRaw: '{"filename": "presentation.pdf", "size": 2097152}',
        attachmentStatusRaw: 0, // Pending
      );
      documentAttachment.message.target = mediaMessage;

      // Act
      final attachmentIds = [
        attachmentBox.put(photoAttachment),
        attachmentBox.put(videoAttachment),
        attachmentBox.put(audioAttachment),
        attachmentBox.put(documentAttachment),
      ];

      // Assert - Verify attachment creation
      expect(attachmentBox.count(), 4);

      final retrievedPhoto = attachmentBox.get(attachmentIds[0])!;
      final retrievedVideo = attachmentBox.get(attachmentIds[1])!;
      final retrievedAudio = attachmentBox.get(attachmentIds[2])!;
      final retrievedDocument = attachmentBox.get(attachmentIds[3])!;

      // Verify photo attachment
      expect(retrievedPhoto.attachmentType, 'photo');
      expect(retrievedPhoto.attachmentTypeDescription, 'Photo');
      expect(retrievedPhoto.attachmentStatusDescription, 'Uploaded');
      expect(retrievedPhoto.isSuccessful, true);
      expect(retrievedPhoto.isMediaType, true);
      expect(retrievedPhoto.estimatedSize, greaterThan(0)); // Has content
      expect(retrievedPhoto.relatedMessageContent, 'Sharing some media files!');
      expect(retrievedPhoto.relatedMessageSenderDisplayName, 'Media User');
      expect(retrievedPhoto.isRelatedMessageSenderOnline, true);

      // Verify video attachment
      expect(retrievedVideo.attachmentType, 'video');
      expect(retrievedVideo.attachmentStatusDescription, 'Uploading');
      expect(retrievedVideo.isUploading, true);
      expect(retrievedVideo.isMediaType, true);
      expect(retrievedVideo.estimatedSize, greaterThan(0)); // Has content

      // Verify audio attachment
      expect(retrievedAudio.attachmentType, 'audio');
      expect(retrievedAudio.attachmentStatusDescription, 'Failed');
      expect(retrievedAudio.isFailed, true);
      expect(retrievedAudio.isMediaType, true);
      expect(retrievedAudio.estimatedSize, greaterThan(0)); // Has content

      // Verify document attachment
      expect(retrievedDocument.attachmentType, 'file');
      expect(retrievedDocument.attachmentStatusDescription, 'Pending');
      expect(retrievedDocument.isPending, true);
      expect(retrievedDocument.isMediaType, false);
      expect(retrievedDocument.estimatedSize, greaterThan(0)); // Has content

      // Verify message backlinks and comprehensive attachment management
      final retrievedMessage = messageBox.query().build().findFirst()!;
      expect(retrievedMessage.attachmentsCount, 4);
      expect(retrievedMessage.hasAttachmentsFromRelationship, true);
      expect(retrievedMessage.photoAttachmentsCount, 1);
      expect(retrievedMessage.videoAttachmentsCount, 1);
      expect(retrievedMessage.audioAttachmentsCount, 1);
      expect(retrievedMessage.fileAttachmentsCount, 1);
      expect(retrievedMessage.mediaAttachmentsCount, 3);
      expect(retrievedMessage.successfulAttachmentsCount, 1);
      expect(retrievedMessage.failedAttachmentsCount, 1);
      expect(retrievedMessage.pendingAttachmentsCount, 1);
      expect(retrievedMessage.totalAttachmentsSize,
          greaterThan(0)); // Has attachments with content
      expect(retrievedMessage.allAttachmentsSuccessful, false);
      expect(retrievedMessage.anyAttachmentFailed, true);
      expect(retrievedMessage.anyAttachmentPending, true);

      print(
          '✅ Attachment Comprehensive Integration Test PASSED - All attachment entity relationships working correctly');
    });
  });
}
