import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:objectbox/objectbox.dart';
import 'package:data_router/src/data/database/entities/entities.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';

/// ObjectBox integration tests for Session entity relationships
/// These tests verify that the actual ObjectBox relationships work correctly
void main() {
  late Store store;
  late Box<Session> sessionBox;
  late Box<SessionLocalMetadata> sessionLocalMetadataBox;
  late Box<History> historyBox;
  late Box<User> userBox;

  setUpAll(() async {
    // Create temporary directory for test database
    final tempDir =
        Directory.systemTemp.createTempSync('session_objectbox_test');

    // Initialize ObjectBox store
    store = Store(
      getObjectBoxModel(),
      directory: tempDir.path,
    );

    // Get boxes
    sessionBox = store.box<Session>();
    sessionLocalMetadataBox = store.box<SessionLocalMetadata>();
    historyBox = store.box<History>();
    userBox = store.box<User>();
  });

  tearDownAll(() {
    store.close();
  });

  setUp(() {
    // Clear all data before each test
    sessionBox.removeAll();
    sessionLocalMetadataBox.removeAll();
    historyBox.removeAll();
    userBox.removeAll();
  });

  group('🔗 ObjectBox Session Relationships Integration Tests', () {
    test('should create Session with relationships initialized', () {
      // Arrange & Act
      final session = Session.create(
        sessionKey: 'objectbox_session_001',
        sessionId: 'objectbox_server_001',
        sessionToken: 'objectbox_token_001',
      );
      final sessionId = sessionBox.put(session);

      // Assert
      expect(sessionId, greaterThan(0));

      final retrievedSession = sessionBox.get(sessionId);
      expect(retrievedSession, isNotNull);
      expect(retrievedSession!.sessionKey, 'objectbox_session_001');

      // Verify relationships are initialized
      expect(retrievedSession.histories, isNotNull);
      expect(retrievedSession.privateDataRecords, isNotNull);
      expect(retrievedSession.collections, isNotNull);
      expect(retrievedSession.histories.length, 0); // ToMany - empty initially
      expect(retrievedSession.privateDataRecords.length,
          0); // ToMany - empty initially
      expect(
          retrievedSession.collections.length, 0); // ToMany - empty initially
    });

    test(
        'should create SessionLocalMetadata with ToOne relationship to Session',
        () {
      // Arrange
      final session = Session.create(
        sessionKey: 'metadata_session_001',
        sessionId: 'metadata_server_001',
        sessionToken: 'metadata_token_001',
      );
      final sessionId = sessionBox.put(session);

      final metadata = SessionLocalMetadata.create(
        sessionKey: 'metadata_session_001',
        userUpdateTimeAfter: DateTime.now(),
      );

      // Act - Set ToOne relationship
      metadata.session.target = session;
      final metadataId = sessionLocalMetadataBox.put(metadata);

      // Assert
      expect(metadataId, greaterThan(0));

      final retrievedMetadata = sessionLocalMetadataBox.get(metadataId);
      expect(retrievedMetadata, isNotNull);
      expect(retrievedMetadata!.session.target, isNotNull);
      expect(
          retrievedMetadata.session.target!.sessionKey, 'metadata_session_001');
      expect(retrievedMetadata.session.targetId, sessionId);

      // Verify 1:1 relationship by querying metadata
      final retrievedSession = sessionBox.get(sessionId);
      expect(retrievedSession, isNotNull);

      // Query metadata by sessionKey to verify 1:1 relationship
      final metadataQuery = sessionLocalMetadataBox
          .query(
              SessionLocalMetadata_.sessionKey.equals('metadata_session_001'))
          .build();
      final foundMetadata = metadataQuery.findFirst();
      metadataQuery.close();

      expect(foundMetadata, isNotNull);
      expect(foundMetadata!.id, metadataId);
    });

    test('should create History with ToOne relationship to Session', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'history_session_001',
        sessionId: 'history_server_001',
        sessionToken: 'history_token_001',
      );
      final sessionId = sessionBox.put(session);

      final history = History.create(
        sessionKey: 'history_session_001',
        historyId: 'hist_001',
        entityType: 'message',
        entityId: 'msg_001',
        action: 'create',
      );

      // Act - Set ToOne relationship
      history.session.target = session;
      final historyId = historyBox.put(history);

      // Assert
      expect(historyId, greaterThan(0));

      final retrievedHistory = historyBox.get(historyId);
      expect(retrievedHistory, isNotNull);
      expect(retrievedHistory!.session.target, isNotNull);
      expect(
          retrievedHistory.session.target!.sessionKey, 'history_session_001');
      expect(retrievedHistory.session.targetId, sessionId);

      // Verify backlink relationship
      final retrievedSession = sessionBox.get(sessionId);
      expect(retrievedSession!.histories.length, 1);
      expect(retrievedSession.histories.first.id, historyId);
    });

    test('should handle multiple related entities correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'multi_session_001',
        sessionId: 'multi_server_001',
        sessionToken: 'multi_token_001',
      );
      final sessionId = sessionBox.put(session);

      // Create one metadata (1:1 relationship) and multiple histories
      final metadata = SessionLocalMetadata.create(
        sessionKey: 'multi_session_001',
        userUpdateTimeAfter: DateTime.now(),
      );
      metadata.session.target = session;

      final history1 = History.create(
        sessionKey: 'multi_session_001',
        historyId: 'multi_hist_001',
        entityType: 'message',
        entityId: 'multi_msg_001',
        action: 'create',
      );
      history1.session.target = session;

      final history2 = History.create(
        sessionKey: 'multi_session_001',
        historyId: 'multi_hist_002',
        entityType: 'channel',
        entityId: 'multi_ch_001',
        action: 'update',
      );
      history2.session.target = session;

      // Act
      sessionLocalMetadataBox.put(metadata);
      historyBox.putMany([history1, history2]);

      // Assert
      final retrievedSession = sessionBox.get(sessionId);
      expect(retrievedSession, isNotNull);

      // Verify ObjectBox relationships
      expect(retrievedSession!.histories.length, 2); // 1:N relationship

      // Verify relationship consistency through queries
      final metadataQuery = sessionLocalMetadataBox
          .query(SessionLocalMetadata_.sessionKey.equals('multi_session_001'))
          .build();
      final foundMetadata = metadataQuery.findFirst();
      metadataQuery.close();

      expect(foundMetadata, isNotNull);
      expect(foundMetadata!.session.target!.sessionKey, 'multi_session_001');

      for (final hist in retrievedSession.histories) {
        expect(hist.session.target!.sessionKey, 'multi_session_001');
      }
    });

    test('should handle relationship updates correctly', () {
      // Arrange - Create two sessions
      final session1 = Session.create(
        sessionKey: 'update_session_001',
        sessionId: 'update_server_001',
        sessionToken: 'update_token_001',
      );

      final session2 = Session.create(
        sessionKey: 'update_session_002',
        sessionId: 'update_server_002',
        sessionToken: 'update_token_002',
      );

      final session1Id = sessionBox.put(session1);
      final session2Id = sessionBox.put(session2);

      final metadata = SessionLocalMetadata.create(
        sessionKey: 'update_session_001',
        userUpdateTimeAfter: DateTime.now(),
      );
      metadata.session.target = session1;
      final metadataId = sessionLocalMetadataBox.put(metadata);

      // Act - Update relationship to point to session2
      metadata.session.target = session2;
      metadata.sessionKey = 'update_session_002'; // Update sessionKey as well
      sessionLocalMetadataBox.put(metadata);

      // Assert - Verify relationship update
      final retrievedMetadata = sessionLocalMetadataBox.get(metadataId);
      expect(retrievedMetadata!.session.target, isNotNull);
      expect(
          retrievedMetadata.session.target!.sessionKey, 'update_session_002');
      expect(retrievedMetadata.sessionKey, 'update_session_002');
      expect(retrievedMetadata.session.targetId, session2Id);

      // Verify backlink relationships (1:1)
      final retrievedSession1 = sessionBox.get(session1Id);
      final retrievedSession2 = sessionBox.get(session2Id);

      // TODO: Update to use query-based approach for 1:1 relationships
      // expect(retrievedSession1!.localMetadata.target, isNull); // No longer linked
      // expect(retrievedSession2!.localMetadata.target, isNotNull); // Now linked
      // expect(retrievedSession2.localMetadata.target!.id, metadataId);

      // Verify through direct metadata query
      final metadata1Query = sessionLocalMetadataBox
          .query(SessionLocalMetadata_.sessionKey.equals('update_session_001'))
          .build();
      final metadata1 = metadata1Query.findFirst();
      metadata1Query.close();

      final metadata2Query = sessionLocalMetadataBox
          .query(SessionLocalMetadata_.sessionKey.equals('update_session_002'))
          .build();
      final metadata2 = metadata2Query.findFirst();
      metadata2Query.close();

      expect(metadata2, isNotNull);
      expect(metadata2!.id, metadataId);
    });

    test('should handle deletion correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'delete_session_001',
        sessionId: 'delete_server_001',
        sessionToken: 'delete_token_001',
      );
      final sessionId = sessionBox.put(session);

      final metadata = SessionLocalMetadata.create(
        sessionKey: 'delete_session_001',
        userUpdateTimeAfter: DateTime.now(),
      );
      metadata.session.target = session;
      final metadataId = sessionLocalMetadataBox.put(metadata);

      // Act - Delete session
      sessionBox.remove(sessionId);

      // Assert - Verify metadata relationship becomes null
      final retrievedMetadata = sessionLocalMetadataBox.get(metadataId);
      expect(retrievedMetadata, isNotNull); // Metadata still exists
      expect(retrievedMetadata!.session.target,
          isNull); // But relationship is null
    });

    test('should verify helper methods work correctly', () {
      // Arrange
      final session = Session.create(
        sessionKey: 'helper_session_001',
        sessionId: 'helper_server_001',
        sessionToken: 'helper_token_001',
      );
      sessionBox.put(session);

      final metadata = SessionLocalMetadata.create(
        sessionKey: 'helper_session_001',
        userUpdateTimeAfter: DateTime.now(),
      );
      metadata.session.target = session;
      sessionLocalMetadataBox.put(metadata);

      final history = History.create(
        sessionKey: 'helper_session_001',
        historyId: 'helper_hist_001',
        entityType: 'message',
        entityId: 'helper_msg_001',
        action: 'create',
      );
      history.session.target = session;
      historyBox.put(history);

      // Act & Assert - Test helper methods
      expect(metadata.effectiveSessionKey, 'helper_session_001');
      expect(metadata.hasSessionRelationship, isTrue);
      expect(metadata.relatedSessionId, 'helper_server_001');
      expect(metadata.relatedSessionToken, 'helper_token_001');

      expect(history.effectiveSessionKey, 'helper_session_001');
      expect(history.hasSessionRelationship, isTrue);
      expect(history.relatedSessionId, 'helper_server_001');
      expect(history.relatedSessionToken, 'helper_token_001');
    });
  });
}
