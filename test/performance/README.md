# 🚀 Database Performance Test Suite

Comprehensive performance testing suite for ObjectBox database operations across all entities and relationships.

## 📊 Overview

This test suite measures performance metrics for:
- **Bulk Insert Operations** - Mass data insertion performance
- **Complex Query Operations** - Relationship traversal and query performance  
- **Bulk Update Operations** - Mass data modification performance
- **Bulk Delete Operations** - Mass data removal performance
- **Memory Usage** - Memory consumption with large datasets
- **Concurrent Operations** - Multi-threaded read/write performance

## 🎯 Test Scales

- **Small Scale**: 100 records
- **Medium Scale**: 1,000 records
- **Large Scale**: 10,000 records

## 📈 Metrics Measured

- **Execution Time** (milliseconds)
- **Throughput** (operations/second)
- **Memory Usage** (MB)
- **Database File Size** (KB)

## 🚀 Quick Start

### Run Performance Tests

```bash
# Using Makefile (Recommended)
make test-db-performance

# Direct Flutter command
flutter test test/performance/database_performance_test.dart --reporter=compact
```

### Run with Verbose Output

```bash
flutter test test/performance/database_performance_test.dart -v
```

## 📋 Test Coverage

### Entities Tested

- **Session** - Core session management
- **User** - User data and relationships
- **Message** - Chat messages with attachments
- **Channel** - Communication channels
- **Collection** - Sticker collections
- **Attachment** - Message attachments
- **History** - Action history tracking

### Relationships Tested

- **ToOne Relationships** - 1:1 and N:1 relationships
- **ToMany Relationships** - 1:N relationships via @Backlink
- **Complex Queries** - Multi-entity relationship traversal

## 📊 Performance Report

The test suite generates a comprehensive performance report including:

### Summary Table
```
Entity               Scale      Time(ms)    Ops/sec    Memory(MB)  DB Size(KB)
Session              100        45          2222       1.2         156.7
Session              1000       234         4273       8.9         1247.3
Session              10000      1876        5331       67.4        12456.8
```

### Statistics
- Average execution times by operation type
- Min/Max performance metrics
- Overall test summary

## 🔧 Configuration

### Test Data Generation

The suite automatically generates realistic test data:
- **Sessions** with authentication tokens
- **Users** with profiles and relationships
- **Channels** with members and messages
- **Messages** with content and attachments
- **Collections** with stickers

### Memory Monitoring

Memory usage is tracked using:
- Process RSS (Resident Set Size)
- Database file size monitoring
- Memory delta calculations

## 📝 Test Structure

```
test/performance/
├── database_performance_test.dart  # Main test suite
└── README.md                      # This documentation
```

### Key Test Groups

1. **Bulk Insert Performance**
   - Tests mass data insertion across entities
   - Measures throughput and memory usage

2. **Complex Query Performance**
   - Tests relationship traversal
   - Measures query execution time

3. **Bulk Update Performance**
   - Tests mass data modification
   - Measures update throughput

4. **Bulk Delete Performance**
   - Tests mass data removal
   - Measures deletion performance

5. **Memory Usage Tests**
   - Tests memory consumption with large datasets
   - Monitors memory leaks

6. **Concurrent Operations**
   - Tests multi-threaded operations
   - Measures concurrent read/write performance

## 🎯 Best Practices

### Running Tests

1. **Clean Environment**: Tests automatically clean up data between runs
2. **Sufficient Resources**: Ensure adequate memory for large-scale tests
3. **Consistent Environment**: Run tests in similar conditions for comparison

### Interpreting Results

1. **Throughput**: Higher ops/sec indicates better performance
2. **Memory Usage**: Lower memory usage is generally better
3. **Execution Time**: Lower times indicate better performance
4. **Database Size**: Monitor for unexpected growth

### Performance Optimization

1. **Indexes**: Ensure proper indexing for frequently queried fields
2. **Relationships**: Optimize relationship structures
3. **Batch Operations**: Use bulk operations for better performance

## 🔍 Troubleshooting

### Common Issues

1. **Memory Errors**: Reduce test scale if running out of memory
2. **Timeout Issues**: Increase test timeout for large datasets
3. **Relationship Errors**: Verify entity relationships are properly configured

### Debug Mode

Run with verbose output to see detailed progress:
```bash
flutter test test/performance/database_performance_test.dart -v
```

## 📞 Support

For issues or questions about performance testing:
1. Check the main project README
2. Review entity relationship documentation
3. Examine existing test patterns in `test/entities/`

## 🎉 Contributing

When adding new performance tests:
1. Follow existing test patterns
2. Include proper cleanup
3. Add comprehensive metrics
4. Update this documentation
