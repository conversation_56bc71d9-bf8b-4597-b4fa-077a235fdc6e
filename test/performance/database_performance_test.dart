import 'dart:io';
import 'dart:isolate';
import 'dart:math';
import 'package:flutter_test/flutter_test.dart';
import 'package:objectbox/objectbox.dart';
import 'package:data_router/src/data/database/entities/entities.dart';
import 'package:data_router/src/data/database/generated/objectbox.g.dart';

// Global variables for utility functions
late Directory tempDir;
late Store store;

/// Comprehensive ObjectBox Database Performance Test Suite
/// Tests performance metrics across all entities and relationships
///
/// Test Scales:
/// - Small: 100 records
/// - Medium: 1,000 records
/// - Large: 10,000 records
///
/// Metrics Measured:
/// - Execution time (milliseconds)
/// - Memory usage (MB)
/// - Throughput (operations/second)
/// - Database file size (KB)
// Global entity boxes for utility functions
late Box<Session> sessionBox;
late Box<SessionLocalMetadata> sessionLocalMetadataBox;
late Box<User> userBox;
late Box<Collection> collectionBox;
late Box<Sticker> stickerBox;
late Box<History> historyBox;
late Box<Manager> managerBox;
late Box<PrivateData> privateDataBox;
late Box<Channel> channelBox;
late Box<Member> memberBox;
late Box<Message> messageBox;
late Box<Attachment> attachmentBox;
late Box<TranslatedResult> translatedResultBox;

// Global performance metrics storage
late Map<String, Map<String, dynamic>> performanceResults;

void main() {
  // Performance metrics storage
  performanceResults = {};

  setUpAll(() async {
    // Create temporary directory for test database
    tempDir = Directory.systemTemp.createTempSync('performance_test_db');

    // Initialize ObjectBox store
    store = Store(
      getObjectBoxModel(),
      directory: tempDir.path,
    );

    // Initialize all entity boxes
    sessionBox = store.box<Session>();
    sessionLocalMetadataBox = store.box<SessionLocalMetadata>();
    userBox = store.box<User>();
    collectionBox = store.box<Collection>();
    stickerBox = store.box<Sticker>();
    historyBox = store.box<History>();
    managerBox = store.box<Manager>();
    privateDataBox = store.box<PrivateData>();
    channelBox = store.box<Channel>();
    memberBox = store.box<Member>();
    messageBox = store.box<Message>();
    attachmentBox = store.box<Attachment>();
    translatedResultBox = store.box<TranslatedResult>();

    print('\n🚀 ObjectBox Database Performance Test Suite');
    print('=' * 60);
    print('📊 Testing scales: Small (100), Medium (1K), Large (10K)');
    print('⚡ Metrics: Time, Memory, Throughput, DB Size');
    print('🗄️ Database path: ${tempDir.path}');
    print('=' * 60);
  });

  tearDownAll(() async {
    // Print comprehensive performance report
    _printPerformanceReport(performanceResults);

    // Cleanup
    store.close();
    if (tempDir.existsSync()) {
      tempDir.deleteSync(recursive: true);
    }
  });

  setUp(() {
    // Clear all data before each test
    _clearAllBoxes();
  });

  group('🔥 Database Performance Tests', () {
    group('📝 Bulk Insert Performance', () {
      test('Session entity bulk insert performance', () async {
        await _testBulkInsertPerformance<Session>(
          entityName: 'Session',
          box: sessionBox,
          createEntity: (index) => Session.create(
            sessionKey: 'perf_session_$index',
            sessionId: 'perf_server_$index',
            sessionToken: 'perf_token_$index',
          ),
        );
      });

      test('User entity bulk insert performance', () async {
        // Create sessions first for relationships
        final sessions = _createTestSessions(10000);
        sessionBox.putMany(sessions);

        await _testBulkInsertPerformance<User>(
          entityName: 'User',
          box: userBox,
          createEntity: (index) {
            final user = User.create(
              sessionKey: 'perf_session_${index % 100}',
              userId: 'perf_user_$index',
              username: 'user_$index',
            );
            // Set relationship to session
            user.session.target = sessions[index % sessions.length];
            return user;
          },
        );
      });

      test('Message entity bulk insert performance', () async {
        // Setup dependencies
        final sessions = _createTestSessions(100);
        final users = _createTestUsers(sessions, 1000);
        final channels = _createTestChannels(sessions, users, 500);

        sessionBox.putMany(sessions);
        userBox.putMany(users);
        channelBox.putMany(channels);

        await _testBulkInsertPerformance<Message>(
          entityName: 'Message',
          box: messageBox,
          createEntity: (index) {
            final message = Message.create(
              workspaceId: 'workspace_1',
              channelId: 'perf_channel_${index % channels.length}',
              messageId: 'perf_msg_$index',
              sessionKey: 'perf_session_${index % sessions.length}',
              userId: 'perf_user_${index % users.length}',
              content: 'Performance test message $index',
            );
            // Set relationships
            message.channel.target = channels[index % channels.length];
            message.sender.target = users[index % users.length];
            return message;
          },
        );
      });
    });

    group('🔍 Complex Query Performance', () {
      test('Session with relationships query performance', () async {
        // Setup test data
        await _setupComplexQueryTestData();

        await _testComplexQueryPerformance(
          testName: 'Session with relationships',
          queryFunction: () {
            final query = sessionBox.query().build();
            final sessions = query.find();
            query.close();

            // Access relationships to test lazy loading performance
            int totalRelated = 0;
            for (final session in sessions) {
              totalRelated += session.histories.length;
              totalRelated += session.privateDataRecords.length;
              totalRelated += session.collections.length;
            }
            return sessions.length;
          },
        );
      });

      test('User with multiple relationships query performance', () async {
        await _testComplexQueryPerformance(
          testName: 'User with multiple relationships',
          queryFunction: () {
            final query = userBox.query().build();
            final users = query.find();
            query.close();

            // Access multiple relationships
            double totalRelated = 0;
            for (final user in users) {
              totalRelated += user.profiles.length;
              totalRelated += user.sentMessages.length;
              totalRelated += user.memberships.length;
            }
            return users.length;
          },
        );
      });

      test('Message with attachments query performance', () async {
        await _testComplexQueryPerformance(
          testName: 'Message with attachments',
          queryFunction: () {
            final query = messageBox.query().build();
            final messages = query.find();
            query.close();

            // Access attachments relationship
            int totalAttachments = 0;
            for (final message in messages) {
              totalAttachments += message.attachments.length;
              // Access translation if exists
              if (message.translatedResult.target != null) {
                totalAttachments += 1;
              }
            }
            return messages.length;
          },
        );
      });
    });

    group('✏️ Bulk Update Performance', () {
      test('Session bulk update performance', () async {
        // Create test data first
        final sessions = _createTestSessions(10000);
        sessionBox.putMany(sessions);

        await _testBulkUpdatePerformance<Session>(
          entityName: 'Session',
          box: sessionBox,
          updateFunction: (entities) {
            for (int i = 0; i < entities.length; i++) {
              entities[i].sessionToken = 'updated_token_$i';
              entities[i].active = !entities[i].active;
            }
          },
        );
      });

      test('User bulk update performance', () async {
        // Setup dependencies and test data
        final sessions = _createTestSessions(100);
        sessionBox.putMany(sessions);

        final users = _createTestUsers(sessions, 10000);
        userBox.putMany(users);

        await _testBulkUpdatePerformance<User>(
          entityName: 'User',
          box: userBox,
          updateFunction: (entities) {
            for (int i = 0; i < entities.length; i++) {
              entities[i].username = 'updated_user_$i';
              entities[i].updateTime = DateTime.now();
            }
          },
        );
      });
    });

    group('🗑️ Bulk Delete Performance', () {
      test('Session bulk delete performance', () async {
        await _testBulkDeletePerformance<Session>(
          entityName: 'Session',
          box: sessionBox,
          createEntities: (count) => _createTestSessions(count),
        );
      });

      test('Message bulk delete performance', () async {
        await _testBulkDeletePerformance<Message>(
          entityName: 'Message',
          box: messageBox,
          createEntities: (count) {
            // Setup dependencies
            final sessions = _createTestSessions(10);
            final users = _createTestUsers(sessions, 100);
            final channels = _createTestChannels(sessions, users, 50);

            sessionBox.putMany(sessions);
            userBox.putMany(users);
            channelBox.putMany(channels);

            return _createTestMessages(sessions, users, channels, count);
          },
        );
      });
    });

    group('💾 Memory Usage Tests', () {
      test('Large dataset memory usage', () async {
        await _testMemoryUsage();
      });
    });

    group('🔄 Concurrent Operations Tests', () {
      test('Concurrent read/write operations', () async {
        await _testConcurrentOperations();
      });
    });
  });
}

/// Clear all entity boxes
void _clearAllBoxes() {
  sessionBox.removeAll();
  sessionLocalMetadataBox.removeAll();
  userBox.removeAll();
  collectionBox.removeAll();
  stickerBox.removeAll();
  historyBox.removeAll();
  managerBox.removeAll();
  privateDataBox.removeAll();
  channelBox.removeAll();
  memberBox.removeAll();
  messageBox.removeAll();
  attachmentBox.removeAll();
  translatedResultBox.removeAll();
}

/// Test bulk insert performance for a specific entity type
Future<void> _testBulkInsertPerformance<T>({
  required String entityName,
  required Box<T> box,
  required T Function(int index) createEntity,
}) async {
  final scales = [100, 1000, 10000];

  for (final scale in scales) {
    print('\n📝 Testing $entityName bulk insert - Scale: $scale');

    // Create entities
    final entities = <T>[];
    for (int i = 0; i < scale; i++) {
      entities.add(createEntity(i));
    }

    // Measure memory before
    final memoryBefore = _getCurrentMemoryUsage();

    // Measure insert performance
    final stopwatch = Stopwatch()..start();
    box.putMany(entities);
    stopwatch.stop();

    // Measure memory after
    final memoryAfter = _getCurrentMemoryUsage();
    final dbSize = _getDatabaseSize();

    // Calculate metrics
    final executionTime = stopwatch.elapsedMilliseconds;
    final throughput = (scale / (executionTime / 1000)).round();
    final memoryUsed = memoryAfter - memoryBefore;

    // Store results
    final testKey = '${entityName}_bulk_insert_$scale';
    performanceResults[testKey] = {
      'entity': entityName,
      'operation': 'Bulk Insert',
      'scale': scale,
      'execution_time_ms': executionTime,
      'throughput_ops_sec': throughput,
      'memory_used_mb': memoryUsed,
      'db_size_kb': dbSize,
    };

    print('  ⏱️  Execution time: ${executionTime}ms');
    print('  🚀 Throughput: $throughput ops/sec');
    print('  💾 Memory used: ${memoryUsed.toStringAsFixed(2)}MB');
    print('  📁 DB size: ${dbSize.toStringAsFixed(2)}KB');

    // Clear for next test
    box.removeAll();
  }
}

/// Test bulk update performance
Future<void> _testBulkUpdatePerformance<T>({
  required String entityName,
  required Box<T> box,
  required void Function(List<T> entities) updateFunction,
}) async {
  final scales = [100, 1000, 10000];

  for (final scale in scales) {
    print('\n✏️ Testing $entityName bulk update - Scale: $scale');

    // Get all entities
    final entities = box.getAll();
    if (entities.length < scale) {
      print(
          '  ⚠️  Insufficient data for scale $scale (have ${entities.length})');
      continue;
    }

    final testEntities = entities.take(scale).toList();

    // Measure memory before
    final memoryBefore = _getCurrentMemoryUsage();

    // Measure update performance
    final stopwatch = Stopwatch()..start();
    updateFunction(testEntities);
    box.putMany(testEntities);
    stopwatch.stop();

    // Measure memory after
    final memoryAfter = _getCurrentMemoryUsage();
    final dbSize = _getDatabaseSize();

    // Calculate metrics
    final executionTime = stopwatch.elapsedMilliseconds;
    final throughput = (scale / (executionTime / 1000)).round();
    final memoryUsed = memoryAfter - memoryBefore;

    // Store results
    final testKey = '${entityName}_bulk_update_$scale';
    performanceResults[testKey] = {
      'entity': entityName,
      'operation': 'Bulk Update',
      'scale': scale,
      'execution_time_ms': executionTime,
      'throughput_ops_sec': throughput,
      'memory_used_mb': memoryUsed,
      'db_size_kb': dbSize,
    };

    print('  ⏱️  Execution time: ${executionTime}ms');
    print('  🚀 Throughput: $throughput ops/sec');
    print('  💾 Memory used: ${memoryUsed.toStringAsFixed(2)}MB');
    print('  📁 DB size: ${dbSize.toStringAsFixed(2)}KB');
  }
}

/// Test bulk delete performance
Future<void> _testBulkDeletePerformance<T>({
  required String entityName,
  required Box<T> box,
  required List<T> Function(int count) createEntities,
}) async {
  final scales = [100, 1000, 10000];

  for (final scale in scales) {
    print('\n🗑️ Testing $entityName bulk delete - Scale: $scale');

    // Create test data
    final entities = createEntities(scale);
    final ids = box.putMany(entities);

    // Measure memory before
    final memoryBefore = _getCurrentMemoryUsage();

    // Measure delete performance
    final stopwatch = Stopwatch()..start();
    box.removeMany(ids);
    stopwatch.stop();

    // Measure memory after
    final memoryAfter = _getCurrentMemoryUsage();
    final dbSize = _getDatabaseSize();

    // Calculate metrics
    final executionTime = stopwatch.elapsedMilliseconds;
    final throughput = (scale / (executionTime / 1000)).round();
    final memoryUsed = memoryAfter - memoryBefore;

    // Store results
    final testKey = '${entityName}_bulk_delete_$scale';
    performanceResults[testKey] = {
      'entity': entityName,
      'operation': 'Bulk Delete',
      'scale': scale,
      'execution_time_ms': executionTime,
      'throughput_ops_sec': throughput,
      'memory_used_mb': memoryUsed,
      'db_size_kb': dbSize,
    };

    print('  ⏱️  Execution time: ${executionTime}ms');
    print('  🚀 Throughput: $throughput ops/sec');
    print('  💾 Memory used: ${memoryUsed.toStringAsFixed(2)}MB');
    print('  📁 DB size: ${dbSize.toStringAsFixed(2)}KB');
  }
}

/// Test complex query performance
Future<void> _testComplexQueryPerformance({
  required String testName,
  required int Function() queryFunction,
}) async {
  final scales = [100, 1000, 10000];

  for (final scale in scales) {
    print('\n🔍 Testing $testName - Scale: $scale');

    // Measure memory before
    final memoryBefore = _getCurrentMemoryUsage();

    // Measure query performance
    final stopwatch = Stopwatch()..start();
    final resultCount = queryFunction();
    stopwatch.stop();

    // Measure memory after
    final memoryAfter = _getCurrentMemoryUsage();
    final dbSize = _getDatabaseSize();

    // Calculate metrics
    final executionTime = stopwatch.elapsedMilliseconds;
    final throughput =
        executionTime > 0 ? (resultCount / (executionTime / 1000)).round() : 0;
    final memoryUsed = memoryAfter - memoryBefore;

    // Store results
    final testKey = '${testName.replaceAll(' ', '_')}_query_$scale';
    performanceResults[testKey] = {
      'entity': testName,
      'operation': 'Complex Query',
      'scale': scale,
      'execution_time_ms': executionTime,
      'throughput_ops_sec': throughput,
      'memory_used_mb': memoryUsed,
      'db_size_kb': dbSize,
      'result_count': resultCount,
    };

    print('  ⏱️  Execution time: ${executionTime}ms');
    print('  🚀 Throughput: $throughput ops/sec');
    print('  💾 Memory used: ${memoryUsed.toStringAsFixed(2)}MB');
    print('  📁 DB size: ${dbSize.toStringAsFixed(2)}KB');
    print('  📊 Results: $resultCount records');
  }
}

/// Setup complex query test data
Future<void> _setupComplexQueryTestData() async {
  print('\n🔧 Setting up complex query test data...');

  // Create sessions
  final sessions = _createTestSessions(100);
  sessionBox.putMany(sessions);

  // Create users
  final users = _createTestUsers(sessions, 1000);
  userBox.putMany(users);

  // Create channels
  final channels = _createTestChannels(sessions, users, 500);
  channelBox.putMany(channels);

  // Create messages
  final messages = _createTestMessages(sessions, users, channels, 5000);
  messageBox.putMany(messages);

  // Create attachments
  final attachments = _createTestAttachments(messages, 2000);
  attachmentBox.putMany(attachments);

  // Create histories
  final histories = _createTestHistories(sessions, 3000);
  historyBox.putMany(histories);

  // Create collections
  final collections = _createTestCollections(sessions, 200);
  collectionBox.putMany(collections);

  // Create stickers
  final stickers = _createTestStickers(collections, 1000);
  stickerBox.putMany(stickers);

  print('  ✅ Test data setup complete');
  print('  📊 Sessions: ${sessions.length}');
  print('  👥 Users: ${users.length}');
  print('  📢 Channels: ${channels.length}');
  print('  💬 Messages: ${messages.length}');
  print('  📎 Attachments: ${attachments.length}');
  print('  📚 Histories: ${histories.length}');
  print('  🎨 Collections: ${collections.length}');
  print('  😀 Stickers: ${stickers.length}');
}

/// Test memory usage with large datasets
Future<void> _testMemoryUsage() async {
  print('\n💾 Testing memory usage with large datasets...');

  final memoryBefore = _getCurrentMemoryUsage();
  print('  📊 Initial memory: ${memoryBefore.toStringAsFixed(2)}MB');

  // Create large dataset
  await _setupComplexQueryTestData();

  final memoryAfter = _getCurrentMemoryUsage();
  final memoryUsed = memoryAfter - memoryBefore;
  final dbSize = _getDatabaseSize();

  print('  📊 Memory after setup: ${memoryAfter.toStringAsFixed(2)}MB');
  print('  📈 Memory used: ${memoryUsed.toStringAsFixed(2)}MB');
  print('  📁 Database size: ${dbSize.toStringAsFixed(2)}KB');

  // Store results
  performanceResults['memory_usage_large_dataset'] = {
    'entity': 'All Entities',
    'operation': 'Memory Usage',
    'scale': 'Large Dataset',
    'memory_before_mb': memoryBefore,
    'memory_after_mb': memoryAfter,
    'memory_used_mb': memoryUsed,
    'db_size_kb': dbSize,
  };
}

/// Test concurrent operations
Future<void> _testConcurrentOperations() async {
  print('\n🔄 Testing concurrent read/write operations...');

  // Setup initial data
  final sessions = _createTestSessions(100);
  sessionBox.putMany(sessions);

  final memoryBefore = _getCurrentMemoryUsage();

  // Concurrent operations
  final stopwatch = Stopwatch()..start();

  final futures = <Future>[];

  // Concurrent reads
  for (int i = 0; i < 10; i++) {
    futures.add(Future(() {
      final query = sessionBox.query().build();
      final results = query.find();
      query.close();
      return results.length;
    }));
  }

  // Concurrent writes
  for (int i = 0; i < 5; i++) {
    futures.add(Future(() {
      final newSessions = _createTestSessions(50);
      return sessionBox.putMany(newSessions);
    }));
  }

  // Wait for all operations to complete
  final results = await Future.wait(futures);
  stopwatch.stop();

  final memoryAfter = _getCurrentMemoryUsage();
  final memoryUsed = memoryAfter - memoryBefore;
  final dbSize = _getDatabaseSize();

  final executionTime = stopwatch.elapsedMilliseconds;
  final totalOperations = futures.length;
  final throughput = (totalOperations / (executionTime / 1000)).round();

  print('  ⏱️  Execution time: ${executionTime}ms');
  print('  🚀 Throughput: $throughput ops/sec');
  print('  💾 Memory used: ${memoryUsed.toStringAsFixed(2)}MB');
  print('  📁 DB size: ${dbSize.toStringAsFixed(2)}KB');
  print('  🔄 Total operations: $totalOperations');

  // Store results
  performanceResults['concurrent_operations'] = {
    'entity': 'Session',
    'operation': 'Concurrent Read/Write',
    'scale': totalOperations,
    'execution_time_ms': executionTime,
    'throughput_ops_sec': throughput,
    'memory_used_mb': memoryUsed,
    'db_size_kb': dbSize,
  };
}

/// Create test sessions
List<Session> _createTestSessions(int count) {
  final sessions = <Session>[];
  final timestamp = DateTime.now().millisecondsSinceEpoch;
  for (int i = 0; i < count; i++) {
    sessions.add(Session.create(
      sessionKey: 'perf_session_${timestamp}_$i',
      sessionId: 'perf_server_${timestamp}_$i',
      sessionToken: 'perf_token_${timestamp}_$i',
    ));
  }
  return sessions;
}

/// Create test users with session relationships
List<User> _createTestUsers(List<Session> sessions, int count) {
  final users = <User>[];
  final timestamp = DateTime.now().millisecondsSinceEpoch;
  for (int i = 0; i < count; i++) {
    final user = User.create(
      sessionKey: 'perf_session_${i % sessions.length}',
      userId: 'perf_user_${timestamp}_$i',
      username: 'user_${timestamp}_$i',
    );
    // Set relationship to session
    user.session.target = sessions[i % sessions.length];
    users.add(user);
  }
  return users;
}

/// Create test channels with session and user relationships
List<Channel> _createTestChannels(
    List<Session> sessions, List<User> users, int count) {
  final channels = <Channel>[];
  for (int i = 0; i < count; i++) {
    final channel = Channel.create(
      workspaceId: 'workspace_1',
      channelId: 'perf_channel_$i',
      sessionKey: 'perf_session_${i % sessions.length}',
      channelOwnerUserId: 'perf_user_${i % users.length}',
      recipientId: 'perf_user_${(i + 1) % users.length}',
      name: 'Channel $i',
    );
    // Set relationships
    channel.session.target = sessions[i % sessions.length];
    channel.ownerUser.target = users[i % users.length];
    channel.recipientUser.target = users[(i + 1) % users.length];
    channels.add(channel);
  }
  return channels;
}

/// Create test messages with relationships
List<Message> _createTestMessages(List<Session> sessions, List<User> users,
    List<Channel> channels, int count) {
  final messages = <Message>[];
  for (int i = 0; i < count; i++) {
    final message = Message.create(
      workspaceId: 'workspace_1',
      channelId: 'perf_channel_${i % channels.length}',
      messageId: 'perf_msg_$i',
      sessionKey: 'perf_session_${i % sessions.length}',
      userId: 'perf_user_${i % users.length}',
      content: 'Performance test message $i with some content to test',
    );
    // Set relationships
    message.channel.target = channels[i % channels.length];
    message.sender.target = users[i % users.length];
    messages.add(message);
  }
  return messages;
}

/// Create test attachments with message relationships
List<Attachment> _createTestAttachments(List<Message> messages, int count) {
  final attachments = <Attachment>[];
  for (int i = 0; i < count; i++) {
    final attachment = Attachment.create(
      attachmentId: 'perf_attachment_$i',
      messageId: 'perf_msg_${i % messages.length}',
      ref: 'attachment_ref_$i',
      photoRaw:
          '{"url": "https://example.com/photo_$i.jpg", "width": 800, "height": 600}',
    );
    // Set relationship
    attachment.message.target = messages[i % messages.length];
    attachments.add(attachment);
  }
  return attachments;
}

/// Create test histories with session relationships
List<History> _createTestHistories(List<Session> sessions, int count) {
  final histories = <History>[];
  final actions = ['create', 'update', 'delete', 'read'];
  final entityTypes = ['message', 'channel', 'user', 'attachment'];

  for (int i = 0; i < count; i++) {
    final history = History.create(
      sessionKey: 'perf_session_${i % sessions.length}',
      historyId: 'perf_hist_$i',
      entityType: entityTypes[i % entityTypes.length],
      entityId: 'entity_$i',
      action: actions[i % actions.length],
    );
    // Set relationship
    history.session.target = sessions[i % sessions.length];
    histories.add(history);
  }
  return histories;
}

/// Create test collections with session relationships
List<Collection> _createTestCollections(List<Session> sessions, int count) {
  final collections = <Collection>[];
  for (int i = 0; i < count; i++) {
    final collection = Collection.create(
      sessionKey: 'perf_session_${i % sessions.length}',
      collectionId: 'perf_collection_$i',
      name: 'Collection $i',
      description: 'Performance test collection $i',
      thumbnailUrl: 'https://example.com/collection_$i.jpg',
      isPremium: i % 3 == 0,
      stickerCount: Random().nextInt(50) + 1,
    );
    // Set relationship
    collection.session.target = sessions[i % sessions.length];
    collections.add(collection);
  }
  return collections;
}

/// Create test stickers with collection relationships
List<Sticker> _createTestStickers(List<Collection> collections, int count) {
  final stickers = <Sticker>[];
  for (int i = 0; i < count; i++) {
    final sticker = Sticker.create(
      sessionKey: 'perf_session_${i % 100}',
      stickerId: 'perf_sticker_$i',
      collectionId: 'perf_collection_${i % collections.length}',
      name: 'Sticker $i',
      url: 'https://example.com/sticker_$i.png',
      thumbnailUrl: 'https://example.com/sticker_thumb_$i.png',
      width: Random().nextInt(200) + 100,
      height: Random().nextInt(200) + 100,
      isAnimated: i % 4 == 0,
      frameCount: i % 4 == 0 ? Random().nextInt(20) + 1 : 1,
    );
    // Set relationship
    sticker.collection.target = collections[i % collections.length];
    stickers.add(sticker);
  }
  return stickers;
}

/// Get current memory usage in MB
double _getCurrentMemoryUsage() {
  // Simple memory usage estimation
  // In a real implementation, you might use dart:developer or platform-specific APIs
  try {
    return ProcessInfo.currentRss / (1024 * 1024);
  } catch (e) {
    // Fallback if ProcessInfo is not available
    return 0.0;
  }
}

/// Get database file size in KB
double _getDatabaseSize() {
  try {
    final dbFiles = tempDir.listSync().where(
        (file) => file.path.endsWith('.mdb') || file.path.endsWith('.lck'));

    int totalSize = 0;
    for (final file in dbFiles) {
      if (file is File) {
        totalSize += file.lengthSync();
      }
    }

    return totalSize / 1024; // Convert to KB
  } catch (e) {
    return 0.0;
  }
}

/// Print comprehensive performance report
void _printPerformanceReport(Map<String, Map<String, dynamic>> results) {
  print('\n' + '=' * 80);
  print('📊 COMPREHENSIVE PERFORMANCE REPORT');
  print('=' * 80);

  if (results.isEmpty) {
    print('❌ No performance data collected');
    return;
  }

  // Group results by operation type
  final groupedResults =
      <String, List<MapEntry<String, Map<String, dynamic>>>>{};

  for (final entry in results.entries) {
    final operation = entry.value['operation'] as String;
    groupedResults.putIfAbsent(operation, () => []).add(entry);
  }

  // Print results by operation type
  for (final operationType in groupedResults.keys) {
    print('\n🔥 $operationType Performance');
    print('-' * 60);

    // Print table header
    print('Entity'.padRight(20) +
        'Scale'.padRight(10) +
        'Time(ms)'.padRight(12) +
        'Ops/sec'.padRight(10) +
        'Memory(MB)'.padRight(12) +
        'DB Size(KB)');
    print('-' * 60);

    // Sort by scale for better readability
    final sortedResults = groupedResults[operationType]!;
    sortedResults.sort((a, b) {
      final entityA = a.value['entity'] as String;
      final entityB = b.value['entity'] as String;
      if (entityA != entityB) return entityA.compareTo(entityB);

      final scaleA = a.value['scale'];
      final scaleB = b.value['scale'];
      if (scaleA is int && scaleB is int) {
        return scaleA.compareTo(scaleB);
      }
      return 0;
    });

    // Print each result
    for (final entry in sortedResults) {
      final data = entry.value;
      final entity = (data['entity'] as String).padRight(20);
      final scale = data['scale'].toString().padRight(10);
      final time = data['execution_time_ms'].toString().padRight(12);
      final throughput = data['throughput_ops_sec'].toString().padRight(10);
      final memory =
          (data['memory_used_mb'] as double).toStringAsFixed(2).padRight(12);
      final dbSize = (data['db_size_kb'] as double).toStringAsFixed(2);

      print('$entity$scale$time$throughput$memory$dbSize');
    }
  }

  // Print summary statistics
  _printSummaryStatistics(results);

  print('\n' + '=' * 80);
  print('✅ Performance test completed successfully!');
  print('📁 Database location: ${tempDir.path}');
  print('⏰ Test completed at: ${DateTime.now()}');
  print('=' * 80);
}

/// Print summary statistics
void _printSummaryStatistics(Map<String, Map<String, dynamic>> results) {
  print('\n📈 SUMMARY STATISTICS');
  print('-' * 40);

  // Calculate averages by operation
  final operationStats = <String, List<double>>{};

  for (final result in results.values) {
    final operation = result['operation'] as String;
    final timeValue = result['execution_time_ms'];
    if (timeValue != null) {
      final time = (timeValue as int).toDouble();
      operationStats.putIfAbsent(operation, () => []).add(time);
    }
  }

  for (final entry in operationStats.entries) {
    final operation = entry.key;
    final times = entry.value;

    final avgTime = times.reduce((a, b) => a + b) / times.length;
    final minTime = times.reduce((a, b) => a < b ? a : b);
    final maxTime = times.reduce((a, b) => a > b ? a : b);

    print('$operation:');
    print('  Average: ${avgTime.toStringAsFixed(2)}ms');
    print('  Min: ${minTime.toStringAsFixed(2)}ms');
    print('  Max: ${maxTime.toStringAsFixed(2)}ms');
    print('  Tests: ${times.length}');
    print('');
  }

  // Overall statistics
  final allTimes = results.values
      .where((r) => r['execution_time_ms'] != null)
      .map((r) => (r['execution_time_ms'] as int).toDouble())
      .toList();

  if (allTimes.isNotEmpty) {
    final totalTime = allTimes.reduce((a, b) => a + b);
    final avgTime = totalTime / allTimes.length;

    print('Overall Performance:');
    print('  Total tests: ${results.length}');
    print('  Total time: ${totalTime.toStringAsFixed(2)}ms');
    print('  Average time: ${avgTime.toStringAsFixed(2)}ms');
  }
}
