# 🧪 Flutter Data Router - Comprehensive Test Suite

Comprehensive test coverage for the Flutter Data Router project with **100+ test cases** across multiple categories including database integration, unit tests, performance testing, and relationship validation.

## 📊 Test Overview

### 🎯 **Test Coverage Statistics**
- **23 Entities** with ObjectBox relationships
- **100+ Test Cases** across all categories
- **50+ ToOne/ToMany Relationships** tested
- **30 Performance Tests** with metrics
- **Multiple Test Categories** (Unit, Integration, Performance)

### 🗂️ **Test Structure**
```
test/
├── entities/                    # Entity-specific integration tests
│   ├── session/                # Session entity tests
│   ├── user/                   # User entity tests
│   ├── message/                # Message entity tests
│   ├── attachment/             # Attachment entity tests
│   ├── channel/                # Channel entity tests
│   ├── collection/             # Collection entity tests
│   ├── sticker/                # Sticker entity tests
│   ├── member/                 # Member entity tests
│   ├── call/                   # Call entity tests
│   ├── translated_result/      # Translation entity tests
│   ├── user_private_data/      # User private data tests
│   ├── visited_profile/        # Visited profile tests
│   ├── session_local_metadata/ # Session metadata tests
│   └── metadata/               # Metadata entities tests
├── unit/                       # Unit tests
│   ├── core/                   # Core business logic tests
│   ├── data/                   # Data layer tests
│   └── presentation/           # Presentation layer tests
├── performance/                # Performance tests
│   ├── database_performance_test.dart
│   └── README.md
├── integration/                # Integration tests
├── fixtures/                   # Test data fixtures
├── helpers/                    # Test helper utilities
└── database_integration_test_suite.dart
```

## 🚀 Quick Start

### **Prerequisites**
```bash
# Setup test environment
make setup-test
```

### **Run All Tests**
```bash
# Run comprehensive database test suite (Recommended)
make test-db

# Run all database tests with expanded output
make test-db-all

# Run individual entity tests with summary
make test-db-individual
```

## 📋 Available Test Commands

### 🗄️ **Database Tests**

#### **Comprehensive Test Suite**
```bash
# Run main database test suite (Default)
make test-db
make test-db-comprehensive

# Output: Expanded format with detailed results
# Coverage: All entities and relationships
```

#### **Individual Entity Tests**
```bash
# Run all individual entity tests with summary
make test-db-individual

# Output: Compact format with progress indicators
# Coverage: Each entity tested separately
```

#### **All Database Tests**
```bash
# Run all database-related tests
make test-db-all

# Output: Expanded format
# Coverage: All test files in test/entities/
```

### 🎯 **Specific Entity Tests**

#### **Single Entity Testing**
```bash
# Run specific entity tests
make test-entity ENTITY=<entity_name>

# Examples:
make test-entity ENTITY=user
make test-entity ENTITY=message
make test-entity ENTITY=attachment
make test-entity ENTITY=session
make test-entity ENTITY=channel
make test-entity ENTITY=collection
make test-entity ENTITY=sticker
make test-entity ENTITY=member
```

#### **Entity Group Testing**
```bash
# User Management Entities
make test-user-entities
# Tests: user, user_private_data, visited_profile

# Content & Media Entities
make test-content-entities
# Tests: collection, sticker

# Messaging Entities
make test-messaging-entities
# Tests: message, attachment, translated_result

# Communication Entities
make test-communication-entities
# Tests: call, channel, member
```

### ⚡ **Performance Tests**

#### **Database Performance Testing**
```bash
# Run comprehensive performance tests
make test-db-performance

# Features:
# - Bulk insert/update/delete performance
# - Complex query performance
# - Memory usage testing
# - Concurrent operations testing
# - Detailed metrics and reporting
```

#### **Legacy Performance Testing**
```bash
# Run legacy performance tests with JSON output
make test-db-performance-legacy

# Output: test_results.json file
# Format: JSON format for analysis
```

### 📊 **Test Coverage**

#### **Coverage Analysis**
```bash
# Run tests with coverage analysis
make test-db-coverage

# Output:
# - LCOV coverage data
# - HTML coverage report in coverage/html/
# - Detailed coverage metrics
```

### �️ **Test Environment Management**

#### **Setup Test Environment**
```bash
# Setup and prepare test environment
make setup-test

# Actions:
# - Install dependencies (flutter pub get)
# - Generate ObjectBox code (build_runner)
# - Prepare test database
```

#### **Clean Test Artifacts**
```bash
# Clean test artifacts and reset environment
make clean-test

# Actions:
# - Clean Flutter build cache
# - Reset dependencies
# - Remove temporary test files
```

## 🎯 **Direct Flutter Commands**

### **Basic Flutter Test Commands**
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/entities/user/user_objectbox_integration_test.dart

# Run tests with different reporters
flutter test --reporter=compact    # Compact output
flutter test --reporter=expanded   # Detailed output
flutter test --reporter=json       # JSON output

# Run tests with verbose output
flutter test -v

# Run tests with coverage
flutter test --coverage
```

### **Advanced Flutter Test Options**
```bash
# Run tests matching pattern
flutter test --name="User Entity"

# Run tests with custom timeout
flutter test --timeout=60s

# Run tests on specific platform
flutter test --platform=vm

# Run tests with custom concurrency
flutter test --concurrency=1
```

## 📈 **Test Categories Explained**

### 🗄️ **Database Integration Tests**
**Location**: `test/entities/`
**Purpose**: Test ObjectBox database operations and relationships
**Coverage**:
- CRUD operations for all entities
- ToOne and ToMany relationship validation
- Data integrity and constraints
- Cross-entity relationship testing

**Example**:
```bash
flutter test test/entities/user/user_objectbox_integration_test.dart
```

### 🧪 **Unit Tests**
**Location**: `test/unit/`
**Purpose**: Test individual components in isolation
**Coverage**:
- Core business logic (`test/unit/core/`)
- Data layer components (`test/unit/data/`)
- Presentation layer (`test/unit/presentation/`)

**Example**:
```bash
flutter test test/unit/core/entities/
flutter test test/unit/data/repositories/
```

### ⚡ **Performance Tests**
**Location**: `test/performance/`
**Purpose**: Measure and validate database performance
**Coverage**:
- Bulk operations (insert/update/delete)
- Complex queries with relationships
- Memory usage monitoring
- Concurrent operations
- Throughput and latency metrics

**Example**:
```bash
flutter test test/performance/database_performance_test.dart
```

### 🔗 **Integration Tests**
**Location**: `test/integration/`
**Purpose**: Test component interactions and workflows
**Coverage**:
- End-to-end workflows
- API integration
- WebSocket communication
- Cross-layer integration
```

## 📊 **Test Results and Reporting**

### **Understanding Test Output**

#### **Compact Reporter** (`--reporter=compact`)
```
✓ User Entity CRUD Operations
✓ User Entity Relationships
✓ User Entity Validation
```

#### **Expanded Reporter** (`--reporter=expanded`)
```
✓ User Entity CRUD Operations
  ✓ should create user successfully
  ✓ should read user by ID
  ✓ should update user properties
  ✓ should delete user
```

#### **JSON Reporter** (`--reporter=json`)
```json
{
  "testID": 1,
  "result": "success",
  "name": "User Entity CRUD Operations",
  "time": 1234
}
```

### **Performance Test Output**
```
🚀 ObjectBox Database Performance Test Suite
============================================================
📊 Testing scales: Small (100), Medium (1K), Large (10K)
⚡ Metrics: Time, Memory, Throughput, DB Size

Entity              Scale     Time(ms)    Ops/sec   Memory(MB)  DB Size(KB)
Session             10000     45          222222    7.88        2631.94
User                10000     62          161290    14.28       9815.94
Message             10000     67          149254    -6.80       10423.94
```

## 🧪 **Entity Test Categories**

### 👤 **User Management Entities**
- **User**: Core user entity with relationships to Profile, UserPresence, UserStatus
- **UserPrivateData**: Private user data with ToOne relationship to User
- **VisitedProfile**: Profile visit tracking with ToOne relationship to User

### 🎨 **Content & Media Entities**
- **Collection**: Sticker collections with ToMany relationship to Stickers
- **Sticker**: Individual stickers with ToOne relationship to Collection

### 📊 **Metadata & Management Entities**
- **SessionLocalMetadata**: Session metadata with ToOne relationship to Session
- **History**: Action history with ToOne relationship to Session
- **Manager**: Data management with ToOne relationship to Session
- **PrivateData**: Private session data with ToOne relationship to Session

### 📞 **Communication Entities**
- **CallLog**: Call records with ToOne relationships to Session and Users
- **CallLogPrivateData**: Private call data with ToOne relationship to CallLog
- **Channel**: Communication channels with ToOne relationship to Session
- **ChannelPrivateData**: Private channel data with ToOne relationship to Channel
- **Member**: Channel membership with ToOne relationships to Channel and User

### 💬 **Messaging Entities**
- **Message**: Chat messages with ToOne relationships to Session, Channel, User
- **Attachment**: Message attachments with ToOne relationship to Message (1:Many)
- **TranslatedResult**: Message translations with ToOne relationship to Message (1:1)

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Build Runner Issues**
```bash
# Clean and regenerate ObjectBox code
dart run build_runner clean
dart run build_runner build --delete-conflicting-outputs
```

#### **Dependency Issues**
```bash
# Reset dependencies
flutter clean
flutter pub get
```

#### **Test Database Issues**
```bash
# Clean test environment
make clean-test
make setup-test
```

#### **Memory Issues in Performance Tests**
```bash
# Run performance tests with reduced scale
flutter test test/performance/database_performance_test.dart -v
```

### **Debug Mode**
```bash
# Run tests with verbose output for debugging
flutter test -v test/entities/user/user_objectbox_integration_test.dart

# Run single test case
flutter test --name="should create user successfully"
```

## 🎯 **Best Practices**

### **Running Tests Efficiently**

1. **Start with Quick Tests**:
   ```bash
   make test-entity ENTITY=session  # Quick validation
   ```

2. **Run Comprehensive Suite**:
   ```bash
   make test-db  # Full validation
   ```

3. **Performance Validation**:
   ```bash
   make test-db-performance  # Performance metrics
   ```

### **Test Development Workflow**

1. **Setup Environment**:
   ```bash
   make setup-test
   ```

2. **Develop and Test**:
   ```bash
   make test-entity ENTITY=<your_entity>
   ```

3. **Validate Integration**:
   ```bash
   make test-db-individual
   ```

4. **Final Validation**:
   ```bash
   make test-db
   make test-db-performance
   ```

### **Continuous Integration**

```bash
# CI/CD Pipeline Commands
make setup-test           # Setup
make test-db             # Core tests
make test-db-performance # Performance validation
make test-db-coverage    # Coverage analysis
```

## � **Test Features**

### **Relationship Testing**
- ✅ ToOne relationships (Entity → Related Entity)
- ✅ ToMany relationships (Entity → List of Related Entities)
- ✅ Backlink relationships (Reverse relationships)
- ✅ Bidirectional relationships (Both directions)
- ✅ Hybrid approach (Field + Relationship)

### **Functionality Testing**
- ✅ Entity creation and persistence
- ✅ Relationship establishment and retrieval
- ✅ Helper method functionality
- ✅ Data integrity and consistency
- ✅ Relationship deletion handling
- ✅ Complex query scenarios

### **Integration Testing**
- ✅ Cross-entity relationship verification
- ✅ Complete lifecycle testing
- ✅ Performance and reliability
- ✅ Error handling and edge cases

## �🛠️ **Development Workflow**

### **Adding New Entity Tests**

1. **Create entity test file**:
```bash
mkdir -p test/entities/new_entity
touch test/entities/new_entity/new_entity_objectbox_integration_test.dart
```

2. **Implement test structure**:
```dart
import 'package:flutter_test/flutter_test.dart';
// ... imports

void main() {
  group('🔗 ObjectBox NewEntity Relationships Integration Tests', () {
    // Setup and teardown

    test('should create NewEntity with relationships', () {
      // Test implementation
    });

    test('Comprehensive Integration Test', () {
      // Comprehensive test
    });
  });
}
```

3. **Add to master test suite**:
```dart
import 'entities/new_entity/new_entity_objectbox_integration_test.dart' as new_entity_tests;

// Add to appropriate group
group('New Entity Tests', new_entity_tests.main);
```

### **Running Tests During Development**

```bash
# Quick test during development
make test-entity ENTITY=new_entity

# Full regression test
make test-db

# Performance monitoring
make test-db-performance
```

## 📞 **Support and Documentation**

### **Additional Resources**
- **Entity Documentation**: Check individual entity README files
- **Performance Analysis**: `test/performance/README.md`
- **ObjectBox Documentation**: [ObjectBox Flutter Docs](https://docs.objectbox.io/flutter)

### **Getting Help**
1. Check test output for specific error messages
2. Review entity relationship documentation
3. Run tests with verbose output (`-v`) for detailed logs
4. Check Makefile for available commands (`make help`)

### **Test Environment Issues**
For issues or questions about the test suite:
1. Check test output for specific error details
2. Verify ObjectBox model generation is up to date
3. Ensure all dependencies are properly installed
4. Run `make setup-test` to reset test environment

---

**Last Updated**: June 5, 2025
**Test Coverage**: 100+ test cases across 23 entities
**Status**: ✅ Comprehensive test suite ready for production

**🎉 Happy Testing! The database is production-ready with comprehensive test coverage!**
