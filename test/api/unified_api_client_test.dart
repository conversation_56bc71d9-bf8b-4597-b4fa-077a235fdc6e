import 'package:data_router/data_router.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('UnifiedApiClient', () {
    late UnifiedApiClient apiClient;

    setUp(() {
      apiClient = UnifiedApiClient.createMock();
    });

    tearDown(() async {
      await apiClient.dispose();
    });

    test('should initialize successfully', () async {
      // Act
      await apiClient.initialize();

      // Assert
      expect(apiClient.isInitialized, isTrue);
    });

    test('should provide auth service', () async {
      // Arrange
      await apiClient.initialize();

      // Act
      final authService = apiClient.auth;

      // Assert
      expect(authService, isNotNull);
      expect(authService.serviceName, equals('AuthService'));
    });

    test('should handle auth token management', () {
      // Arrange
      const testToken = 'test-token-123';

      // Act
      apiClient.setAuthToken(testToken);

      // Assert
      expect(apiClient.authToken, equals(testToken));
      expect(apiClient.isAuthenticated, isTrue);

      // Act - clear token
      apiClient.clearAuthToken();

      // Assert
      expect(apiClient.authToken, isNull);
      expect(apiClient.isAuthenticated, isFalse);
    });

    test('should dispose properly', () async {
      // Arrange
      await apiClient.initialize();
      expect(apiClient.isInitialized, isTrue);

      // Act
      await apiClient.dispose();

      // Assert
      expect(apiClient.isInitialized, isFalse);
    });
  });

  group('BaseResult', () {
    test('should create success result', () {
      // Arrange
      const testData = 'test data';

      // Act
      final result = BaseResult.success(testData);

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(testData));
      expect(result.error, isNull);
    });

    test('should create error result', () {
      // Arrange
      const errorMessage = 'Test error';
      const statusCode = 400;

      // Act
      final result = BaseResult<String>.error(errorMessage, statusCode);

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.data, isNull);
      expect(result.error, equals(errorMessage));
      expect(result.statusCode, equals(statusCode));
    });
  });

  group('API Exceptions', () {
    test('should create ApiException', () {
      // Arrange
      const message = 'API error';
      const statusCode = 500;

      // Act
      final exception = ApiException(message, statusCode: statusCode);

      // Assert
      expect(exception.message, equals(message));
      expect(exception.statusCode, equals(statusCode));
      expect(exception.toString(), contains(message));
      expect(exception.toString(), contains(statusCode.toString()));
    });

    test('should create AuthenticationException', () {
      // Arrange
      const message = 'Auth error';
      const statusCode = 401;

      // Act
      final exception =
          AuthenticationException(message, statusCode: statusCode);

      // Assert
      expect(exception.message, equals(message));
      expect(exception.statusCode, equals(statusCode));
      expect(exception, isA<ApiException>());
    });

    test('should create NetworkException', () {
      // Arrange
      const message = 'Network error';
      const originalError = 'Connection timeout';

      // Act
      final exception = NetworkException(message, originalError: originalError);

      // Assert
      expect(exception.message, equals(message));
      expect(exception.originalError, equals(originalError));
      expect(exception, isA<ApiException>());
    });

    test('should create ValidationException', () {
      // Arrange
      const message = 'Validation error';
      const fieldErrors = {
        'email': ['Invalid email format']
      };
      const statusCode = 422;

      // Act
      final exception = ValidationException(
        message,
        fieldErrors: fieldErrors,
        statusCode: statusCode,
      );

      // Assert
      expect(exception.message, equals(message));
      expect(exception.fieldErrors, equals(fieldErrors));
      expect(exception.statusCode, equals(statusCode));
      expect(exception, isA<ApiException>());
    });
  });
}
