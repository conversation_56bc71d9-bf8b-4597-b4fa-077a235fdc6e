import 'package:data_router/src/core/data_router.dart';
import 'package:data_router/src/data/adapters/user_adapter.dart';
import 'package:data_router/src/data/database/entities/user.dart';

/// Simple test to verify DataRouter functionality
Future<void> testDataRouter() async {
  print('🚀 Starting DataRouter test...\n');

  try {
    // 1. Create DataRouter instance
    final dataRouter = DataRouter();
    print('✅ Created DataRouter instance');

    // 2. Register User adapter
    dataRouter.registerAdapter<User>(UserAdapter());
    print('✅ Registered User adapter');

    // 3. Initialize DataRouter
    await dataRouter.init();
    print('✅ DataRouter initialized successfully');

    // 4. Show stats
    final stats = dataRouter.getStats();
    print('📊 DataRouter Stats:');
    stats.forEach((key, value) {
      print('   $key: $value');
    });

    // 5. Get user operation
    final userOp = await dataRouter.get<User>();
    print('✅ Got user operation');

    // 6. Test getUser functionality
    print('\n👤 Testing getUser functionality...');
    
    userOp.get('test_user_123').listen(
      (resource) {
        print('📡 Received resource: ${resource.state}');
        if (resource.hasData) {
          print('👤 User data: ${resource.data!.username}');
        }
        if (resource.hasError) {
          print('❌ Error: ${resource.error}');
        }
      },
      onError: (error) {
        print('💥 Stream error: $error');
      },
    );

    // 7. Test getAllUsers functionality
    print('\n👥 Testing getAllUsers functionality...');
    
    userOp.getAll().listen(
      (resource) {
        print('📡 Received all users resource: ${resource.state}');
        if (resource.hasData) {
          print('👥 Users count: ${resource.data!.length}');
        }
        if (resource.hasError) {
          print('❌ Error: ${resource.error}');
        }
      },
      onError: (error) {
        print('💥 Stream error: $error');
      },
    );

    // 8. Wait a bit for async operations
    await Future.delayed(const Duration(seconds: 2));

    // 9. Test create user
    print('\n➕ Testing create user...');
    try {
      final newUser = User.create(
        userId: 'test_user_new',
        username: 'Test User',
        sessionKey: 'test_session_key',
      );

      final created = await userOp.create(newUser);
      print('✅ Created user: ${created.username}');
    } catch (e) {
      print('⚠️ Create user failed (expected): $e');
    }

    // 10. Cleanup
    await dataRouter.dispose();
    print('\n✅ DataRouter disposed successfully');

    print('\n🎉 DataRouter test completed successfully!');

  } catch (e) {
    print('❌ DataRouter test failed: $e');
    rethrow;
  }
}

/// Main function to run the test
Future<void> main() async {
  await testDataRouter();
}
