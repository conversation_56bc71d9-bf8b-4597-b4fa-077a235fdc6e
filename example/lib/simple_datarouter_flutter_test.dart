import 'package:flutter/material.dart';
import 'package:data_router/src/core/data_router.dart';
import 'package:data_router/src/data/adapters/user_adapter.dart';
import 'package:data_router/src/data/database/entities/user.dart';

void main() {
  runApp(const DataRouterTestApp());
}

class DataRouterTestApp extends StatelessWidget {
  const DataRouterTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DataRouter Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const DataRouterTestPage(),
    );
  }
}

class DataRouterTestPage extends StatefulWidget {
  const DataRouterTestPage({super.key});

  @override
  State<DataRouterTestPage> createState() => _DataRouterTestPageState();
}

class _DataRouterTestPageState extends State<DataRouterTestPage> {
  final List<String> _logs = [];
  bool _isInitialized = false;
  DataRouter? _dataRouter;

  @override
  void initState() {
    super.initState();
    _initializeDataRouter();
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toIso8601String().substring(11, 19)}: $message');
    });
    print(message);
  }

  Future<void> _initializeDataRouter() async {
    try {
      _addLog('🚀 Starting DataRouter initialization...');

      // 1. Create DataRouter instance
      _dataRouter = DataRouter();
      _addLog('✅ Created DataRouter instance');

      // 2. Register User adapter
      _dataRouter!.registerAdapter<User>(UserAdapter());
      _addLog('✅ Registered User adapter');

      // 3. Initialize DataRouter
      await _dataRouter!.init();
      _addLog('✅ DataRouter initialized successfully');

      // 4. Show stats
      final stats = _dataRouter!.getStats();
      _addLog('📊 DataRouter Stats: ${stats.length} adapters');

      setState(() {
        _isInitialized = true;
      });

      _addLog('🎉 DataRouter ready for use!');

    } catch (e) {
      _addLog('❌ DataRouter initialization failed: $e');
    }
  }

  Future<void> _testUserOperations() async {
    if (!_isInitialized || _dataRouter == null) {
      _addLog('⚠️ DataRouter not initialized');
      return;
    }

    try {
      _addLog('👤 Testing User operations...');

      // Get user operation
      final userOp = await _dataRouter!.get<User>();
      _addLog('✅ Got user operation');

      // Test get user
      _addLog('📡 Testing get user...');
      userOp.get('test_user_123').listen(
        (resource) {
          _addLog('📡 Get user result: ${resource.state}');
          if (resource.hasData) {
            _addLog('👤 User: ${resource.data!.username}');
          }
          if (resource.hasError) {
            _addLog('❌ Get user error: ${resource.error}');
          }
        },
        onError: (error) {
          _addLog('💥 Get user stream error: $error');
        },
      );

      // Test get all users
      _addLog('📡 Testing get all users...');
      userOp.getAll().listen(
        (resource) {
          _addLog('📡 Get all users result: ${resource.state}');
          if (resource.hasData) {
            _addLog('👥 Users count: ${resource.data!.length}');
          }
          if (resource.hasError) {
            _addLog('❌ Get all users error: ${resource.error}');
          }
        },
        onError: (error) {
          _addLog('💥 Get all users stream error: $error');
        },
      );

      // Wait a bit for async operations
      await Future.delayed(const Duration(seconds: 1));

      // Test create user
      _addLog('➕ Testing create user...');
      try {
        final newUser = User.create(
          userId: 'test_user_new',
          username: 'Test User',
          sessionKey: 'test_session_key',
        );

        final created = await userOp.create(newUser);
        _addLog('✅ Created user: ${created.username}');
      } catch (e) {
        _addLog('⚠️ Create user failed (expected): $e');
      }

    } catch (e) {
      _addLog('❌ User operations test failed: $e');
    }
  }

  @override
  void dispose() {
    _dataRouter?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DataRouter Test'),
        backgroundColor: _isInitialized ? Colors.green : Colors.orange,
      ),
      body: Column(
        children: [
          // Status
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _isInitialized ? Colors.green.shade100 : Colors.orange.shade100,
            child: Text(
              _isInitialized ? '✅ DataRouter Initialized' : '⏳ Initializing DataRouter...',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isInitialized ? _testUserOperations : null,
                    child: const Text('Test User Operations'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _logs.clear();
                      });
                    },
                    child: const Text('Clear Logs'),
                  ),
                ),
              ],
            ),
          ),
          
          // Logs
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _logs.length,
                itemBuilder: (context, index) {
                  final log = _logs[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      log,
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                        color: log.contains('❌') || log.contains('💥') 
                            ? Colors.red 
                            : log.contains('✅') || log.contains('🎉')
                                ? Colors.green
                                : Colors.black87,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
