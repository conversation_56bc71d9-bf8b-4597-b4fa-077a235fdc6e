import 'package:flutter/material.dart';
import 'package:data_router/src/core/data_router.dart';
import 'package:data_router/src/data/adapters/user_adapter.dart';
import 'package:data_router/src/data/database/entities/user.dart';
import 'package:data_router/src/data/models/resource.dart';

/// Simple DataRouter example demonstrating getUser functionality
class DataRouterExample extends StatefulWidget {
  const DataRouterExample({super.key});

  @override
  State<DataRouterExample> createState() => _DataRouterExampleState();
}

class _DataRouterExampleState extends State<DataRouterExample> {
  final DataRouter _dataRouter = DataRouter();
  bool _isInitialized = false;
  String _initError = '';
  String _output = '';

  @override
  void initState() {
    super.initState();
    _initializeDataRouter();
  }

  Future<void> _initializeDataRouter() async {
    try {
      _addOutput('🚀 Initializing DataRouter...');
      
      // Register User adapter
      _dataRouter.registerAdapter<User>(UserAdapter());
      _addOutput('✅ Registered User adapter');
      
      // Initialize DataRouter
      await _dataRouter.init();
      _addOutput('✅ DataRouter initialized successfully');
      
      setState(() {
        _isInitialized = true;
      });
      
      // Show stats
      final stats = _dataRouter.getStats();
      _addOutput('📊 DataRouter Stats: $stats');
      
    } catch (e) {
      setState(() {
        _initError = e.toString();
      });
      _addOutput('❌ Failed to initialize DataRouter: $e');
    }
  }

  Future<void> _demonstrateGetUser() async {
    if (!_isInitialized) {
      _addOutput('❌ DataRouter not initialized');
      return;
    }

    try {
      _addOutput('\n👤 === GET USER EXAMPLE ===');
      
      final userOp = await _dataRouter.get<User>();
      _addOutput('✅ Got user operation');

      // Create a test user first
      _addOutput('📝 Creating test user...');
      final testUser = User.create(
        userId: 'test_user_123',
        username: 'Test User',
        sessionKey: 'test_session_key',
      );

      try {
        await userOp.create(testUser);
        _addOutput('✅ Test user created');
      } catch (e) {
        _addOutput('⚠️ Create user failed (expected): $e');
      }

      // Now try to get the user
      _addOutput('🔍 Getting user test_user_123...');
      
      userOp.get('test_user_123').listen(
        (resource) {
          switch (resource.state) {
            case DataState.loading:
              if (resource.hasData) {
                _addOutput('⏳ Loading... (showing cached data)');
                _addOutput('💾 Cached user: ${resource.data!.username}');
              } else {
                _addOutput('⏳ Loading from remote...');
              }
              break;

            case DataState.success:
              if (resource.isFromCache) {
                _addOutput('💾 Success (from cache):');
              } else {
                _addOutput('🌐 Success (from remote):');
              }
              _addOutput('👤 User: ${resource.data!.username} (${resource.data!.userId})');
              break;

            case DataState.error:
              _addOutput('❌ Error: ${resource.error}');
              if (resource.hasData) {
                _addOutput('   Using cached data: ${resource.data!.username}');
              }
              break;

            case DataState.empty:
              _addOutput('📭 No user found');
              break;
          }
        },
        onError: (error) {
          _addOutput('💥 Stream error: $error');
        },
      );

    } catch (e) {
      _addOutput('❌ Get user failed: $e');
    }
  }

  Future<void> _demonstrateGetAllUsers() async {
    if (!_isInitialized) {
      _addOutput('❌ DataRouter not initialized');
      return;
    }

    try {
      _addOutput('\n👥 === GET ALL USERS EXAMPLE ===');
      
      final userOp = await _dataRouter.get<User>();
      _addOutput('✅ Got user operation');

      _addOutput('🔍 Getting all users...');
      
      userOp.getAll().listen(
        (resource) {
          switch (resource.state) {
            case DataState.loading:
              if (resource.hasData) {
                _addOutput('⏳ Loading... (showing ${resource.data!.length} cached users)');
              } else {
                _addOutput('⏳ Loading all users from remote...');
              }
              break;

            case DataState.success:
              final users = resource.data!;
              if (resource.isFromCache) {
                _addOutput('💾 Success (from cache): ${users.length} users');
              } else {
                _addOutput('🌐 Success (from remote): ${users.length} users');
              }
              
              for (int i = 0; i < users.length; i++) {
                _addOutput('   ${i + 1}. ${users[i].username} (${users[i].userId})');
              }
              break;

            case DataState.error:
              _addOutput('❌ Error: ${resource.error}');
              if (resource.hasData) {
                _addOutput('   Using ${resource.data!.length} cached users');
              }
              break;

            case DataState.empty:
              _addOutput('📭 No users found');
              break;
          }
        },
        onError: (error) {
          _addOutput('💥 Stream error: $error');
        },
      );

    } catch (e) {
      _addOutput('❌ Get all users failed: $e');
    }
  }

  void _addOutput(String message) {
    setState(() {
      _output += '$message\n';
    });
    print(message);
  }

  void _clearOutput() {
    setState(() {
      _output = '';
    });
  }

  @override
  void dispose() {
    _dataRouter.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DataRouter Example'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // Status bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _isInitialized 
                ? Colors.green.withOpacity(0.1) 
                : _initError.isNotEmpty 
                    ? Colors.red.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
            child: Row(
              children: [
                Icon(
                  _isInitialized 
                      ? Icons.check_circle 
                      : _initError.isNotEmpty 
                          ? Icons.error 
                          : Icons.hourglass_empty,
                  color: _isInitialized 
                      ? Colors.green 
                      : _initError.isNotEmpty 
                          ? Colors.red 
                          : Colors.orange,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _isInitialized 
                        ? 'DataRouter Ready' 
                        : _initError.isNotEmpty 
                            ? 'Error: $_initError'
                            : 'Initializing...',
                    style: TextStyle(
                      color: _isInitialized 
                          ? Colors.green 
                          : _initError.isNotEmpty 
                              ? Colors.red 
                              : Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isInitialized ? _demonstrateGetUser : null,
                    child: const Text('Get User'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isInitialized ? _demonstrateGetAllUsers : null,
                    child: const Text('Get All Users'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _clearOutput,
                  child: const Icon(Icons.clear),
                ),
              ],
            ),
          ),
          
          // Output console
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _output.isEmpty ? 'Output will appear here...' : _output,
                  style: const TextStyle(
                    color: Colors.green,
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Simple demo app to run the DataRouter example
class DataRouterDemoApp extends StatelessWidget {
  const DataRouterDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DataRouter Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const DataRouterExample(),
    );
  }
}

/// Main function to run the demo
void main() {
  runApp(const DataRouterDemoApp());
}
