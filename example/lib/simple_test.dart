import 'dart:async';

/// Simple test to verify core DataRouter functionality without Flutter dependencies
Future<void> testDataRouterCore() async {
  print('🚀 Starting DataRouter core test...\n');

  try {
    // Test 1: Basic imports
    print('✅ Testing imports...');

    // Test basic Dart functionality
    final testMap = <String, dynamic>{
      'id': 'test123',
      'name': 'Test User',
      'email': '<EMAIL>',
    };

    print('✅ Basic Map operations work: ${testMap['name']}');

    // Test async operations
    await Future.delayed(const Duration(milliseconds: 100));
    print('✅ Async operations work');

    // Test stream operations
    final controller = StreamController<String>();
    controller.add('test message');

    final subscription = controller.stream.listen((message) {
      print('✅ Stream operations work: $message');
    });

    await Future.delayed(const Duration(milliseconds: 100));
    await subscription.cancel();
    await controller.close();

    print('\n🎉 Core functionality test completed successfully!');
    print('📝 Note: Full DataRouter test requires Flutter environment');
  } catch (e) {
    print('❌ Core test failed: $e');
    rethrow;
  }
}

/// Main function to run the test
Future<void> main() async {
  await testDataRouterCore();
}
