# 🚀 **QUICK START: DataRouter Migration**

## 📋 **IMMEDIATE ACTION PLAN**

Dựa trên phân tích architecture alignment, đ<PERSON><PERSON> là kế hoạch hành động ngay lập tức để bắt đầu migration sang DataRouter architecture.

## ⚡ **TODAY: Setup Foundation (2-3 hours)**

### **Step 1: Create New Directory Structure**
```bash
# Create new directories
mkdir -p lib/src/core/interfaces
mkdir -p lib/src/core/operations  
mkdir -p lib/src/data/sources
mkdir -p lib/src/data/adapters
mkdir -p lib/src/data/models
mkdir -p lib/src/legacy

# Move current API code to legacy
mv lib/src/data/api lib/src/legacy/api
```

### **Step 2: Create Core Interfaces**

#### **ModelAdapter Interface:**
```dart
// lib/src/core/interfaces/model_adapter.dart
abstract class ModelAdapter<T> {
  String get tableName;
  String get endpoint;
  String get socketChannel;
  
  T fromMap(Map<String, dynamic> map);
  Map<String, dynamic> toMap(T model);
  String getId(T model);
  
  bool validate(T model) => true;
}
```

#### **DataSource Interfaces:**
```dart
// lib/src/core/interfaces/data_source.dart
abstract class LocalDataSource {
  Future<void> init();
  Future<T?> get<T>(String id, ModelAdapter<T> adapter);
  Future<List<T>> getAll<T>(ModelAdapter<T> adapter);
  Future<void> save<T>(T item, ModelAdapter<T> adapter);
  Future<void> delete<T>(String id, ModelAdapter<T> adapter);
  Stream<T?> watch<T>(String id, ModelAdapter<T> adapter);
}

abstract class RemoteDataSource {
  Future<void> init();
  Future<T?> fetch<T>(String id, ModelAdapter<T> adapter);
  Future<List<T>> fetchAll<T>(ModelAdapter<T> adapter);
  Future<T> create<T>(T item, ModelAdapter<T> adapter);
  Future<T> update<T>(T item, ModelAdapter<T> adapter);
  Future<void> delete<T>(String id, ModelAdapter<T> adapter);
}

abstract class WebSocketDataSource {
  Future<void> init();
  Stream<T> subscribe<T>(String id, ModelAdapter<T> adapter);
  void unsubscribe<T>(String id, ModelAdapter<T> adapter);
}
```

#### **DataOperation Interface:**
```dart
// lib/src/core/interfaces/data_operation.dart
abstract class DataOperation<T> {
  Stream<Resource<T?>> get(String id);
  Stream<Resource<List<T>>> getAll();
  Stream<T> subscribeUpdate(String id);
  Future<T> create(T item);
  Future<T> update(T item);
  Future<void> delete(String id);
}
```

### **Step 3: Create Resource Model**
```dart
// lib/src/data/models/resource.dart
enum DataState { loading, success, error, empty }

class Resource<T> {
  final DataState state;
  final T? data;
  final String? error;
  final bool isFromCache;
  final DateTime timestamp;

  const Resource({
    required this.state,
    this.data,
    this.error,
    this.isFromCache = false,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory Resource.loading([T? data]) => Resource(
    state: DataState.loading,
    data: data,
    isFromCache: data != null,
  );

  factory Resource.success(T data, {bool isFromCache = false}) => Resource(
    state: DataState.success,
    data: data,
    isFromCache: isFromCache,
  );

  factory Resource.error(String error, [T? data]) => Resource(
    state: DataState.error,
    error: error,
    data: data,
    isFromCache: data != null,
  );

  factory Resource.empty() => const Resource(state: DataState.empty);

  bool get isLoading => state == DataState.loading;
  bool get isSuccess => state == DataState.success;
  bool get isError => state == DataState.error;
  bool get isEmpty => state == DataState.empty;
  bool get hasData => data != null;
}
```

## 🎯 **TOMORROW: DataRouter Core (4-5 hours)**

### **Step 4: Implement DataRouter Singleton**
```dart
// lib/src/core/data_router.dart
import 'interfaces/model_adapter.dart';
import 'interfaces/data_source.dart';
import 'interfaces/data_operation.dart';
import 'operations/data_operation_impl.dart';

class DataRouter {
  static final DataRouter _instance = DataRouter._internal();
  factory DataRouter() => _instance;
  DataRouter._internal();

  final Map<Type, ModelAdapter> _adapters = {};
  LocalDataSource? _localSource;
  RemoteDataSource? _remoteSource;
  WebSocketDataSource? _webSocketSource;
  
  bool _initialized = false;

  void registerAdapter<T>(ModelAdapter<T> adapter) {
    _adapters[T] = adapter;
  }

  Future<void> init({
    LocalDataSource? localSource,
    RemoteDataSource? remoteSource,
    WebSocketDataSource? webSocketSource,
  }) async {
    if (_initialized) return;
    
    _localSource = localSource ?? _createDefaultLocalSource();
    _remoteSource = remoteSource ?? _createDefaultRemoteSource();
    _webSocketSource = webSocketSource ?? _createDefaultWebSocketSource();
    
    await Future.wait([
      _localSource!.init(),
      _remoteSource!.init(),
      _webSocketSource!.init(),
    ]);
    
    _initialized = true;
  }

  Future<DataOperation<T>> get<T>() async {
    if (!_initialized) throw StateError('DataRouter not initialized');
    
    final adapter = _adapters[T] as ModelAdapter<T>?;
    if (adapter == null) {
      throw StateError('No adapter registered for type $T');
    }

    return DataOperationImpl<T>(
      adapter: adapter,
      localSource: _localSource!,
      remoteSource: _remoteSource!,
      webSocketSource: _webSocketSource!,
    );
  }

  // Factory methods for default implementations
  LocalDataSource _createDefaultLocalSource() {
    // TODO: Return actual implementation
    throw UnimplementedError('LocalDataSource not implemented yet');
  }

  RemoteDataSource _createDefaultRemoteSource() {
    // TODO: Return actual implementation  
    throw UnimplementedError('RemoteDataSource not implemented yet');
  }

  WebSocketDataSource _createDefaultWebSocketSource() {
    // TODO: Return actual implementation
    throw UnimplementedError('WebSocketDataSource not implemented yet');
  }
}
```

### **Step 5: Implement DataOperation**
```dart
// lib/src/core/operations/data_operation_impl.dart
import '../interfaces/data_operation.dart';
import '../interfaces/model_adapter.dart';
import '../interfaces/data_source.dart';
import '../../data/models/resource.dart';

class DataOperationImpl<T> implements DataOperation<T> {
  final ModelAdapter<T> adapter;
  final LocalDataSource localSource;
  final RemoteDataSource remoteSource;
  final WebSocketDataSource webSocketSource;

  DataOperationImpl({
    required this.adapter,
    required this.localSource,
    required this.remoteSource,
    required this.webSocketSource,
  });

  @override
  Stream<Resource<T?>> get(String id) async* {
    try {
      // 1. Try to get from local first
      final cached = await localSource.get(id, adapter);
      if (cached != null) {
        yield Resource.success(cached, isFromCache: true);
      } else {
        yield Resource.loading();
      }

      // 2. Fetch from remote
      final remote = await remoteSource.fetch(id, adapter);
      if (remote != null) {
        // 3. Save to local
        await localSource.save(remote, adapter);
        
        // 4. Emit fresh data
        yield Resource.success(remote, isFromCache: false);
      } else if (cached == null) {
        yield Resource.empty();
      }
    } catch (e) {
      final cached = await localSource.get(id, adapter);
      yield Resource.error(e.toString(), cached);
    }
  }

  @override
  Stream<Resource<List<T>>> getAll() async* {
    try {
      // 1. Get cached data first
      final cached = await localSource.getAll(adapter);
      if (cached.isNotEmpty) {
        yield Resource.success(cached, isFromCache: true);
      } else {
        yield Resource.loading();
      }

      // 2. Fetch from remote
      final remote = await remoteSource.fetchAll(adapter);
      if (remote.isNotEmpty) {
        // 3. Save to local
        for (final item in remote) {
          await localSource.save(item, adapter);
        }
        
        // 4. Emit fresh data
        yield Resource.success(remote, isFromCache: false);
      } else if (cached.isEmpty) {
        yield Resource.empty();
      }
    } catch (e) {
      final cached = await localSource.getAll(adapter);
      yield Resource.error(e.toString(), cached);
    }
  }

  @override
  Stream<T> subscribeUpdate(String id) {
    return webSocketSource.subscribe(id, adapter);
  }

  @override
  Future<T> create(T item) async {
    // 1. Create on remote
    final created = await remoteSource.create(item, adapter);
    
    // 2. Save to local
    await localSource.save(created, adapter);
    
    return created;
  }

  @override
  Future<T> update(T item) async {
    // 1. Update on remote
    final updated = await remoteSource.update(item, adapter);
    
    // 2. Save to local
    await localSource.save(updated, adapter);
    
    return updated;
  }

  @override
  Future<void> delete(String id) async {
    // 1. Delete from remote
    await remoteSource.delete(id, adapter);
    
    // 2. Delete from local
    await localSource.delete(id, adapter);
  }
}
```

## 📅 **THIS WEEK: First Working Example**

### **Step 6: Create Simple User Example**
```dart
// lib/src/data/models/user.dart
class User {
  final String id;
  final String name;
  final String email;

  const User({
    required this.id,
    required this.name,
    required this.email,
  });

  factory User.fromMap(Map<String, dynamic> map) => User(
    id: map['id'] as String,
    name: map['name'] as String,
    email: map['email'] as String,
  );

  Map<String, dynamic> toMap() => {
    'id': id,
    'name': name,
    'email': email,
  };
}

// lib/src/data/adapters/user_adapter.dart
import '../models/user.dart';
import '../../core/interfaces/model_adapter.dart';

class UserAdapter implements ModelAdapter<User> {
  @override
  String get tableName => 'users';

  @override
  String get endpoint => '/users';

  @override
  String get socketChannel => 'user_updates';

  @override
  User fromMap(Map<String, dynamic> map) => User.fromMap(map);

  @override
  Map<String, dynamic> toMap(User model) => model.toMap();

  @override
  String getId(User model) => model.id;
}
```

### **Step 7: Create Mock DataSources for Testing**
```dart
// lib/src/data/sources/mock_local_data_source.dart
class MockLocalDataSource implements LocalDataSource {
  final Map<String, Map<String, Map<String, dynamic>>> _storage = {};

  @override
  Future<void> init() async {
    // Mock initialization
  }

  @override
  Future<T?> get<T>(String id, ModelAdapter<T> adapter) async {
    final table = _storage[adapter.tableName];
    if (table == null) return null;
    
    final data = table[id];
    if (data == null) return null;
    
    return adapter.fromMap(data);
  }

  @override
  Future<List<T>> getAll<T>(ModelAdapter<T> adapter) async {
    final table = _storage[adapter.tableName];
    if (table == null) return [];
    
    return table.values.map((data) => adapter.fromMap(data)).toList();
  }

  @override
  Future<void> save<T>(T item, ModelAdapter<T> adapter) async {
    _storage.putIfAbsent(adapter.tableName, () => {});
    final id = adapter.getId(item);
    _storage[adapter.tableName]![id] = adapter.toMap(item);
  }

  @override
  Future<void> delete<T>(String id, ModelAdapter<T> adapter) async {
    _storage[adapter.tableName]?.remove(id);
  }

  @override
  Stream<T?> watch<T>(String id, ModelAdapter<T> adapter) {
    // TODO: Implement reactive watching
    throw UnimplementedError();
  }
}
```

### **Step 8: Test the Implementation**
```dart
// test/data_router_test.dart
void main() {
  group('DataRouter', () {
    late DataRouter dataRouter;

    setUp(() async {
      dataRouter = DataRouter();
      dataRouter.registerAdapter<User>(UserAdapter());
      
      await dataRouter.init(
        localSource: MockLocalDataSource(),
        remoteSource: MockRemoteDataSource(),
        webSocketSource: MockWebSocketDataSource(),
      );
    });

    test('should get user with local-first strategy', () async {
      final userOp = await dataRouter.get<User>();
      
      final stream = userOp.get('user123');
      final resources = await stream.take(2).toList();
      
      expect(resources.length, 2);
      expect(resources[0].isLoading, true);
      expect(resources[1].isSuccess, true);
    });
  });
}
```

## ✅ **VALIDATION CHECKLIST**

### **Foundation Setup:**
- [ ] New directory structure created
- [ ] Core interfaces implemented
- [ ] Resource model working
- [ ] DataRouter singleton created
- [ ] DataOperation implementation done

### **First Example:**
- [ ] User model and adapter created
- [ ] Mock data sources working
- [ ] Basic test passing
- [ ] Local-first flow working
- [ ] Error handling functional

### **Next Steps Ready:**
- [ ] Real data source implementations
- [ ] More entity adapters
- [ ] WebSocket integration
- [ ] Performance optimization

---

**Goal**: Có một working prototype của DataRouter trong 1-2 ngày để validate architecture trước khi invest thêm effort vào full implementation.
