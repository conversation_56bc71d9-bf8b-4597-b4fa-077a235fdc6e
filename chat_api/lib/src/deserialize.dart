import 'package:chat_api/src/model/call_signal_updated_event_data_recipient_info.dart';
import 'package:chat_api/src/model/common_assertion_response.dart';
import 'package:chat_api/src/model/common_assertion_result.dart';
import 'package:chat_api/src/model/common_attestation_response.dart';
import 'package:chat_api/src/model/common_attestation_result.dart';
import 'package:chat_api/src/model/common_auth_user.dart';
import 'package:chat_api/src/model/common_authenticator_selection_criteria.dart';
import 'package:chat_api/src/model/common_credential_request_options.dart';
import 'package:chat_api/src/model/common_public_key_cred_param.dart';
import 'package:chat_api/src/model/common_public_key_credential_creation_options.dart';
import 'package:chat_api/src/model/common_public_key_credential_descriptor.dart';
import 'package:chat_api/src/model/common_public_key_credential_request_options.dart';
import 'package:chat_api/src/model/common_relay_party.dart';
import 'package:chat_api/src/model/iamv3_terminate_session.dart';
import 'package:chat_api/src/model/initiate_file_collection_response_storage_space.dart';
import 'package:chat_api/src/model/initiate_multipart_upload_response_policies.dart';
import 'package:chat_api/src/model/login_request_smart_otp.dart';
import 'package:chat_api/src/model/message_reaction_updated_event_data_message_reaction_data.dart';
import 'package:chat_api/src/model/premium_settings_boosted.dart';
import 'package:chat_api/src/model/privacy_settings_restrict_saving_content.dart';
import 'package:chat_api/src/model/protobuf_any.dart';
import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/sharedv3_channel_data.dart';
import 'package:chat_api/src/model/stream_result_of_v3_speech_to_text_response.dart';
import 'package:chat_api/src/model/stream_result_of_v3_sync_all_channels_response.dart';
import 'package:chat_api/src/model/stream_result_of_v3_sync_all_friends_response.dart';
import 'package:chat_api/src/model/stream_result_of_v3_sync_dm_messages_response.dart';
import 'package:chat_api/src/model/stream_result_of_v3_sync_members_response.dart';
import 'package:chat_api/src/model/stream_result_of_v3_sync_messages_response.dart';
import 'package:chat_api/src/model/stream_result_of_v3_upload_part_response.dart';
import 'package:chat_api/src/model/sync_all_channels_response_channel_identification.dart';
import 'package:chat_api/src/model/sync_all_friends_response_friend_identification.dart';
import 'package:chat_api/src/model/sync_users_response_user_identification.dart';
import 'package:chat_api/src/model/user_setting_media_permission_setting.dart';
import 'package:chat_api/src/model/user_setting_privacy_setting.dart';
import 'package:chat_api/src/model/user_setting_recovery_code_setting.dart';
import 'package:chat_api/src/model/user_setting_security_key_setting.dart';
import 'package:chat_api/src/model/user_setting_security_setting.dart';
import 'package:chat_api/src/model/user_setting_session_expiration_setting.dart';
import 'package:chat_api/src/model/user_setting_smart_opt_setting.dart';
import 'package:chat_api/src/model/v3_accept_friend_request_request.dart';
import 'package:chat_api/src/model/v3_accept_friend_request_response.dart';
import 'package:chat_api/src/model/v3_accept_invitation_request.dart';
import 'package:chat_api/src/model/v3_accept_invitation_response.dart';
import 'package:chat_api/src/model/v3_accept_message_request_request.dart';
import 'package:chat_api/src/model/v3_accept_message_request_response.dart';
import 'package:chat_api/src/model/v3_accept_qr_auth_request.dart';
import 'package:chat_api/src/model/v3_accept_qr_auth_response.dart';
import 'package:chat_api/src/model/v3_add_cover_photo_request.dart';
import 'package:chat_api/src/model/v3_add_cover_photo_response.dart';
import 'package:chat_api/src/model/v3_add_dm_message_reaction_request.dart';
import 'package:chat_api/src/model/v3_add_dm_message_reaction_response.dart';
import 'package:chat_api/src/model/v3_add_friend_request.dart';
import 'package:chat_api/src/model/v3_add_friend_response.dart';
import 'package:chat_api/src/model/v3_add_message_reaction_request.dart';
import 'package:chat_api/src/model/v3_add_message_reaction_response.dart';
import 'package:chat_api/src/model/v3_add_user_status_request.dart';
import 'package:chat_api/src/model/v3_add_user_status_response.dart';
import 'package:chat_api/src/model/v3_all_messages_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_all_user_messages_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_assign_as_admin_request.dart';
import 'package:chat_api/src/model/v3_assign_as_admin_response.dart';
import 'package:chat_api/src/model/v3_audio_file_to_text_request.dart';
import 'package:chat_api/src/model/v3_audio_file_to_text_response.dart';
import 'package:chat_api/src/model/v3_audio_metadata.dart';
import 'package:chat_api/src/model/v3_audit_log.dart';
import 'package:chat_api/src/model/v3_avatar_frame_collection_data.dart';
import 'package:chat_api/src/model/v3_avatar_frame_created_event_data.dart';
import 'package:chat_api/src/model/v3_avatar_frame_data.dart';
import 'package:chat_api/src/model/v3_avatar_frame_decorated_data.dart';
import 'package:chat_api/src/model/v3_avatar_frame_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_badge_value_argument.dart';
import 'package:chat_api/src/model/v3_ban_from_channel_request.dart';
import 'package:chat_api/src/model/v3_ban_from_channel_response.dart';
import 'package:chat_api/src/model/v3_ban_user_request.dart';
import 'package:chat_api/src/model/v3_ban_user_response.dart';
import 'package:chat_api/src/model/v3_block_user_request.dart';
import 'package:chat_api/src/model/v3_block_user_response.dart';
import 'package:chat_api/src/model/v3_boost_channel_request.dart';
import 'package:chat_api/src/model/v3_boost_channel_response.dart';
import 'package:chat_api/src/model/v3_boost_dm_request.dart';
import 'package:chat_api/src/model/v3_boost_dm_response.dart';
import 'package:chat_api/src/model/v3_cache_data.dart';
import 'package:chat_api/src/model/v3_cache_data_embed.dart';
import 'package:chat_api/src/model/v3_call_created_event_data.dart';
import 'package:chat_api/src/model/v3_call_data.dart';
import 'package:chat_api/src/model/v3_call_log_sync_data.dart';
import 'package:chat_api/src/model/v3_call_signal_updated_event_data.dart';
import 'package:chat_api/src/model/v3_call_updated_event_data.dart';
import 'package:chat_api/src/model/v3_cancel_boost_channel_request.dart';
import 'package:chat_api/src/model/v3_cancel_boost_channel_response.dart';
import 'package:chat_api/src/model/v3_cancel_boost_dm_request.dart';
import 'package:chat_api/src/model/v3_cancel_boost_dm_response.dart';
import 'package:chat_api/src/model/v3_cancel_friend_request_request.dart';
import 'package:chat_api/src/model/v3_cancel_friend_request_response.dart';
import 'package:chat_api/src/model/v3_cancel_registration_request.dart';
import 'package:chat_api/src/model/v3_cancel_registration_response.dart';
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:chat_api/src/model/v3_channel_avatar_upload_failed_event_data.dart';
import 'package:chat_api/src/model/v3_channel_created_event_data.dart';
import 'package:chat_api/src/model/v3_channel_creation_completed_event_data.dart';
import 'package:chat_api/src/model/v3_channel_creation_failed_event_data.dart';
import 'package:chat_api/src/model/v3_channel_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_channel_destination_cloud_event.dart';
import 'package:chat_api/src/model/v3_channel_metadata.dart';
import 'package:chat_api/src/model/v3_channel_notification_status_updated_event_data.dart';
import 'package:chat_api/src/model/v3_channel_status.dart';
import 'package:chat_api/src/model/v3_channel_sync_data.dart';
import 'package:chat_api/src/model/v3_channel_typing_event_data.dart';
import 'package:chat_api/src/model/v3_channel_updated_event_data.dart';
import 'package:chat_api/src/model/v3_check_migrate_passkey_status_response.dart';
import 'package:chat_api/src/model/v3_clear_dm_message_for_everyone_response.dart';
import 'package:chat_api/src/model/v3_clear_user_visited_profile_notifications_response.dart';
import 'package:chat_api/src/model/v3_cloud_event.dart';
import 'package:chat_api/src/model/v3_codec.dart';
import 'package:chat_api/src/model/v3_complete_file_collection_request.dart';
import 'package:chat_api/src/model/v3_complete_file_collection_response.dart';
import 'package:chat_api/src/model/v3_complete_multipart_upload_request.dart';
import 'package:chat_api/src/model/v3_complete_multipart_upload_response.dart';
import 'package:chat_api/src/model/v3_completed_part.dart';
import 'package:chat_api/src/model/v3_confirm_account_deletion_by_security_key_request.dart';
import 'package:chat_api/src/model/v3_confirm_account_deletion_by_security_key_response.dart';
import 'package:chat_api/src/model/v3_confirm_account_deletion_request.dart';
import 'package:chat_api/src/model/v3_confirm_account_deletion_response.dart';
import 'package:chat_api/src/model/v3_confirm_recovery_code_generation_flow_request.dart';
import 'package:chat_api/src/model/v3_confirm_recovery_code_generation_flow_response.dart';
import 'package:chat_api/src/model/v3_confirm_view_security_key_request.dart';
import 'package:chat_api/src/model/v3_confirm_view_security_key_response.dart';
import 'package:chat_api/src/model/v3_connect_params.dart';
import 'package:chat_api/src/model/v3_contact.dart';
import 'package:chat_api/src/model/v3_cover_data.dart';
import 'package:chat_api/src/model/v3_cover_photo_created_event_data.dart';
import 'package:chat_api/src/model/v3_cover_photo_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_cover_photo_updated_data.dart';
import 'package:chat_api/src/model/v3_crawl_place_request.dart';
import 'package:chat_api/src/model/v3_crawl_place_response.dart';
import 'package:chat_api/src/model/v3_crawl_request.dart';
import 'package:chat_api/src/model/v3_crawl_response.dart';
import 'package:chat_api/src/model/v3_create_avatar_frame_request.dart';
import 'package:chat_api/src/model/v3_create_avatar_frame_response.dart';
import 'package:chat_api/src/model/v3_create_call_request.dart';
import 'package:chat_api/src/model/v3_create_call_response.dart';
import 'package:chat_api/src/model/v3_create_call_response_data.dart';
import 'package:chat_api/src/model/v3_create_channel_request.dart';
import 'package:chat_api/src/model/v3_create_channel_response.dart';
import 'package:chat_api/src/model/v3_create_invitation_request.dart';
import 'package:chat_api/src/model/v3_create_invitation_response.dart';
import 'package:chat_api/src/model/v3_dm_channel_created_event_data.dart';
import 'package:chat_api/src/model/v3_dm_channel_updated_event_data.dart';
import 'package:chat_api/src/model/v3_data_buffer_response.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_data_text_response.dart';
import 'package:chat_api/src/model/v3_decode_user_connect_link_request.dart';
import 'package:chat_api/src/model/v3_decode_user_connect_link_response.dart';
import 'package:chat_api/src/model/v3_decode_user_connect_link_response_data.dart';
import 'package:chat_api/src/model/v3_decorated_avatar_removed_event_data.dart';
import 'package:chat_api/src/model/v3_decorated_avatar_uploaded_event_data.dart';
import 'package:chat_api/src/model/v3_delete_all_dm_messages_for_everyone_response.dart';
import 'package:chat_api/src/model/v3_delete_all_dm_messages_only_me_response.dart';
import 'package:chat_api/src/model/v3_delete_all_messages_only_me_response.dart';
import 'package:chat_api/src/model/v3_delete_avatar_frame_response.dart';
import 'package:chat_api/src/model/v3_delete_channel_avatar_response.dart';
import 'package:chat_api/src/model/v3_delete_channel_response.dart';
import 'package:chat_api/src/model/v3_delete_cover_photo_response.dart';
import 'package:chat_api/src/model/v3_delete_dm_message_for_everyone_response.dart';
import 'package:chat_api/src/model/v3_delete_dm_message_only_me_response.dart';
import 'package:chat_api/src/model/v3_delete_dm_messages_for_everyone_response.dart';
import 'package:chat_api/src/model/v3_delete_dm_messages_only_me_response.dart';
import 'package:chat_api/src/model/v3_delete_file_response.dart';
import 'package:chat_api/src/model/v3_delete_friend_request_response.dart';
import 'package:chat_api/src/model/v3_delete_message_for_everyone_response.dart';
import 'package:chat_api/src/model/v3_delete_message_only_me_response.dart';
import 'package:chat_api/src/model/v3_delete_messages_for_everyone_response.dart';
import 'package:chat_api/src/model/v3_delete_messages_only_me_response.dart';
import 'package:chat_api/src/model/v3_delete_mocked_channels_response.dart';
import 'package:chat_api/src/model/v3_delete_mocked_users_response.dart';
import 'package:chat_api/src/model/v3_delete_user_avatar_response.dart';
import 'package:chat_api/src/model/v3_delete_user_status_response.dart';
import 'package:chat_api/src/model/v3_delete_user_video_avatar_response.dart';
import 'package:chat_api/src/model/v3_delete_user_visited_profile_event_data.dart';
import 'package:chat_api/src/model/v3_delete_user_visited_profile_response.dart';
import 'package:chat_api/src/model/v3_deletion_confirmation_payload.dart';
import 'package:chat_api/src/model/v3_device_info.dart';
import 'package:chat_api/src/model/v3_device_linked_event_data.dart';
import 'package:chat_api/src/model/v3_device_session.dart';
import 'package:chat_api/src/model/v3_device_unlinked_event_data.dart';
import 'package:chat_api/src/model/v3_dimensions.dart';
import 'package:chat_api/src/model/v3_dismiss_as_admin_request.dart';
import 'package:chat_api/src/model/v3_dismiss_as_admin_response.dart';
import 'package:chat_api/src/model/v3_embed.dart';
import 'package:chat_api/src/model/v3_embed_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_file_metadata.dart';
import 'package:chat_api/src/model/v3_file_upload_request.dart';
import 'package:chat_api/src/model/v3_file_upload_response.dart';
import 'package:chat_api/src/model/v3_file_uploaded_event_data.dart';
import 'package:chat_api/src/model/v3_forward_messages_to_channel_request.dart';
import 'package:chat_api/src/model/v3_forward_messages_to_channel_response.dart';
import 'package:chat_api/src/model/v3_forward_messages_to_dm_channel_request.dart';
import 'package:chat_api/src/model/v3_forward_messages_to_dm_channel_response.dart';
import 'package:chat_api/src/model/v3_friend.dart';
import 'package:chat_api/src/model/v3_friend_data.dart';
import 'package:chat_api/src/model/v3_friend_removed_event_data.dart';
import 'package:chat_api/src/model/v3_gateway_connected_event_data.dart';
import 'package:chat_api/src/model/v3_generate_security_key_request.dart';
import 'package:chat_api/src/model/v3_generate_security_key_response.dart';
import 'package:chat_api/src/model/v3_generate_user_connect_link_response.dart';
import 'package:chat_api/src/model/v3_generate_user_connect_link_response_data.dart';
import 'package:chat_api/src/model/v3_generic_data.dart';
import 'package:chat_api/src/model/v3_get_avatar_frame_response.dart';
import 'package:chat_api/src/model/v3_get_channel_response.dart';
import 'package:chat_api/src/model/v3_get_dm_channel_response.dart';
import 'package:chat_api/src/model/v3_get_dm_media_file_response.dart';
import 'package:chat_api/src/model/v3_get_dm_message_response.dart';
import 'package:chat_api/src/model/v3_get_friend_response.dart';
import 'package:chat_api/src/model/v3_get_invitation_response.dart';
import 'package:chat_api/src/model/v3_get_me_response.dart';
import 'package:chat_api/src/model/v3_get_media_file_response.dart';
import 'package:chat_api/src/model/v3_get_meeting_room_response.dart';
import 'package:chat_api/src/model/v3_get_member_response.dart';
import 'package:chat_api/src/model/v3_get_message_response.dart';
import 'package:chat_api/src/model/v3_get_pinned_dm_message_response.dart';
import 'package:chat_api/src/model/v3_get_pinned_message_response.dart';
import 'package:chat_api/src/model/v3_get_pow_challenge_response.dart';
import 'package:chat_api/src/model/v3_get_private_data_response.dart';
import 'package:chat_api/src/model/v3_get_qr_auth_state_response.dart';
import 'package:chat_api/src/model/v3_get_ringback_tone_response.dart';
import 'package:chat_api/src/model/v3_get_sticker_collection_response.dart';
import 'package:chat_api/src/model/v3_get_sticker_response.dart';
import 'package:chat_api/src/model/v3_get_tokens_request.dart';
import 'package:chat_api/src/model/v3_get_tokens_response.dart';
import 'package:chat_api/src/model/v3_get_user_by_username_response.dart';
import 'package:chat_api/src/model/v3_get_user_response.dart';
import 'package:chat_api/src/model/v3_ice_candidate.dart';
import 'package:chat_api/src/model/v3_incoming_friend_request_accepted_event_data.dart';
import 'package:chat_api/src/model/v3_incoming_friend_request_canceled_event_data.dart';
import 'package:chat_api/src/model/v3_incoming_friend_request_created_event_data.dart';
import 'package:chat_api/src/model/v3_incoming_friend_request_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_incoming_message_request_accepted_event_data.dart';
import 'package:chat_api/src/model/v3_incoming_message_request_created_event_data.dart';
import 'package:chat_api/src/model/v3_initiate_account_deletion_by_security_key_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_account_deletion_by_security_key_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_account_deletion_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_account_deletion_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_file_collection_request.dart';
import 'package:chat_api/src/model/v3_initiate_file_collection_response.dart';
import 'package:chat_api/src/model/v3_initiate_file_collection_response_data.dart';
import 'package:chat_api/src/model/v3_initiate_generate_security_key_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_generate_security_key_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_multipart_upload_request.dart';
import 'package:chat_api/src/model/v3_initiate_multipart_upload_response.dart';
import 'package:chat_api/src/model/v3_initiate_multipart_upload_response_data.dart';
import 'package:chat_api/src/model/v3_initiate_qr_auth_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_qr_auth_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_recovery_account_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_recovery_account_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_recovery_code_generation_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_recovery_code_generation_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_smart_otp_auth_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_smart_otp_auth_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_suggest_user_key_auth_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_suggest_user_key_auth_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_user_key_auth_flow_request.dart';
import 'package:chat_api/src/model/v3_initiate_user_key_auth_flow_response.dart';
import 'package:chat_api/src/model/v3_initiate_view_security_key_request.dart';
import 'package:chat_api/src/model/v3_initiate_view_security_key_response.dart';
import 'package:chat_api/src/model/v3_invitation.dart';
import 'package:chat_api/src/model/v3_invitation_data.dart';
import 'package:chat_api/src/model/v3_invitation_data_channel_data.dart';
import 'package:chat_api/src/model/v3_jump_to_dm_message_response.dart';
import 'package:chat_api/src/model/v3_jump_to_message_response.dart';
import 'package:chat_api/src/model/v3_layout_metadata.dart';
import 'package:chat_api/src/model/v3_leave_channel_request.dart';
import 'package:chat_api/src/model/v3_leave_channel_response.dart';
import 'package:chat_api/src/model/v3_link_object.dart';
import 'package:chat_api/src/model/v3_list_all_active_sessions_response.dart';
import 'package:chat_api/src/model/v3_list_all_channels_response.dart';
import 'package:chat_api/src/model/v3_list_all_sticker_collections_response.dart';
import 'package:chat_api/src/model/v3_list_avatar_frame_collection_response.dart';
import 'package:chat_api/src/model/v3_list_banned_users_response.dart';
import 'package:chat_api/src/model/v3_list_blocked_users_response.dart';
import 'package:chat_api/src/model/v3_list_call_logs_response.dart';
import 'package:chat_api/src/model/v3_list_channel_audit_logs_response.dart';
import 'package:chat_api/src/model/v3_list_channels_response.dart';
import 'package:chat_api/src/model/v3_list_dm_channels_response.dart';
import 'package:chat_api/src/model/v3_list_dm_media_file_fragments_response.dart';
import 'package:chat_api/src/model/v3_list_dm_media_files_response.dart';
import 'package:chat_api/src/model/v3_list_dm_message_fragments_response.dart';
import 'package:chat_api/src/model/v3_list_dm_message_reactions_response.dart';
import 'package:chat_api/src/model/v3_list_dm_messages_response.dart';
import 'package:chat_api/src/model/v3_list_friends_response.dart';
import 'package:chat_api/src/model/v3_list_in_coming_friend_requests_response.dart';
import 'package:chat_api/src/model/v3_list_in_coming_message_requests_response.dart';
import 'package:chat_api/src/model/v3_list_invitable_users_response.dart';
import 'package:chat_api/src/model/v3_list_invitation_response.dart';
import 'package:chat_api/src/model/v3_list_media_file_fragments_response.dart';
import 'package:chat_api/src/model/v3_list_media_files_response.dart';
import 'package:chat_api/src/model/v3_list_meeting_room_response.dart';
import 'package:chat_api/src/model/v3_list_members_response.dart';
import 'package:chat_api/src/model/v3_list_message_fragments_response.dart';
import 'package:chat_api/src/model/v3_list_message_reactions_data.dart';
import 'package:chat_api/src/model/v3_list_message_reactions_response.dart';
import 'package:chat_api/src/model/v3_list_messages_response.dart';
import 'package:chat_api/src/model/v3_list_out_going_friend_requests_response.dart';
import 'package:chat_api/src/model/v3_list_out_going_message_requests_response.dart';
import 'package:chat_api/src/model/v3_list_private_data_response.dart';
import 'package:chat_api/src/model/v3_list_ringback_tones_response.dart';
import 'package:chat_api/src/model/v3_list_share_to_incoming_response.dart';
import 'package:chat_api/src/model/v3_list_stickers_response.dart';
import 'package:chat_api/src/model/v3_list_subscriptions_response.dart';
import 'package:chat_api/src/model/v3_list_suggested_friends_by_type_response.dart';
import 'package:chat_api/src/model/v3_list_suggested_friends_response.dart';
import 'package:chat_api/src/model/v3_list_unread_message_counts_response.dart';
import 'package:chat_api/src/model/v3_list_unread_message_counts_response_data.dart';
import 'package:chat_api/src/model/v3_list_user_status_response.dart';
import 'package:chat_api/src/model/v3_list_user_visited_profile_response.dart';
import 'package:chat_api/src/model/v3_location_data.dart';
import 'package:chat_api/src/model/v3_login_request_user_key.dart';
import 'package:chat_api/src/model/v3_login_with_qr_auth_code_request.dart';
import 'package:chat_api/src/model/v3_login_with_qr_auth_code_response.dart';
import 'package:chat_api/src/model/v3_login_with_smart_otp_request.dart';
import 'package:chat_api/src/model/v3_login_with_smart_otp_response.dart';
import 'package:chat_api/src/model/v3_login_with_suggest_user_key_request.dart';
import 'package:chat_api/src/model/v3_login_with_suggest_user_key_response.dart';
import 'package:chat_api/src/model/v3_login_with_user_key_request.dart';
import 'package:chat_api/src/model/v3_login_with_user_key_response.dart';
import 'package:chat_api/src/model/v3_logout_response.dart';
import 'package:chat_api/src/model/v3_mark_all_as_read_response.dart';
import 'package:chat_api/src/model/v3_mark_all_channels_as_read_event_data.dart';
import 'package:chat_api/src/model/v3_mark_all_channels_as_read_response.dart';
import 'package:chat_api/src/model/v3_mark_as_read_request.dart';
import 'package:chat_api/src/model/v3_mark_as_read_response.dart';
import 'package:chat_api/src/model/v3_mark_dmas_read_request.dart';
import 'package:chat_api/src/model/v3_mark_dmas_read_response.dart';
import 'package:chat_api/src/model/v3_matrix.dart';
import 'package:chat_api/src/model/v3_me.dart';
import 'package:chat_api/src/model/v3_media_attachment.dart';
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:chat_api/src/model/v3_meet_open_connection_request.dart';
import 'package:chat_api/src/model/v3_meet_open_connection_response.dart';
import 'package:chat_api/src/model/v3_meet_token_data.dart';
import 'package:chat_api/src/model/v3_member.dart';
import 'package:chat_api/src/model/v3_member_banned_event_data.dart';
import 'package:chat_api/src/model/v3_member_data.dart';
import 'package:chat_api/src/model/v3_member_joined_event_data.dart';
import 'package:chat_api/src/model/v3_member_left_event_data.dart';
import 'package:chat_api/src/model/v3_member_nickname_updated_event_data.dart';
import 'package:chat_api/src/model/v3_member_removed_event_data.dart';
import 'package:chat_api/src/model/v3_member_role.dart';
import 'package:chat_api/src/model/v3_member_role_revoked_event_data.dart';
import 'package:chat_api/src/model/v3_member_role_updated_event_data.dart';
import 'package:chat_api/src/model/v3_member_unbanned_event_data.dart';
import 'package:chat_api/src/model/v3_message.dart';
import 'package:chat_api/src/model/v3_message_created_event_data.dart';
import 'package:chat_api/src/model/v3_message_data.dart';
import 'package:chat_api/src/model/v3_message_pinned_event_data.dart';
import 'package:chat_api/src/model/v3_message_reaction_updated_event_data.dart';
import 'package:chat_api/src/model/v3_message_request_rejected_event_data.dart';
import 'package:chat_api/src/model/v3_message_unpinned_event_data.dart';
import 'package:chat_api/src/model/v3_message_updated_event_data.dart';
import 'package:chat_api/src/model/v3_messages_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_migrate_passkey_request.dart';
import 'package:chat_api/src/model/v3_migrate_passkey_response.dart';
import 'package:chat_api/src/model/v3_mock_channels_request.dart';
import 'package:chat_api/src/model/v3_mock_channels_response.dart';
import 'package:chat_api/src/model/v3_mock_friends_request.dart';
import 'package:chat_api/src/model/v3_mock_friends_response.dart';
import 'package:chat_api/src/model/v3_mock_messages_request.dart';
import 'package:chat_api/src/model/v3_mock_messages_response.dart';
import 'package:chat_api/src/model/v3_mock_users_request.dart';
import 'package:chat_api/src/model/v3_mock_users_response.dart';
import 'package:chat_api/src/model/v3_mocked_channel.dart';
import 'package:chat_api/src/model/v3_mocked_user.dart';
import 'package:chat_api/src/model/v3_open_connection_response.dart';
import 'package:chat_api/src/model/v3_original_message.dart';
import 'package:chat_api/src/model/v3_otp_request_options.dart';
import 'package:chat_api/src/model/v3_outgoing_friend_request_accepted_event_data.dart';
import 'package:chat_api/src/model/v3_outgoing_friend_request_canceled_event_data.dart';
import 'package:chat_api/src/model/v3_outgoing_friend_request_created_event_data.dart';
import 'package:chat_api/src/model/v3_outgoing_friend_request_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_outgoing_message_request_accepted_event_data.dart';
import 'package:chat_api/src/model/v3_outgoing_message_request_created_event_data.dart';
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_participant.dart';
import 'package:chat_api/src/model/v3_pin_unpin_dm_message_request.dart';
import 'package:chat_api/src/model/v3_pin_unpin_dm_message_response.dart';
import 'package:chat_api/src/model/v3_pin_unpin_message_request.dart';
import 'package:chat_api/src/model/v3_pin_unpin_message_response.dart';
import 'package:chat_api/src/model/v3_place_info.dart';
import 'package:chat_api/src/model/v3_pow_challenge_data.dart';
import 'package:chat_api/src/model/v3_premium_settings.dart';
import 'package:chat_api/src/model/v3_presence_data.dart';
import 'package:chat_api/src/model/v3_presence_updated_event_data.dart';
import 'package:chat_api/src/model/v3_privacy_settings.dart';
import 'package:chat_api/src/model/v3_private_data.dart';
import 'package:chat_api/src/model/v3_profile.dart';
import 'package:chat_api/src/model/v3_qr_auth_code_request_options.dart';
import 'package:chat_api/src/model/v3_qr_authentication.dart';
import 'package:chat_api/src/model/v3_quote_dm_message_request.dart';
import 'package:chat_api/src/model/v3_quote_dm_message_response.dart';
import 'package:chat_api/src/model/v3_quote_message_request.dart';
import 'package:chat_api/src/model/v3_quote_message_response.dart';
import 'package:chat_api/src/model/v3_rtc_ice_server.dart';
import 'package:chat_api/src/model/v3_rtc_session_description.dart';
import 'package:chat_api/src/model/v3_reaction_data.dart';
import 'package:chat_api/src/model/v3_recovery_account_request.dart';
import 'package:chat_api/src/model/v3_recovery_account_response.dart';
import 'package:chat_api/src/model/v3_recovery_code_confirmation_payload.dart';
import 'package:chat_api/src/model/v3_recovery_request.dart';
import 'package:chat_api/src/model/v3_register_request_user_key.dart';
import 'package:chat_api/src/model/v3_register_vo_ip_token_request.dart';
import 'package:chat_api/src/model/v3_register_vo_ip_token_response.dart';
import 'package:chat_api/src/model/v3_register_with_user_key_request.dart';
import 'package:chat_api/src/model/v3_register_with_user_key_response.dart';
import 'package:chat_api/src/model/v3_reject_message_request_request.dart';
import 'package:chat_api/src/model/v3_reject_message_request_response.dart';
import 'package:chat_api/src/model/v3_remove_decorated_avatar_response.dart';
import 'package:chat_api/src/model/v3_remove_from_channel_response.dart';
import 'package:chat_api/src/model/v3_renew_qr_auth_code_options.dart';
import 'package:chat_api/src/model/v3_renew_qr_auth_code_request.dart';
import 'package:chat_api/src/model/v3_renew_qr_auth_code_response.dart';
import 'package:chat_api/src/model/v3_report.dart';
import 'package:chat_api/src/model/v3_report_dm_message_request.dart';
import 'package:chat_api/src/model/v3_report_dm_message_response.dart';
import 'package:chat_api/src/model/v3_report_message_request.dart';
import 'package:chat_api/src/model/v3_report_message_response.dart';
import 'package:chat_api/src/model/v3_report_user_request.dart';
import 'package:chat_api/src/model/v3_report_user_response.dart';
import 'package:chat_api/src/model/v3_request_account_deletion_key.dart';
import 'package:chat_api/src/model/v3_revoke_channels_notification_pushed_event_data.dart';
import 'package:chat_api/src/model/v3_revoke_dm_message_reaction_request.dart';
import 'package:chat_api/src/model/v3_revoke_dm_message_reaction_response.dart';
import 'package:chat_api/src/model/v3_revoke_invitation_response.dart';
import 'package:chat_api/src/model/v3_revoke_message_reaction_request.dart';
import 'package:chat_api/src/model/v3_revoke_message_reaction_response.dart';
import 'package:chat_api/src/model/v3_revoke_messages_notification_pushed_event_data.dart';
import 'package:chat_api/src/model/v3_ringback_tone_create_request.dart';
import 'package:chat_api/src/model/v3_ringback_tone_create_response.dart';
import 'package:chat_api/src/model/v3_ringback_tone_created_event_data.dart';
import 'package:chat_api/src/model/v3_ringback_tone_data.dart';
import 'package:chat_api/src/model/v3_ringback_tone_delete_response.dart';
import 'package:chat_api/src/model/v3_ringback_tone_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_ringback_tone_rename_request.dart';
import 'package:chat_api/src/model/v3_ringback_tone_rename_response.dart';
import 'package:chat_api/src/model/v3_ringback_tone_renamed_event_data.dart';
import 'package:chat_api/src/model/v3_ringback_tone_selected_event_data.dart';
import 'package:chat_api/src/model/v3_room.dart';
import 'package:chat_api/src/model/v3_search_channel_result.dart';
import 'package:chat_api/src/model/v3_search_channels_request.dart';
import 'package:chat_api/src/model/v3_search_channels_response.dart';
import 'package:chat_api/src/model/v3_search_everything_request.dart';
import 'package:chat_api/src/model/v3_search_everything_response.dart';
import 'package:chat_api/src/model/v3_search_friend_result.dart';
import 'package:chat_api/src/model/v3_search_friends_request.dart';
import 'package:chat_api/src/model/v3_search_friends_response.dart';
import 'package:chat_api/src/model/v3_search_members_request.dart';
import 'package:chat_api/src/model/v3_search_members_response.dart';
import 'package:chat_api/src/model/v3_search_members_result.dart';
import 'package:chat_api/src/model/v3_search_stickers_response.dart';
import 'package:chat_api/src/model/v3_search_user_result.dart';
import 'package:chat_api/src/model/v3_search_users_request.dart';
import 'package:chat_api/src/model/v3_search_users_response.dart';
import 'package:chat_api/src/model/v3_security_key.dart';
import 'package:chat_api/src/model/v3_send_dm_location_request.dart';
import 'package:chat_api/src/model/v3_send_dm_location_response.dart';
import 'package:chat_api/src/model/v3_send_dm_message_request.dart';
import 'package:chat_api/src/model/v3_send_dm_message_response.dart';
import 'package:chat_api/src/model/v3_send_dm_message_sticker_request.dart';
import 'package:chat_api/src/model/v3_send_dm_message_sticker_response.dart';
import 'package:chat_api/src/model/v3_send_dm_message_media_request.dart';
import 'package:chat_api/src/model/v3_send_dm_message_media_response.dart';
import 'package:chat_api/src/model/v3_send_invitation_request.dart';
import 'package:chat_api/src/model/v3_send_invitation_response.dart';
import 'package:chat_api/src/model/v3_send_location_request.dart';
import 'package:chat_api/src/model/v3_send_location_response.dart';
import 'package:chat_api/src/model/v3_send_message_media_request.dart';
import 'package:chat_api/src/model/v3_send_message_media_response.dart';
import 'package:chat_api/src/model/v3_send_message_request.dart';
import 'package:chat_api/src/model/v3_send_message_response.dart';
import 'package:chat_api/src/model/v3_send_message_sticker_request.dart';
import 'package:chat_api/src/model/v3_send_message_sticker_response.dart';
import 'package:chat_api/src/model/v3_send_poke_message_request.dart';
import 'package:chat_api/src/model/v3_send_poke_message_response.dart';
import 'package:chat_api/src/model/v3_session_data.dart';
import 'package:chat_api/src/model/v3_sessions_data.dart';
import 'package:chat_api/src/model/v3_set_badge_request.dart';
import 'package:chat_api/src/model/v3_set_badge_response.dart';
import 'package:chat_api/src/model/v3_set_ringback_tone_request.dart';
import 'package:chat_api/src/model/v3_set_ringback_tone_response.dart';
import 'package:chat_api/src/model/v3_share_to_incoming_result.dart';
import 'package:chat_api/src/model/v3_speech_response_data.dart';
import 'package:chat_api/src/model/v3_speech_to_text_request.dart';
import 'package:chat_api/src/model/v3_speech_to_text_response.dart';
import 'package:chat_api/src/model/v3_sticker.dart';
import 'package:chat_api/src/model/v3_sticker_collection.dart';
import 'package:chat_api/src/model/v3_sticker_object.dart';
import 'package:chat_api/src/model/v3_subscribe_all_request.dart';
import 'package:chat_api/src/model/v3_subscribe_all_response.dart';
import 'package:chat_api/src/model/v3_subscribe_channel_request.dart';
import 'package:chat_api/src/model/v3_subscribe_channel_response.dart';
import 'package:chat_api/src/model/v3_suggest_friend.dart';
import 'package:chat_api/src/model/v3_suggestions.dart';
import 'package:chat_api/src/model/v3_sync_all_channels_response.dart';
import 'package:chat_api/src/model/v3_sync_all_friends_response.dart';
import 'package:chat_api/src/model/v3_sync_contacts_request.dart';
import 'package:chat_api/src/model/v3_sync_contacts_response.dart';
import 'package:chat_api/src/model/v3_sync_dm_messages_response.dart';
import 'package:chat_api/src/model/v3_sync_members_response.dart';
import 'package:chat_api/src/model/v3_sync_messages_response.dart';
import 'package:chat_api/src/model/v3_sync_sticker_collections_response.dart';
import 'package:chat_api/src/model/v3_sync_users_response.dart';
import 'package:chat_api/src/model/v3_terminate_all_sessions_response.dart';
import 'package:chat_api/src/model/v3_terminate_session_request.dart';
import 'package:chat_api/src/model/v3_terminate_session_response.dart';
import 'package:chat_api/src/model/v3_text_to_speech_request.dart';
import 'package:chat_api/src/model/v3_text_to_speech_response.dart';
import 'package:chat_api/src/model/v3_timed_version.dart';
import 'package:chat_api/src/model/v3_token_exchange_request.dart';
import 'package:chat_api/src/model/v3_token_exchange_response.dart';
import 'package:chat_api/src/model/v3_transfer_ownership_and_leave_channel_request.dart';
import 'package:chat_api/src/model/v3_transfer_ownership_and_leave_channel_response.dart';
import 'package:chat_api/src/model/v3_transfer_ownership_request.dart';
import 'package:chat_api/src/model/v3_transfer_ownership_response.dart';
import 'package:chat_api/src/model/v3_translation_request.dart';
import 'package:chat_api/src/model/v3_translation_request_data.dart';
import 'package:chat_api/src/model/v3_translation_response.dart';
import 'package:chat_api/src/model/v3_translation_response_data.dart';
import 'package:chat_api/src/model/v3_turn_off_global_notification_response.dart';
import 'package:chat_api/src/model/v3_turn_on_global_notification_response.dart';
import 'package:chat_api/src/model/v3_unban_from_channel_request.dart';
import 'package:chat_api/src/model/v3_unban_from_channel_response.dart';
import 'package:chat_api/src/model/v3_unban_user_request.dart';
import 'package:chat_api/src/model/v3_unban_user_response.dart';
import 'package:chat_api/src/model/v3_unblock_user_request.dart';
import 'package:chat_api/src/model/v3_unblock_user_response.dart';
import 'package:chat_api/src/model/v3_unfriend_request.dart';
import 'package:chat_api/src/model/v3_unfriend_response.dart';
import 'package:chat_api/src/model/v3_unsubscribe_all_response.dart';
import 'package:chat_api/src/model/v3_unsubscribe_channel_request.dart';
import 'package:chat_api/src/model/v3_unsubscribe_channel_response.dart';
import 'package:chat_api/src/model/v3_update_channel_avatar_request.dart';
import 'package:chat_api/src/model/v3_update_channel_avatar_response.dart';
import 'package:chat_api/src/model/v3_update_channel_name_request.dart';
import 'package:chat_api/src/model/v3_update_channel_name_response.dart';
import 'package:chat_api/src/model/v3_update_cover_photo_request.dart';
import 'package:chat_api/src/model/v3_update_cover_photo_response.dart';
import 'package:chat_api/src/model/v3_update_dm_media_permission_setting_request.dart';
import 'package:chat_api/src/model/v3_update_dm_media_permission_setting_response.dart';
import 'package:chat_api/src/model/v3_update_dm_message_request.dart';
import 'package:chat_api/src/model/v3_update_dm_message_response.dart';
import 'package:chat_api/src/model/v3_update_dm_media_attachments_request.dart';
import 'package:chat_api/src/model/v3_update_dm_media_attachments_response.dart';
import 'package:chat_api/src/model/v3_update_media_attachments_request.dart';
import 'package:chat_api/src/model/v3_update_media_attachments_response.dart';
import 'package:chat_api/src/model/v3_update_media_permission_setting_request.dart';
import 'package:chat_api/src/model/v3_update_media_permission_setting_response.dart';
import 'package:chat_api/src/model/v3_update_message_request.dart';
import 'package:chat_api/src/model/v3_update_message_response.dart';
import 'package:chat_api/src/model/v3_update_nickname_request.dart';
import 'package:chat_api/src/model/v3_update_nickname_response.dart';
import 'package:chat_api/src/model/v3_update_recovery_code_setting_request.dart';
import 'package:chat_api/src/model/v3_update_recovery_code_setting_response.dart';
import 'package:chat_api/src/model/v3_update_session_expiration_setting_request.dart';
import 'package:chat_api/src/model/v3_update_session_expiration_setting_response.dart';
import 'package:chat_api/src/model/v3_update_smart_otp_setting_request.dart';
import 'package:chat_api/src/model/v3_update_smart_otp_setting_response.dart';
import 'package:chat_api/src/model/v3_update_user_avatar_request.dart';
import 'package:chat_api/src/model/v3_update_user_avatar_response.dart';
import 'package:chat_api/src/model/v3_update_user_display_name_request.dart';
import 'package:chat_api/src/model/v3_update_user_display_name_response.dart';
import 'package:chat_api/src/model/v3_update_user_email_request.dart';
import 'package:chat_api/src/model/v3_update_user_email_response.dart';
import 'package:chat_api/src/model/v3_update_user_phone_request.dart';
import 'package:chat_api/src/model/v3_update_user_phone_response.dart';
import 'package:chat_api/src/model/v3_update_user_scope_for_call_request.dart';
import 'package:chat_api/src/model/v3_update_user_scope_for_call_response.dart';
import 'package:chat_api/src/model/v3_update_user_scope_for_message_request.dart';
import 'package:chat_api/src/model/v3_update_user_scope_for_message_response.dart';
import 'package:chat_api/src/model/v3_update_user_status_request.dart';
import 'package:chat_api/src/model/v3_update_user_status_response.dart';
import 'package:chat_api/src/model/v3_update_user_video_avatar_request.dart';
import 'package:chat_api/src/model/v3_update_user_video_avatar_response.dart';
import 'package:chat_api/src/model/v3_updated_avatar.dart';
import 'package:chat_api/src/model/v3_upload_chunk.dart';
import 'package:chat_api/src/model/v3_upload_decorated_avatar_request.dart';
import 'package:chat_api/src/model/v3_upload_decorated_avatar_response.dart';
import 'package:chat_api/src/model/v3_upload_metadata.dart';
import 'package:chat_api/src/model/v3_upload_part_request.dart';
import 'package:chat_api/src/model/v3_upload_part_response.dart';
import 'package:chat_api/src/model/v3_upload_request.dart';
import 'package:chat_api/src/model/v3_user.dart';
import 'package:chat_api/src/model/v3_user_avatar_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_user_avatar_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_badge_count_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_blocked_event_data.dart';
import 'package:chat_api/src/model/v3_user_created_event_data.dart';
import 'package:chat_api/src/model/v3_user_created_event_data_geo_location.dart';
import 'package:chat_api/src/model/v3_user_creation_failed_data.dart';
import 'package:chat_api/src/model/v3_user_creation_failed_data_geo_location.dart';
import 'package:chat_api/src/model/v3_user_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_user_display_name_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_email_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_global_media_permission_setting_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_global_notification_status_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_message_reaction_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_messages_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_user_phone_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_scope_for_call_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_scope_for_message_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_setting.dart';
import 'package:chat_api/src/model/v3_user_status.dart';
import 'package:chat_api/src/model/v3_user_status_created_event_data.dart';
import 'package:chat_api/src/model/v3_user_status_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_user_status_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_sync_data.dart';
import 'package:chat_api/src/model/v3_user_unblocked_event_data.dart';
import 'package:chat_api/src/model/v3_user_unread_messages_updated_event_data.dart';
import 'package:chat_api/src/model/v3_user_video_avatar_deleted_event_data.dart';
import 'package:chat_api/src/model/v3_user_view.dart';
import 'package:chat_api/src/model/v3_user_visited_profile_event_data.dart';
import 'package:chat_api/src/model/v3_verify_migrate_passkey_request.dart';
import 'package:chat_api/src/model/v3_verify_migrate_passkey_response.dart';
import 'package:chat_api/src/model/v3_verify_qr_auth_data.dart';
import 'package:chat_api/src/model/v3_verify_qr_auth_request.dart';
import 'package:chat_api/src/model/v3_verify_qr_auth_response.dart';
import 'package:chat_api/src/model/v3_verify_smart_otp_request.dart';
import 'package:chat_api/src/model/v3_verify_smart_otp_response.dart';
import 'package:chat_api/src/model/v3_visited_profile_data.dart';
import 'package:chat_api/src/model/v3_visited_profile_request.dart';
import 'package:chat_api/src/model/v3_visited_profile_response.dart';
import 'package:chat_api/src/model/v3_websocket_resume_event_data.dart';

final _regList = RegExp(r'^List<(.*)>$');
final _regSet = RegExp(r'^Set<(.*)>$');
final _regMap = RegExp(r'^Map<String,(.*)>$');

ReturnType deserialize<ReturnType, BaseType>(dynamic value, String targetType,
    {bool growable = true}) {
  switch (targetType) {
    case 'String':
      return '$value' as ReturnType;
    case 'int':
      return (value is int ? value : int.parse('$value')) as ReturnType;
    case 'bool':
      if (value is bool) {
        return value as ReturnType;
      }
      final valueString = '$value'.toLowerCase();
      return (valueString == 'true' || valueString == '1') as ReturnType;
    case 'double':
      return (value is double ? value : double.parse('$value')) as ReturnType;
    case 'CallSignalUpdatedEventDataRecipientInfo':
      return CallSignalUpdatedEventDataRecipientInfo.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'CommonAssertionResponse':
      return CommonAssertionResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'CommonAssertionResult':
      return CommonAssertionResult.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'CommonAttestationResponse':
      return CommonAttestationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'CommonAttestationResult':
      return CommonAttestationResult.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'CommonAuthUser':
      return CommonAuthUser.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'CommonAuthenticatorSelectionCriteria':
      return CommonAuthenticatorSelectionCriteria.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'CommonCredentialRequestOptions':
      return CommonCredentialRequestOptions.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'CommonPublicKeyCredParam':
      return CommonPublicKeyCredParam.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'CommonPublicKeyCredentialCreationOptions':
      return CommonPublicKeyCredentialCreationOptions.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'CommonPublicKeyCredentialDescriptor':
      return CommonPublicKeyCredentialDescriptor.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'CommonPublicKeyCredentialRequestOptions':
      return CommonPublicKeyCredentialRequestOptions.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'CommonRelayParty':
      return CommonRelayParty.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'Iamv3TerminateSession':
      return Iamv3TerminateSession.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'InitiateFileCollectionResponseStorageSpace':
      return InitiateFileCollectionResponseStorageSpace.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'InitiateMultipartUploadResponsePolicies':
      return InitiateMultipartUploadResponsePolicies.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'LoginRequestSmartOtp':
      return LoginRequestSmartOtp.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'MessageReactionUpdatedEventDataMessageReactionData':
      return MessageReactionUpdatedEventDataMessageReactionData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'PremiumSettingsBoosted':
      return PremiumSettingsBoosted.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'PrivacySettingsRestrictSavingContent':
      return PrivacySettingsRestrictSavingContent.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'ProtobufAny':
      return ProtobufAny.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'RpcStatus':
      return RpcStatus.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'Sharedv3ChannelData':
      return Sharedv3ChannelData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'StreamResultOfV3SpeechToTextResponse':
      return StreamResultOfV3SpeechToTextResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'StreamResultOfV3SyncAllChannelsResponse':
      return StreamResultOfV3SyncAllChannelsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'StreamResultOfV3SyncAllFriendsResponse':
      return StreamResultOfV3SyncAllFriendsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'StreamResultOfV3SyncDMMessagesResponse':
      return StreamResultOfV3SyncDMMessagesResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'StreamResultOfV3SyncMembersResponse':
      return StreamResultOfV3SyncMembersResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'StreamResultOfV3SyncMessagesResponse':
      return StreamResultOfV3SyncMessagesResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'StreamResultOfV3UploadPartResponse':
      return StreamResultOfV3UploadPartResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'SyncAllChannelsResponseChannelDeletedTypeEnum':
    case 'SyncAllChannelsResponseChannelIdentification':
      return SyncAllChannelsResponseChannelIdentification.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'SyncAllFriendsResponseFriendIdentification':
      return SyncAllFriendsResponseFriendIdentification.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'SyncUsersResponseUserIdentification':
      return SyncUsersResponseUserIdentification.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'UserSettingMediaPermissionSetting':
      return UserSettingMediaPermissionSetting.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'UserSettingPrivacySetting':
      return UserSettingPrivacySetting.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'UserSettingRecoveryCodeSetting':
      return UserSettingRecoveryCodeSetting.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'UserSettingSecurityKeySetting':
      return UserSettingSecurityKeySetting.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'UserSettingSecuritySetting':
      return UserSettingSecuritySetting.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'UserSettingSessionExpirationSetting':
      return UserSettingSessionExpirationSetting.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'UserSettingSmartOptSetting':
      return UserSettingSmartOptSetting.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AcceptFriendRequestRequest':
      return V3AcceptFriendRequestRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AcceptFriendRequestResponse':
      return V3AcceptFriendRequestResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AcceptInvitationRequest':
      return V3AcceptInvitationRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AcceptInvitationResponse':
      return V3AcceptInvitationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AcceptMessageRequestRequest':
      return V3AcceptMessageRequestRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AcceptMessageRequestResponse':
      return V3AcceptMessageRequestResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AcceptQRAuthRequest':
      return V3AcceptQRAuthRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AcceptQRAuthResponse':
      return V3AcceptQRAuthResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AddCoverPhotoRequest':
      return V3AddCoverPhotoRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AddCoverPhotoResponse':
      return V3AddCoverPhotoResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AddDMMessageReactionRequest':
      return V3AddDMMessageReactionRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AddDMMessageReactionResponse':
      return V3AddDMMessageReactionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AddFriendRequest':
      return V3AddFriendRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AddFriendResponse':
      return V3AddFriendResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AddMessageReactionRequest':
      return V3AddMessageReactionRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AddMessageReactionResponse':
      return V3AddMessageReactionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AddUserStatusRequest':
      return V3AddUserStatusRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AddUserStatusResponse':
      return V3AddUserStatusResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AllMessagesDeletedEventData':
      return V3AllMessagesDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AllUserMessagesDeletedEventData':
      return V3AllUserMessagesDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AssignAsAdminRequest':
      return V3AssignAsAdminRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AssignAsAdminResponse':
      return V3AssignAsAdminResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AttachmentFileStatusEnum':
    case 'V3AttachmentTypeEnum':
    case 'V3AudioFileToTextRequest':
      return V3AudioFileToTextRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AudioFileToTextResponse':
      return V3AudioFileToTextResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AudioMetadata':
      return V3AudioMetadata.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AudioTextToSpeechEncodingEnum':
    case 'V3AuditLog':
      return V3AuditLog.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3AvatarFrameCollectionData':
      return V3AvatarFrameCollectionData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AvatarFrameCreatedEventData':
      return V3AvatarFrameCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3AvatarFrameData':
      return V3AvatarFrameData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AvatarFrameDecoratedData':
      return V3AvatarFrameDecoratedData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3AvatarFrameDeletedEventData':
      return V3AvatarFrameDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3BadgeValueArgument':
      return V3BadgeValueArgument.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BanFromChannelRequest':
      return V3BanFromChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BanFromChannelResponse':
      return V3BanFromChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BanUserRequest':
      return V3BanUserRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BanUserResponse':
      return V3BanUserResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BlockUserRequest':
      return V3BlockUserRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BlockUserResponse':
      return V3BlockUserResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BoostChannelRequest':
      return V3BoostChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BoostChannelResponse':
      return V3BoostChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BoostDMRequest':
      return V3BoostDMRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3BoostDMResponse':
      return V3BoostDMResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CacheData':
      return V3CacheData.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3CacheDataEmbed':
      return V3CacheDataEmbed.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CallCreatedEventData':
      return V3CallCreatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CallData':
      return V3CallData.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3CallEndedReasonEnum':
    case 'V3CallLogSyncData':
      return V3CallLogSyncData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CallSignalIntentEnum':
    case 'V3CallSignalUpdatedEventData':
      return V3CallSignalUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CallStateEnum':
    case 'V3CallTypeEnum':
    case 'V3CallUpdatedEventData':
      return V3CallUpdatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CancelBoostChannelRequest':
      return V3CancelBoostChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CancelBoostChannelResponse':
      return V3CancelBoostChannelResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CancelBoostDMRequest':
      return V3CancelBoostDMRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CancelBoostDMResponse':
      return V3CancelBoostDMResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CancelFriendRequestRequest':
      return V3CancelFriendRequestRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CancelFriendRequestResponse':
      return V3CancelFriendRequestResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CancelRegistrationRequest':
      return V3CancelRegistrationRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CancelRegistrationResponse':
      return V3CancelRegistrationResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3Channel':
      return V3Channel.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3ChannelAvatarUploadFailedEventData':
      return V3ChannelAvatarUploadFailedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ChannelCreatedEventData':
      return V3ChannelCreatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ChannelCreationCompletedEventData':
      return V3ChannelCreationCompletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ChannelCreationFailedEventData':
      return V3ChannelCreationFailedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ChannelDeletedEventData':
      return V3ChannelDeletedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ChannelDestinationCloudEvent':
      return V3ChannelDestinationCloudEvent.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ChannelMetadata':
      return V3ChannelMetadata.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ChannelNotificationStatusUpdatedEventData':
      return V3ChannelNotificationStatusUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ChannelPermissionsEnum':
    case 'V3ChannelStatus':
      return V3ChannelStatus.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ChannelSyncData':
      return V3ChannelSyncData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ChannelTypeEnum':
    case 'V3ChannelTypingEventData':
      return V3ChannelTypingEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ChannelUpdatedEventData':
      return V3ChannelUpdatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CheckMigratePasskeyStatusResponse':
      return V3CheckMigratePasskeyStatusResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ClearDMMessageForEveryoneResponse':
      return V3ClearDMMessageForEveryoneResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ClearUserVisitedProfileNotificationsResponse':
      return V3ClearUserVisitedProfileNotificationsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CloudEvent':
      return V3CloudEvent.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3Codec':
      return V3Codec.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3CompleteFileCollectionRequest':
      return V3CompleteFileCollectionRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CompleteFileCollectionResponse':
      return V3CompleteFileCollectionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CompleteMultipartUploadRequest':
      return V3CompleteMultipartUploadRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CompleteMultipartUploadResponse':
      return V3CompleteMultipartUploadResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CompletedPart':
      return V3CompletedPart.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ConfirmAccountDeletionBySecurityKeyRequest':
      return V3ConfirmAccountDeletionBySecurityKeyRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConfirmAccountDeletionBySecurityKeyResponse':
      return V3ConfirmAccountDeletionBySecurityKeyResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConfirmAccountDeletionRequest':
      return V3ConfirmAccountDeletionRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConfirmAccountDeletionResponse':
      return V3ConfirmAccountDeletionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConfirmRecoveryCodeGenerationFlowRequest':
      return V3ConfirmRecoveryCodeGenerationFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConfirmRecoveryCodeGenerationFlowResponse':
      return V3ConfirmRecoveryCodeGenerationFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConfirmViewSecurityKeyRequest':
      return V3ConfirmViewSecurityKeyRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConfirmViewSecurityKeyResponse':
      return V3ConfirmViewSecurityKeyResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ConnectParams':
      return V3ConnectParams.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Contact':
      return V3Contact.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3CoverData':
      return V3CoverData.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3CoverPhotoCreatedEventData':
      return V3CoverPhotoCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CoverPhotoDeletedEventData':
      return V3CoverPhotoDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3CoverPhotoUpdatedData':
      return V3CoverPhotoUpdatedData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CrawlPlaceRequest':
      return V3CrawlPlaceRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CrawlPlaceResponse':
      return V3CrawlPlaceResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CrawlRequest':
      return V3CrawlRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CrawlResponse':
      return V3CrawlResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateAvatarFrameRequest':
      return V3CreateAvatarFrameRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateAvatarFrameResponse':
      return V3CreateAvatarFrameResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateCallRequest':
      return V3CreateCallRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateCallResponse':
      return V3CreateCallResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateCallResponseData':
      return V3CreateCallResponseData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateChannelRequest':
      return V3CreateChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateChannelResponse':
      return V3CreateChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateInvitationRequest':
      return V3CreateInvitationRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3CreateInvitationResponse':
      return V3CreateInvitationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DMChannelCreatedEventData':
      return V3DMChannelCreatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DMChannelUpdatedEventData':
      return V3DMChannelUpdatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DataBufferResponse':
      return V3DataBufferResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DataInclude':
      return V3DataInclude.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DataTextResponse':
      return V3DataTextResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DecodeUserConnectLinkRequest':
      return V3DecodeUserConnectLinkRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DecodeUserConnectLinkResponse':
      return V3DecodeUserConnectLinkResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DecodeUserConnectLinkResponseData':
      return V3DecodeUserConnectLinkResponseData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DecoratedAvatarRemovedEventData':
      return V3DecoratedAvatarRemovedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DecoratedAvatarUploadedEventData':
      return V3DecoratedAvatarUploadedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteAllDMMessagesForEveryoneResponse':
      return V3DeleteAllDMMessagesForEveryoneResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteAllDMMessagesOnlyMeResponse':
      return V3DeleteAllDMMessagesOnlyMeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteAllMessagesOnlyMeResponse':
      return V3DeleteAllMessagesOnlyMeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteAvatarFrameResponse':
      return V3DeleteAvatarFrameResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeleteChannelAvatarResponse':
      return V3DeleteChannelAvatarResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteChannelResponse':
      return V3DeleteChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeleteCoverPhotoResponse':
      return V3DeleteCoverPhotoResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeleteDMMessageForEveryoneResponse':
      return V3DeleteDMMessageForEveryoneResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteDMMessageOnlyMeResponse':
      return V3DeleteDMMessageOnlyMeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteDMMessagesForEveryoneResponse':
      return V3DeleteDMMessagesForEveryoneResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteDMMessagesOnlyMeResponse':
      return V3DeleteDMMessagesOnlyMeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteFileResponse':
      return V3DeleteFileResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeleteFriendRequestResponse':
      return V3DeleteFriendRequestResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteMessageForEveryoneResponse':
      return V3DeleteMessageForEveryoneResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteMessageOnlyMeResponse':
      return V3DeleteMessageOnlyMeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteMessagesForEveryoneResponse':
      return V3DeleteMessagesForEveryoneResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteMessagesOnlyMeResponse':
      return V3DeleteMessagesOnlyMeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteMockedChannelsResponse':
      return V3DeleteMockedChannelsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteMockedUsersResponse':
      return V3DeleteMockedUsersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeleteUserAvatarResponse':
      return V3DeleteUserAvatarResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeleteUserStatusResponse':
      return V3DeleteUserStatusResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeleteUserVideoAvatarResponse':
      return V3DeleteUserVideoAvatarResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteUserVisitedProfileEventData':
      return V3DeleteUserVisitedProfileEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeleteUserVisitedProfileResponse':
      return V3DeleteUserVisitedProfileResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeletionConfirmationPayload':
      return V3DeletionConfirmationPayload.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3DeviceInfo':
      return V3DeviceInfo.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3DeviceLinkedEventData':
      return V3DeviceLinkedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeviceSession':
      return V3DeviceSession.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DeviceUnlinkedEventData':
      return V3DeviceUnlinkedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Dimensions':
      return V3Dimensions.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3DirectMessageStatusEnum':
    case 'V3DismissAsAdminRequest':
      return V3DismissAsAdminRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3DismissAsAdminResponse':
      return V3DismissAsAdminResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Embed':
      return V3Embed.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3EmbedData':
      return V3EmbedData.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3EmbedTypeEnum':
    case 'V3Error':
      return V3Error.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3FileMetadata':
      return V3FileMetadata.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3FileUploadRequest':
      return V3FileUploadRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3FileUploadResponse':
      return V3FileUploadResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3FileUploadedEventData':
      return V3FileUploadedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ForwardMessagesToChannelRequest':
      return V3ForwardMessagesToChannelRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ForwardMessagesToChannelResponse':
      return V3ForwardMessagesToChannelResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ForwardMessagesToDMChannelRequest':
      return V3ForwardMessagesToDMChannelRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ForwardMessagesToDMChannelResponse':
      return V3ForwardMessagesToDMChannelResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3Friend':
      return V3Friend.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3FriendData':
      return V3FriendData.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3FriendRemovedEventData':
      return V3FriendRemovedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3FriendStatusEnum':
    case 'V3GatewayConnectedEventData':
      return V3GatewayConnectedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GenerateSecurityKeyRequest':
      return V3GenerateSecurityKeyRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3GenerateSecurityKeyResponse':
      return V3GenerateSecurityKeyResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3GenerateUserConnectLinkResponse':
      return V3GenerateUserConnectLinkResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3GenerateUserConnectLinkResponseData':
      return V3GenerateUserConnectLinkResponseData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3GenericData':
      return V3GenericData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetAvatarFrameResponse':
      return V3GetAvatarFrameResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetChannelResponse':
      return V3GetChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetDMChannelResponse':
      return V3GetDMChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetDMMediaFileResponse':
      return V3GetDMMediaFileResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetDMMessageResponse':
      return V3GetDMMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetFriendResponse':
      return V3GetFriendResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetInvitationResponse':
      return V3GetInvitationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetMeResponse':
      return V3GetMeResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetMediaFileResponse':
      return V3GetMediaFileResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetMeetingRoomResponse':
      return V3GetMeetingRoomResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetMemberResponse':
      return V3GetMemberResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetMessageResponse':
      return V3GetMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetPinnedDMMessageResponse':
      return V3GetPinnedDMMessageResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3GetPinnedMessageResponse':
      return V3GetPinnedMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetPowChallengeResponse':
      return V3GetPowChallengeResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetPrivateDataResponse':
      return V3GetPrivateDataResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetQRAuthStateResponse':
      return V3GetQRAuthStateResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetRingbackToneResponse':
      return V3GetRingbackToneResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetStickerCollectionResponse':
      return V3GetStickerCollectionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3GetStickerResponse':
      return V3GetStickerResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetTokensRequest':
      return V3GetTokensRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetTokensResponse':
      return V3GetTokensResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetUserByUsernameResponse':
      return V3GetUserByUsernameResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3GetUserResponse':
      return V3GetUserResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ICEConnectionStateEnum':
    case 'V3IceCandidate':
      return V3IceCandidate.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3IncomingFriendRequestAcceptedEventData':
      return V3IncomingFriendRequestAcceptedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3IncomingFriendRequestCanceledEventData':
      return V3IncomingFriendRequestCanceledEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3IncomingFriendRequestCreatedEventData':
      return V3IncomingFriendRequestCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3IncomingFriendRequestDeletedEventData':
      return V3IncomingFriendRequestDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3IncomingMessageRequestAcceptedEventData':
      return V3IncomingMessageRequestAcceptedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3IncomingMessageRequestCreatedEventData':
      return V3IncomingMessageRequestCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateAccountDeletionBySecurityKeyFlowRequest':
      return V3InitiateAccountDeletionBySecurityKeyFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateAccountDeletionBySecurityKeyFlowResponse':
      return V3InitiateAccountDeletionBySecurityKeyFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateAccountDeletionFlowRequest':
      return V3InitiateAccountDeletionFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateAccountDeletionFlowResponse':
      return V3InitiateAccountDeletionFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateFileCollectionRequest':
      return V3InitiateFileCollectionRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateFileCollectionResponse':
      return V3InitiateFileCollectionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateFileCollectionResponseData':
      return V3InitiateFileCollectionResponseData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateGenerateSecurityKeyFlowRequest':
      return V3InitiateGenerateSecurityKeyFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateGenerateSecurityKeyFlowResponse':
      return V3InitiateGenerateSecurityKeyFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateMultipartUploadRequest':
      return V3InitiateMultipartUploadRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateMultipartUploadResponse':
      return V3InitiateMultipartUploadResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateMultipartUploadResponseData':
      return V3InitiateMultipartUploadResponseData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateQRAuthFlowRequest':
      return V3InitiateQRAuthFlowRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3InitiateQRAuthFlowResponse':
      return V3InitiateQRAuthFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateRecoveryAccountFlowRequest':
      return V3InitiateRecoveryAccountFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateRecoveryAccountFlowResponse':
      return V3InitiateRecoveryAccountFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateRecoveryCodeGenerationFlowRequest':
      return V3InitiateRecoveryCodeGenerationFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateRecoveryCodeGenerationFlowResponse':
      return V3InitiateRecoveryCodeGenerationFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateSmartOtpAuthFlowRequest':
      return V3InitiateSmartOtpAuthFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateSmartOtpAuthFlowResponse':
      return V3InitiateSmartOtpAuthFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateSuggestUserKeyAuthFlowRequest':
      return V3InitiateSuggestUserKeyAuthFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateSuggestUserKeyAuthFlowResponse':
      return V3InitiateSuggestUserKeyAuthFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateUserKeyAuthFlowRequest':
      return V3InitiateUserKeyAuthFlowRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateUserKeyAuthFlowResponse':
      return V3InitiateUserKeyAuthFlowResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateViewSecurityKeyRequest':
      return V3InitiateViewSecurityKeyRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3InitiateViewSecurityKeyResponse':
      return V3InitiateViewSecurityKeyResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3Invitation':
      return V3Invitation.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3InvitationData':
      return V3InvitationData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3InvitationDataChannelData':
      return V3InvitationDataChannelData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3InvitationStatusEnum':
    case 'V3JumpToDMMessageResponse':
      return V3JumpToDMMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3JumpToMessageResponse':
      return V3JumpToMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LayoutMetadata':
      return V3LayoutMetadata.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LeaveChannelRequest':
      return V3LeaveChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LeaveChannelResponse':
      return V3LeaveChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LinkObject':
      return V3LinkObject.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3ListAllActiveSessionsResponse':
      return V3ListAllActiveSessionsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListAllChannelsResponse':
      return V3ListAllChannelsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListAllStickerCollectionsResponse':
      return V3ListAllStickerCollectionsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListAvatarFrameCollectionResponse':
      return V3ListAvatarFrameCollectionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListBannedUsersResponse':
      return V3ListBannedUsersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListBlockedUsersResponse':
      return V3ListBlockedUsersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListCallLogsResponse':
      return V3ListCallLogsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListChannelAuditLogsResponse':
      return V3ListChannelAuditLogsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListChannelsResponse':
      return V3ListChannelsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListDMChannelsResponse':
      return V3ListDMChannelsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListDMMediaFileFragmentsResponse':
      return V3ListDMMediaFileFragmentsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListDMMediaFilesResponse':
      return V3ListDMMediaFilesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListDMMessageFragmentsResponse':
      return V3ListDMMessageFragmentsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListDMMessageReactionsResponse':
      return V3ListDMMessageReactionsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListDMMessagesResponse':
      return V3ListDMMessagesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListFriendsResponse':
      return V3ListFriendsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListInComingFriendRequestsResponse':
      return V3ListInComingFriendRequestsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListInComingMessageRequestsResponse':
      return V3ListInComingMessageRequestsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListInvitableUsersResponse':
      return V3ListInvitableUsersResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListInvitationResponse':
      return V3ListInvitationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListMediaFileFragmentsResponse':
      return V3ListMediaFileFragmentsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListMediaFilesResponse':
      return V3ListMediaFilesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListMeetingRoomResponse':
      return V3ListMeetingRoomResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListMembersResponse':
      return V3ListMembersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListMessageFragmentsResponse':
      return V3ListMessageFragmentsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListMessageReactionsData':
      return V3ListMessageReactionsData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListMessageReactionsResponse':
      return V3ListMessageReactionsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListMessagesResponse':
      return V3ListMessagesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListOutGoingFriendRequestsResponse':
      return V3ListOutGoingFriendRequestsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListOutGoingMessageRequestsResponse':
      return V3ListOutGoingMessageRequestsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListPrivateDataResponse':
      return V3ListPrivateDataResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListRingbackTonesResponse':
      return V3ListRingbackTonesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListShareToIncomingResponse':
      return V3ListShareToIncomingResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListStickersResponse':
      return V3ListStickersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListSubscriptionsResponse':
      return V3ListSubscriptionsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListSuggestedFriendsByTypeResponse':
      return V3ListSuggestedFriendsByTypeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListSuggestedFriendsResponse':
      return V3ListSuggestedFriendsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListUnreadMessageCountsResponse':
      return V3ListUnreadMessageCountsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListUnreadMessageCountsResponseData':
      return V3ListUnreadMessageCountsResponseData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3ListUserStatusResponse':
      return V3ListUserStatusResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ListUserVisitedProfileResponse':
      return V3ListUserVisitedProfileResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3LocationData':
      return V3LocationData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LoginRequestState':
    case 'V3LoginRequestUserKey':
      return V3LoginRequestUserKey.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LoginWithQRAuthCodeRequest':
      return V3LoginWithQRAuthCodeRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3LoginWithQRAuthCodeResponse':
      return V3LoginWithQRAuthCodeResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3LoginWithSmartOtpRequest':
      return V3LoginWithSmartOtpRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LoginWithSmartOtpResponse':
      return V3LoginWithSmartOtpResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LoginWithSuggestUserKeyRequest':
      return V3LoginWithSuggestUserKeyRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3LoginWithSuggestUserKeyResponse':
      return V3LoginWithSuggestUserKeyResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3LoginWithUserKeyRequest':
      return V3LoginWithUserKeyRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LoginWithUserKeyResponse':
      return V3LoginWithUserKeyResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3LogoutResponse':
      return V3LogoutResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MarkAllAsReadResponse':
      return V3MarkAllAsReadResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MarkAllChannelsAsReadEventData':
      return V3MarkAllChannelsAsReadEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MarkAllChannelsAsReadResponse':
      return V3MarkAllChannelsAsReadResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MarkAsReadRequest':
      return V3MarkAsReadRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MarkAsReadResponse':
      return V3MarkAsReadResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MarkDMAsReadRequest':
      return V3MarkDMAsReadRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MarkDMAsReadResponse':
      return V3MarkDMAsReadResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Matrix':
      return V3Matrix.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3Me':
      return V3Me.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3MediaAttachment':
      return V3MediaAttachment.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MediaObject':
      return V3MediaObject.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MediaPermissionSettingEnum':
    case 'V3MeetOpenConnectionRequest':
      return V3MeetOpenConnectionRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MeetOpenConnectionResponse':
      return V3MeetOpenConnectionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MeetTokenData':
      return V3MeetTokenData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Member':
      return V3Member.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3MemberBannedEventData':
      return V3MemberBannedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MemberData':
      return V3MemberData.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3MemberJoinedEventData':
      return V3MemberJoinedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MemberLeftEventData':
      return V3MemberLeftEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MemberNicknameUpdatedEventData':
      return V3MemberNicknameUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MemberRemovedEventData':
      return V3MemberRemovedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MemberRole':
      return V3MemberRole.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3MemberRoleRevokedEventData':
      return V3MemberRoleRevokedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MemberRoleUpdatedEventData':
      return V3MemberRoleUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MemberUnbannedEventData':
      return V3MemberUnbannedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Message':
      return V3Message.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3MessageCreatedEventData':
      return V3MessageCreatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MessageData':
      return V3MessageData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MessagePinnedEventData':
      return V3MessagePinnedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MessageReactionUpdatedEventData':
      return V3MessageReactionUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MessageRequestRejectedEventData':
      return V3MessageRequestRejectedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3MessageStatusEnum':
    case 'V3MessageTypeEnum':
    case 'V3MessageUnpinnedEventData':
      return V3MessageUnpinnedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MessageUpdatedEventData':
      return V3MessageUpdatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MessagesDeletedEventData':
      return V3MessagesDeletedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MigratePasskeyRequest':
      return V3MigratePasskeyRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MigratePasskeyResponse':
      return V3MigratePasskeyResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockChannelsRequest':
      return V3MockChannelsRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockChannelsResponse':
      return V3MockChannelsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockFriendsRequest':
      return V3MockFriendsRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockFriendsResponse':
      return V3MockFriendsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockMessagesRequest':
      return V3MockMessagesRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockMessagesResponse':
      return V3MockMessagesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockUsersRequest':
      return V3MockUsersRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockUsersResponse':
      return V3MockUsersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockedChannel':
      return V3MockedChannel.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3MockedUser':
      return V3MockedUser.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3OpenConnectionIntentEnum':
    case 'V3OpenConnectionResponse':
      return V3OpenConnectionResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Orientation':
    case 'V3OriginalMessage':
      return V3OriginalMessage.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3OtpRequestOptions':
      return V3OtpRequestOptions.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3OutgoingFriendRequestAcceptedEventData':
      return V3OutgoingFriendRequestAcceptedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3OutgoingFriendRequestCanceledEventData':
      return V3OutgoingFriendRequestCanceledEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3OutgoingFriendRequestCreatedEventData':
      return V3OutgoingFriendRequestCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3OutgoingFriendRequestDeletedEventData':
      return V3OutgoingFriendRequestDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3OutgoingMessageRequestAcceptedEventData':
      return V3OutgoingMessageRequestAcceptedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3OutgoingMessageRequestCreatedEventData':
      return V3OutgoingMessageRequestCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3Paging':
      return V3Paging.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3Participant':
      return V3Participant.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PinUnpinDMMessageRequest':
      return V3PinUnpinDMMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PinUnpinDMMessageResponse':
      return V3PinUnpinDMMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PinUnpinMessageRequest':
      return V3PinUnpinMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PinUnpinMessageResponse':
      return V3PinUnpinMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PlaceInfo':
      return V3PlaceInfo.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3PowChallengeData':
      return V3PowChallengeData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PremiumSettings':
      return V3PremiumSettings.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PresenceData':
      return V3PresenceData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PresenceStateEnum':
    case 'V3PresenceUpdatedEventData':
      return V3PresenceUpdatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PretendingTo':
    case 'V3PrivacySettings':
      return V3PrivacySettings.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3PrivateData':
      return V3PrivateData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Profile':
      return V3Profile.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3QRAuthCodeRequestOptions':
      return V3QRAuthCodeRequestOptions.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3QRAuthentication':
      return V3QRAuthentication.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3QuoteDMMessageRequest':
      return V3QuoteDMMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3QuoteDMMessageResponse':
      return V3QuoteDMMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3QuoteMessageRequest':
      return V3QuoteMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3QuoteMessageResponse':
      return V3QuoteMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RTCIceServer':
      return V3RTCIceServer.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RTCSessionDescription':
      return V3RTCSessionDescription.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ReactionData':
      return V3ReactionData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RecoveryAccountRequest':
      return V3RecoveryAccountRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RecoveryAccountResponse':
      return V3RecoveryAccountResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RecoveryCodeConfirmationPayload':
      return V3RecoveryCodeConfirmationPayload.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RecoveryRequest':
      return V3RecoveryRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RegisterRequestUserKey':
      return V3RegisterRequestUserKey.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RegisterVoIPTokenRequest':
      return V3RegisterVoIPTokenRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RegisterVoIPTokenResponse':
      return V3RegisterVoIPTokenResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RegisterWithUserKeyRequest':
      return V3RegisterWithUserKeyRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RegisterWithUserKeyResponse':
      return V3RegisterWithUserKeyResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RejectMessageRequestRequest':
      return V3RejectMessageRequestRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RejectMessageRequestResponse':
      return V3RejectMessageRequestResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RemoveDecoratedAvatarResponse':
      return V3RemoveDecoratedAvatarResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RemoveFromChannelResponse':
      return V3RemoveFromChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RenewQrAuthCodeOptions':
      return V3RenewQrAuthCodeOptions.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RenewQrAuthCodeRequest':
      return V3RenewQrAuthCodeRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RenewQrAuthCodeResponse':
      return V3RenewQrAuthCodeResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3Report':
      return V3Report.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3ReportCategory':
    case 'V3ReportDMMessageRequest':
      return V3ReportDMMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ReportDMMessageResponse':
      return V3ReportDMMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ReportMessageRequest':
      return V3ReportMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ReportMessageResponse':
      return V3ReportMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ReportUserRequest':
      return V3ReportUserRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ReportUserResponse':
      return V3ReportUserResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RequestAccountDeletionKey':
      return V3RequestAccountDeletionKey.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RevokeChannelsNotificationPushedEventData':
      return V3RevokeChannelsNotificationPushedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RevokeDMMessageReactionRequest':
      return V3RevokeDMMessageReactionRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RevokeDMMessageReactionResponse':
      return V3RevokeDMMessageReactionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RevokeInvitationResponse':
      return V3RevokeInvitationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RevokeMessageReactionRequest':
      return V3RevokeMessageReactionRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RevokeMessageReactionResponse':
      return V3RevokeMessageReactionResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RevokeMessagesNotificationPushedEventData':
      return V3RevokeMessagesNotificationPushedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RingbackToneCreateRequest':
      return V3RingbackToneCreateRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RingbackToneCreateResponse':
      return V3RingbackToneCreateResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RingbackToneCreatedEventData':
      return V3RingbackToneCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RingbackToneData':
      return V3RingbackToneData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RingbackToneDeleteResponse':
      return V3RingbackToneDeleteResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RingbackToneDeletedEventData':
      return V3RingbackToneDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RingbackToneRenameRequest':
      return V3RingbackToneRenameRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3RingbackToneRenameResponse':
      return V3RingbackToneRenameResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RingbackToneRenamedEventData':
      return V3RingbackToneRenamedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3RingbackToneSelectedEventData':
      return V3RingbackToneSelectedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3Room':
      return V3Room.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3SearchChannelResult':
      return V3SearchChannelResult.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchChannelsRequest':
      return V3SearchChannelsRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchChannelsResponse':
      return V3SearchChannelsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchEverythingRequest':
      return V3SearchEverythingRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchEverythingResponse':
      return V3SearchEverythingResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchFriendResult':
      return V3SearchFriendResult.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchFriendsRequest':
      return V3SearchFriendsRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchFriendsResponse':
      return V3SearchFriendsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchMembersRequest':
      return V3SearchMembersRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchMembersResponse':
      return V3SearchMembersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchMembersResult':
      return V3SearchMembersResult.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchStickersResponse':
      return V3SearchStickersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchUserResult':
      return V3SearchUserResult.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchUsersRequest':
      return V3SearchUsersRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SearchUsersResponse':
      return V3SearchUsersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SecurityKey':
      return V3SecurityKey.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendDMLocationRequest':
      return V3SendDMLocationRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendDMLocationResponse':
      return V3SendDMLocationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendDMMessageRequest':
      return V3SendDMMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendDMMessageResponse':
      return V3SendDMMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendDMMessageStickerRequest':
      return V3SendDMMessageStickerRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3SendDMMessageStickerResponse':
      return V3SendDMMessageStickerResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3SendDmMessageMediaRequest':
      return V3SendDmMessageMediaRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendDmMessageMediaResponse':
      return V3SendDmMessageMediaResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3SendInvitationRequest':
      return V3SendInvitationRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendInvitationResponse':
      return V3SendInvitationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendLocationRequest':
      return V3SendLocationRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendLocationResponse':
      return V3SendLocationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendMessageMediaRequest':
      return V3SendMessageMediaRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendMessageMediaResponse':
      return V3SendMessageMediaResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendMessageRequest':
      return V3SendMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendMessageResponse':
      return V3SendMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendMessageStickerRequest':
      return V3SendMessageStickerRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendMessageStickerResponse':
      return V3SendMessageStickerResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3SendPokeMessageRequest':
      return V3SendPokeMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SendPokeMessageResponse':
      return V3SendPokeMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SessionData':
      return V3SessionData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SessionDescriptionTypeEnum':
    case 'V3SessionExpirationSettingEnum':
    case 'V3SessionsData':
      return V3SessionsData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SetBadgeRequest':
      return V3SetBadgeRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SetBadgeResponse':
      return V3SetBadgeResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SetRingbackToneRequest':
      return V3SetRingbackToneRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SetRingbackToneResponse':
      return V3SetRingbackToneResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3ShareToIncomingResult':
      return V3ShareToIncomingResult.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SpeechResponseData':
      return V3SpeechResponseData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SpeechToTextRequest':
      return V3SpeechToTextRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SpeechToTextResponse':
      return V3SpeechToTextResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SsmlVoiceGenderEnum':
    case 'V3Sticker':
      return V3Sticker.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3StickerCollection':
      return V3StickerCollection.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3StickerCollectionTypeEnum':
    case 'V3StickerObject':
      return V3StickerObject.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SubscribeAllRequest':
      return V3SubscribeAllRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SubscribeAllResponse':
      return V3SubscribeAllResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SubscribeChannelRequest':
      return V3SubscribeChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SubscribeChannelResponse':
      return V3SubscribeChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SuggestFriend':
      return V3SuggestFriend.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SuggestionTypeEnum':
    case 'V3Suggestions':
      return V3Suggestions.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncAllChannelsResponse':
      return V3SyncAllChannelsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncAllFriendsResponse':
      return V3SyncAllFriendsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncContactsRequest':
      return V3SyncContactsRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncContactsResponse':
      return V3SyncContactsResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncDMMessagesResponse':
      return V3SyncDMMessagesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncMembersResponse':
      return V3SyncMembersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncMessagesResponse':
      return V3SyncMessagesResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3SyncStickerCollectionsResponse':
      return V3SyncStickerCollectionsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3SyncUsersResponse':
      return V3SyncUsersResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TerminateAllSessionsResponse':
      return V3TerminateAllSessionsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3TerminateSessionRequest':
      return V3TerminateSessionRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TerminateSessionResponse':
      return V3TerminateSessionResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TextToSpeechRequest':
      return V3TextToSpeechRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TextToSpeechResponse':
      return V3TextToSpeechResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TimedVersion':
      return V3TimedVersion.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TokenExchangeRequest':
      return V3TokenExchangeRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TokenExchangeResponse':
      return V3TokenExchangeResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TokenType':
    case 'V3TransferOwnershipAndLeaveChannelRequest':
      return V3TransferOwnershipAndLeaveChannelRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3TransferOwnershipAndLeaveChannelResponse':
      return V3TransferOwnershipAndLeaveChannelResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3TransferOwnershipRequest':
      return V3TransferOwnershipRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TransferOwnershipResponse':
      return V3TransferOwnershipResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TranslationRequest':
      return V3TranslationRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TranslationRequestData':
      return V3TranslationRequestData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TranslationResponse':
      return V3TranslationResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TranslationResponseData':
      return V3TranslationResponseData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3TurnOffGlobalNotificationResponse':
      return V3TurnOffGlobalNotificationResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3TurnOnGlobalNotificationResponse':
      return V3TurnOnGlobalNotificationResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UnbanFromChannelRequest':
      return V3UnbanFromChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnbanFromChannelResponse':
      return V3UnbanFromChannelResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnbanUserRequest':
      return V3UnbanUserRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnbanUserResponse':
      return V3UnbanUserResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnblockUserRequest':
      return V3UnblockUserRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnblockUserResponse':
      return V3UnblockUserResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnfriendRequest':
      return V3UnfriendRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnfriendResponse':
      return V3UnfriendResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnsubscribeAllResponse':
      return V3UnsubscribeAllResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnsubscribeChannelRequest':
      return V3UnsubscribeChannelRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UnsubscribeChannelResponse':
      return V3UnsubscribeChannelResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateChannelAvatarRequest':
      return V3UpdateChannelAvatarRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateChannelAvatarResponse':
      return V3UpdateChannelAvatarResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateChannelNameRequest':
      return V3UpdateChannelNameRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateChannelNameResponse':
      return V3UpdateChannelNameResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateCoverPhotoRequest':
      return V3UpdateCoverPhotoRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateCoverPhotoResponse':
      return V3UpdateCoverPhotoResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateDMMediaPermissionSettingRequest':
      return V3UpdateDMMediaPermissionSettingRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateDMMediaPermissionSettingResponse':
      return V3UpdateDMMediaPermissionSettingResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateDMMessageRequest':
      return V3UpdateDMMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateDMMessageResponse':
      return V3UpdateDMMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateDmMediaAttachmentsRequest':
      return V3UpdateDmMediaAttachmentsRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateDmMediaAttachmentsResponse':
      return V3UpdateDmMediaAttachmentsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateMediaAttachmentsRequest':
      return V3UpdateMediaAttachmentsRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateMediaAttachmentsResponse':
      return V3UpdateMediaAttachmentsResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateMediaPermissionSettingRequest':
      return V3UpdateMediaPermissionSettingRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateMediaPermissionSettingResponse':
      return V3UpdateMediaPermissionSettingResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateMessageRequest':
      return V3UpdateMessageRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateMessageResponse':
      return V3UpdateMessageResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateNicknameRequest':
      return V3UpdateNicknameRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateNicknameResponse':
      return V3UpdateNicknameResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateRecoveryCodeSettingRequest':
      return V3UpdateRecoveryCodeSettingRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateRecoveryCodeSettingResponse':
      return V3UpdateRecoveryCodeSettingResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateSessionExpirationSettingRequest':
      return V3UpdateSessionExpirationSettingRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateSessionExpirationSettingResponse':
      return V3UpdateSessionExpirationSettingResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateSmartOtpSettingRequest':
      return V3UpdateSmartOtpSettingRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateSmartOtpSettingResponse':
      return V3UpdateSmartOtpSettingResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserAvatarRequest':
      return V3UpdateUserAvatarRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserAvatarResponse':
      return V3UpdateUserAvatarResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserDisplayNameRequest':
      return V3UpdateUserDisplayNameRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserDisplayNameResponse':
      return V3UpdateUserDisplayNameResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserEmailRequest':
      return V3UpdateUserEmailRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserEmailResponse':
      return V3UpdateUserEmailResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserPhoneRequest':
      return V3UpdateUserPhoneRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserPhoneResponse':
      return V3UpdateUserPhoneResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserScopeForCallRequest':
      return V3UpdateUserScopeForCallRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserScopeForCallResponse':
      return V3UpdateUserScopeForCallResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserScopeForMessageRequest':
      return V3UpdateUserScopeForMessageRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserScopeForMessageResponse':
      return V3UpdateUserScopeForMessageResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserStatusRequest':
      return V3UpdateUserStatusRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserStatusResponse':
      return V3UpdateUserStatusResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UpdateUserVideoAvatarRequest':
      return V3UpdateUserVideoAvatarRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdateUserVideoAvatarResponse':
      return V3UpdateUserVideoAvatarResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UpdatedAvatar':
      return V3UpdatedAvatar.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UploadChunk':
      return V3UploadChunk.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UploadDecoratedAvatarRequest':
      return V3UploadDecoratedAvatarRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UploadDecoratedAvatarResponse':
      return V3UploadDecoratedAvatarResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UploadMetadata':
      return V3UploadMetadata.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UploadPartRequest':
      return V3UploadPartRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UploadPartResponse':
      return V3UploadPartResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UploadRequest':
      return V3UploadRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3User':
      return V3User.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3UserAvatarDeletedEventData':
      return V3UserAvatarDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserAvatarTypeEnum':
    case 'V3UserAvatarUpdatedEventData':
      return V3UserAvatarUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserBadgeCountUpdatedEventData':
      return V3UserBadgeCountUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserBadgeTypeEnum':
    case 'V3UserBlockedEventData':
      return V3UserBlockedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserCreatedEventData':
      return V3UserCreatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserCreatedEventDataGeoLocation':
      return V3UserCreatedEventDataGeoLocation.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserCreationFailedData':
      return V3UserCreationFailedData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserCreationFailedDataGeoLocation':
      return V3UserCreationFailedDataGeoLocation.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserDeletedEventData':
      return V3UserDeletedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserDeletedTypeEnum':
    case 'V3UserDisplayNameUpdatedEventData':
      return V3UserDisplayNameUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserEmailUpdatedEventData':
      return V3UserEmailUpdatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserGlobalMediaPermissionSettingUpdatedEventData':
      return V3UserGlobalMediaPermissionSettingUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserGlobalNotificationStatusUpdatedEventData':
      return V3UserGlobalNotificationStatusUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserMessageReactionUpdatedEventData':
      return V3UserMessageReactionUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserMessagesDeletedEventData':
      return V3UserMessagesDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserPhoneUpdatedEventData':
      return V3UserPhoneUpdatedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserScopeEnum':
    case 'V3UserScopeForCallUpdatedEventData':
      return V3UserScopeForCallUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserScopeForMessageUpdatedEventData':
      return V3UserScopeForMessageUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserSetting':
      return V3UserSetting.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserStatus':
      return V3UserStatus.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3UserStatusCreatedEventData':
      return V3UserStatusCreatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserStatusDeletedEventData':
      return V3UserStatusDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserStatusExpireAfterTimeEnum':
    case 'V3UserStatusUpdatedEventData':
      return V3UserStatusUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserSyncData':
      return V3UserSyncData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserTypeEnum':
    case 'V3UserUnblockedEventData':
      return V3UserUnblockedEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3UserUnreadMessagesUpdatedEventData':
      return V3UserUnreadMessagesUpdatedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserVideoAvatarDeletedEventData':
      return V3UserVideoAvatarDeletedEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3UserView':
      return V3UserView.fromJson(value as Map<String, dynamic>) as ReturnType;
    case 'V3UserVisitedProfileEventData':
      return V3UserVisitedProfileEventData.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3VerifyMigratePasskeyRequest':
      return V3VerifyMigratePasskeyRequest.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3VerifyMigratePasskeyResponse':
      return V3VerifyMigratePasskeyResponse.fromJson(
          value as Map<String, dynamic>) as ReturnType;
    case 'V3VerifyQRAuthData':
      return V3VerifyQRAuthData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3VerifyQRAuthRequest':
      return V3VerifyQRAuthRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3VerifyQRAuthResponse':
      return V3VerifyQRAuthResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3VerifySmartOtpRequest':
      return V3VerifySmartOtpRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3VerifySmartOtpResponse':
      return V3VerifySmartOtpResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3VisitedProfileData':
      return V3VisitedProfileData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3VisitedProfileRequest':
      return V3VisitedProfileRequest.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3VisitedProfileResponse':
      return V3VisitedProfileResponse.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    case 'V3WebsocketResumeEventData':
      return V3WebsocketResumeEventData.fromJson(value as Map<String, dynamic>)
          as ReturnType;
    default:
      RegExpMatch? match;

      if (value is List && (match = _regList.firstMatch(targetType)) != null) {
        targetType = match![1]!; // ignore: parameter_assignments
        return value
            .map<BaseType>((dynamic v) => deserialize<BaseType, BaseType>(
                v, targetType,
                growable: growable))
            .toList(growable: growable) as ReturnType;
      }
      if (value is Set && (match = _regSet.firstMatch(targetType)) != null) {
        targetType = match![1]!; // ignore: parameter_assignments
        return value
            .map<BaseType>((dynamic v) => deserialize<BaseType, BaseType>(
                v, targetType,
                growable: growable))
            .toSet() as ReturnType;
      }
      if (value is Map && (match = _regMap.firstMatch(targetType)) != null) {
        targetType = match![1]!.trim(); // ignore: parameter_assignments
        return Map<String, BaseType>.fromIterables(
          value.keys as Iterable<String>,
          value.values.map((dynamic v) => deserialize<BaseType, BaseType>(
              v, targetType,
              growable: growable)),
        ) as ReturnType;
      }
      break;
  }
  throw Exception('Cannot deserialize');
}
