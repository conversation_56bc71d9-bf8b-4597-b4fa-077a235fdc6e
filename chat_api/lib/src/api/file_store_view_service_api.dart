//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

// ignore: unused_import
import 'dart:convert';
import 'package:chat_api/src/deserialize.dart';
import 'package:dio/dio.dart';

import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/v3_get_dm_media_file_response.dart';
import 'package:chat_api/src/model/v3_get_media_file_response.dart';
import 'package:chat_api/src/model/v3_list_dm_media_file_fragments_response.dart';
import 'package:chat_api/src/model/v3_list_dm_media_files_response.dart';
import 'package:chat_api/src/model/v3_list_media_file_fragments_response.dart';
import 'package:chat_api/src/model/v3_list_media_files_response.dart';

class FileStoreViewServiceApi {
  final Dio _dio;

  const FileStoreViewServiceApi(this._dio);

  /// Get file data in dm channel
  ///
  ///
  /// Parameters:
  /// * [userId] - The user identify send request
  /// * [fileId] - The file identify
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3GetDMMediaFileResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3GetDMMediaFileResponse>> getDMMediaFile({
    String? userId,
    String? fileId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/FileStoreView/GetDMMediaFile';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (userId != null) r'userId': userId,
      if (fileId != null) r'fileId': fileId,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3GetDMMediaFileResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3GetDMMediaFileResponse, V3GetDMMediaFileResponse>(
              rawData, 'V3GetDMMediaFileResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3GetDMMediaFileResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// Get file data in channel
  ///
  ///
  /// Parameters:
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel identify
  /// * [fileId] - The file identify
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3GetMediaFileResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3GetMediaFileResponse>> getMediaFile({
    String? workspaceId,
    String? channelId,
    String? fileId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/FileStoreView/GetMediaFile';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
      if (fileId != null) r'fileId': fileId,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3GetMediaFileResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3GetMediaFileResponse, V3GetMediaFileResponse>(
              rawData, 'V3GetMediaFileResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3GetMediaFileResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// Get list file fragments in dm channel
  ///
  ///
  /// Parameters:
  /// * [userId] - The user identify
  /// * [offsetId] - fileId
  /// * [limit] - maximum 5k
  /// * [addOffset] - based pagination,  Ex: Loading 20 files, older than message with file_id:        {offset_id: file_id, add_offset: 0, limit: 20}      Loading 20 files, newer than message with file_id:        {offset_id: file_id, add_offset: -20, limit: 20}      Loading 20 files around message with file_id:        {offset_id: file_id, add_offset: -10, limit: 20}
  /// * [maxId] - Can be used to only return results with ID strictly smaller than max_id
  /// * [minId] - Can be used to only return results with ID strictly greater than min_id
  /// * [maxDate] - Can be used to only return results that are older than max_date
  /// * [minDate] - Can be used to only return results with are newer than min_date
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3ListDMMediaFileFragmentsResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3ListDMMediaFileFragmentsResponse>>
      listDMMediaFileFragments({
    String? userId,
    String? offsetId,
    int? limit,
    int? addOffset,
    String? maxId,
    String? minId,
    String? maxDate,
    String? minDate,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/FileStoreView/ListDMMediaFileFragments';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (userId != null) r'userId': userId,
      if (offsetId != null) r'offsetId': offsetId,
      if (limit != null) r'limit': limit,
      if (addOffset != null) r'addOffset': addOffset,
      if (maxId != null) r'maxId': maxId,
      if (minId != null) r'minId': minId,
      if (maxDate != null) r'maxDate': maxDate,
      if (minDate != null) r'minDate': minDate,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3ListDMMediaFileFragmentsResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3ListDMMediaFileFragmentsResponse,
                  V3ListDMMediaFileFragmentsResponse>(
              rawData, 'V3ListDMMediaFileFragmentsResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3ListDMMediaFileFragmentsResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// Get list files data in dm channel
  ///
  ///
  /// Parameters:
  /// * [userId] - The user identify
  /// * [offsetId] - fileId
  /// * [limit] - maximum 5k
  /// * [addOffset] - based pagination,  Ex: Loading 20 files, older than message with file_id:        {offset_id: file_id, add_offset: 0, limit: 20}      Loading 20 files, newer than message with file_id:        {offset_id: file_id, add_offset: -20, limit: 20}      Loading 20 files around message with file_id:        {offset_id: file_id, add_offset: -10, limit: 20}
  /// * [maxId] - Can be used to only return results with ID strictly smaller than max_id
  /// * [minId] - Can be used to only return results with ID strictly greater than min_id
  /// * [maxDate] - Can be used to only return results that are older than max_date
  /// * [minDate] - Can be used to only return results with are newer than min_date
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3ListDMMediaFilesResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3ListDMMediaFilesResponse>> listDMMediaFiles({
    String? userId,
    String? offsetId,
    int? limit,
    int? addOffset,
    String? maxId,
    String? minId,
    String? maxDate,
    String? minDate,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/FileStoreView/ListDMMediaFiles';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (userId != null) r'userId': userId,
      if (offsetId != null) r'offsetId': offsetId,
      if (limit != null) r'limit': limit,
      if (addOffset != null) r'addOffset': addOffset,
      if (maxId != null) r'maxId': maxId,
      if (minId != null) r'minId': minId,
      if (maxDate != null) r'maxDate': maxDate,
      if (minDate != null) r'minDate': minDate,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3ListDMMediaFilesResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3ListDMMediaFilesResponse, V3ListDMMediaFilesResponse>(
              rawData, 'V3ListDMMediaFilesResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3ListDMMediaFilesResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// Get list file fragments in channel
  ///
  ///
  /// Parameters:
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel identify
  /// * [offsetId] - fileId
  /// * [limit] - maximum 5k
  /// * [addOffset] - based pagination,  Ex: Loading 20 files, older than message with file_id:        {offset_id: file_id, add_offset: 0, limit: 20}      Loading 20 files, newer than message with file_id:        {offset_id: file_id, add_offset: -20, limit: 20}      Loading 20 files around message with file_id:        {offset_id: file_id, add_offset: -10, limit: 20}
  /// * [maxId] - Can be used to only return results with ID strictly smaller than max_id
  /// * [minId] - Can be used to only return results with ID strictly greater than min_id
  /// * [maxDate] - Can be used to only return results that are older than max_date
  /// * [minDate] - Can be used to only return results with are newer than min_date
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3ListMediaFileFragmentsResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3ListMediaFileFragmentsResponse>> listMediaFileFragments({
    String? workspaceId,
    String? channelId,
    String? offsetId,
    int? limit,
    int? addOffset,
    String? maxId,
    String? minId,
    String? maxDate,
    String? minDate,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/FileStoreView/ListMediaFileFragments';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
      if (offsetId != null) r'offsetId': offsetId,
      if (limit != null) r'limit': limit,
      if (addOffset != null) r'addOffset': addOffset,
      if (maxId != null) r'maxId': maxId,
      if (minId != null) r'minId': minId,
      if (maxDate != null) r'maxDate': maxDate,
      if (minDate != null) r'minDate': minDate,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3ListMediaFileFragmentsResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3ListMediaFileFragmentsResponse,
                  V3ListMediaFileFragmentsResponse>(
              rawData, 'V3ListMediaFileFragmentsResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3ListMediaFileFragmentsResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// Get list files data in channel
  ///
  ///
  /// Parameters:
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel identify
  /// * [offsetId] - fileId
  /// * [limit] - maximum 5k
  /// * [addOffset] - based pagination,  Ex: Loading 20 files, older than message with file_id:        {offset_id: file_id, add_offset: 0, limit: 20}      Loading 20 files, newer than message with file_id:        {offset_id: file_id, add_offset: -20, limit: 20}      Loading 20 files around message with file_id:        {offset_id: file_id, add_offset: -10, limit: 20}
  /// * [maxId] - Can be used to only return results with ID strictly smaller than max_id
  /// * [minId] - Can be used to only return results with ID strictly greater than min_id
  /// * [maxDate] - Can be used to only return results that are older than max_date
  /// * [minDate] - Can be used to only return results with are newer than min_date
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3ListMediaFilesResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3ListMediaFilesResponse>> listMediaFiles({
    String? workspaceId,
    String? channelId,
    String? offsetId,
    int? limit,
    int? addOffset,
    String? maxId,
    String? minId,
    String? maxDate,
    String? minDate,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/FileStoreView/ListMediaFiles';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
      if (offsetId != null) r'offsetId': offsetId,
      if (limit != null) r'limit': limit,
      if (addOffset != null) r'addOffset': addOffset,
      if (maxId != null) r'maxId': maxId,
      if (minId != null) r'minId': minId,
      if (maxDate != null) r'maxDate': maxDate,
      if (minDate != null) r'minDate': minDate,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3ListMediaFilesResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3ListMediaFilesResponse, V3ListMediaFilesResponse>(
              rawData, 'V3ListMediaFilesResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3ListMediaFilesResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }
}
