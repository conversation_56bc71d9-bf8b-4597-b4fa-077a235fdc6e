//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

// ignore: unused_import
import 'dart:convert';
import 'package:chat_api/src/deserialize.dart';
import 'package:dio/dio.dart';

import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/stream_result_of_v3_sync_members_response.dart';
import 'package:chat_api/src/model/v3_get_member_response.dart';
import 'package:chat_api/src/model/v3_list_banned_users_response.dart';
import 'package:chat_api/src/model/v3_list_members_response.dart';

class MemberViewServiceApi {
  final Dio _dio;

  const MemberViewServiceApi(this._dio);

  /// Get the information of member and user details who have joined the group.
  ///
  ///
  /// Parameters:
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel identify
  /// * [userId] - The user identify
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3GetMemberResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3GetMemberResponse>> getMember({
    String? workspaceId,
    String? channelId,
    String? userId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/MemberView/GetMember';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
      if (userId != null) r'userId': userId,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3GetMemberResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3GetMemberResponse, V3GetMemberResponse>(
              rawData, 'V3GetMemberResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3GetMemberResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// List the information of members and user details who have banned from the group.
  ///
  ///
  /// Parameters:
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel identify
  /// * [limit] - It is the limit of the number of records returned in one request The allowable value ranges from 1 to 500, with a default value of 100.
  /// * [nextPageToken] - The next page of current page
  /// * [prevPageToken] - The previous page of current page
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3ListBannedUsersResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3ListBannedUsersResponse>> listBannedUsers({
    String? workspaceId,
    String? channelId,
    int? limit,
    String? nextPageToken,
    String? prevPageToken,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/MemberView/ListBannedUsers';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
      if (limit != null) r'limit': limit,
      if (nextPageToken != null) r'nextPageToken': nextPageToken,
      if (prevPageToken != null) r'prevPageToken': prevPageToken,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3ListBannedUsersResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3ListBannedUsersResponse, V3ListBannedUsersResponse>(
              rawData, 'V3ListBannedUsersResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3ListBannedUsersResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// List the information of members and user details who have joined the group.
  ///
  ///
  /// Parameters:
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel identify
  /// * [limit] - It is the limit of the number of records returned in one request The allowable value ranges from 1 to 500, with a default value of 100.
  /// * [nextPageToken] - The last token of this page will be the next_page_token of the current page
  /// * [prevPageToken] - The first token of this page will be the prev_page_token of the current page
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3ListMembersResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3ListMembersResponse>> listMembers({
    String? workspaceId,
    String? channelId,
    int? limit,
    String? nextPageToken,
    String? prevPageToken,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/MemberView/ListMembers';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
      if (limit != null) r'limit': limit,
      if (nextPageToken != null) r'nextPageToken': nextPageToken,
      if (prevPageToken != null) r'prevPageToken': prevPageToken,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3ListMembersResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3ListMembersResponse, V3ListMembersResponse>(
              rawData, 'V3ListMembersResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3ListMembersResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

  /// Get the list of member update or deleted by after update time
  ///
  ///
  /// Parameters:
  /// * [updateTimeAfter] - Datetime ISO String
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel identify
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [StreamResultOfV3SyncMembersResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<StreamResultOfV3SyncMembersResponse>> syncMembers({
    String? updateTimeAfter,
    String? workspaceId,
    String? channelId,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/MemberView/SyncMembers';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (updateTimeAfter != null) r'updateTimeAfter': updateTimeAfter,
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    StreamResultOfV3SyncMembersResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<StreamResultOfV3SyncMembersResponse,
                  StreamResultOfV3SyncMembersResponse>(
              rawData, 'StreamResultOfV3SyncMembersResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<StreamResultOfV3SyncMembersResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }
}
