//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

// ignore: unused_import
import 'dart:convert';
import 'package:chat_api/src/deserialize.dart';
import 'package:dio/dio.dart';

import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/v3_list_channel_audit_logs_response.dart';

class AuditLogViewServiceApi {
  final Dio _dio;

  const AuditLogViewServiceApi(this._dio);

  /// The list of all audit log information of channel 1-N
  ///
  ///
  /// Parameters:
  /// * [workspaceId] - The workspace identify
  /// * [channelId] - The channel Identify
  /// * [limit] - It is the limit of the number of records returned in one request
  /// * [nextPageToken] - The last audit log identify of this page will be the next_page_token of the next page
  /// * [prevPageToken] - The first audit log identify of this page will be the prev_page_token of the next page
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [V3ListChannelAuditLogsResponse] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<V3ListChannelAuditLogsResponse>> listChannelAuditLogs({
    String? workspaceId,
    String? channelId,
    int? limit,
    String? nextPageToken,
    String? prevPageToken,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/AuditLogView/ListChannelAuditLogs';
    final _options = Options(
      method: r'GET',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[],
        ...?extra,
      },
      validateStatus: validateStatus,
    );

    final _queryParameters = <String, dynamic>{
      if (workspaceId != null) r'workspaceId': workspaceId,
      if (channelId != null) r'channelId': channelId,
      if (limit != null) r'limit': limit,
      if (nextPageToken != null) r'nextPageToken': nextPageToken,
      if (prevPageToken != null) r'prevPageToken': prevPageToken,
    };

    final _response = await _dio.request<Object>(
      _path,
      options: _options,
      queryParameters: _queryParameters,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    V3ListChannelAuditLogsResponse? _responseData;

    try {
      final rawData = _response.data;
      _responseData = rawData == null
          ? null
          : deserialize<V3ListChannelAuditLogsResponse,
                  V3ListChannelAuditLogsResponse>(
              rawData, 'V3ListChannelAuditLogsResponse',
              growable: true);
    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<V3ListChannelAuditLogsResponse>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }
}
