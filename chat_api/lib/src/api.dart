//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'package:dio/dio.dart';
import 'package:chat_api/src/auth/api_key_auth.dart';
import 'package:chat_api/src/auth/basic_auth.dart';
import 'package:chat_api/src/auth/bearer_auth.dart';
import 'package:chat_api/src/auth/oauth.dart';
import 'package:chat_api/src/api/audit_log_view_service_api.dart';
import 'package:chat_api/src/api/auth_service_api.dart';
import 'package:chat_api/src/api/avatar_frame_service_api.dart';
import 'package:chat_api/src/api/call_service_api.dart';
import 'package:chat_api/src/api/channel_service_api.dart';
import 'package:chat_api/src/api/channel_view_service_api.dart';
import 'package:chat_api/src/api/crawler_service_api.dart';
import 'package:chat_api/src/api/file_store_service_api.dart';
import 'package:chat_api/src/api/file_store_view_service_api.dart';
import 'package:chat_api/src/api/friend_service_api.dart';
import 'package:chat_api/src/api/friend_view_service_api.dart';
import 'package:chat_api/src/api/hashcash_service_api.dart';
import 'package:chat_api/src/api/internal_faker_service_api.dart';
import 'package:chat_api/src/api/invitation_service_api.dart';
import 'package:chat_api/src/api/invitation_view_service_api.dart';
import 'package:chat_api/src/api/member_service_api.dart';
import 'package:chat_api/src/api/member_view_service_api.dart';
import 'package:chat_api/src/api/message_service_api.dart';
import 'package:chat_api/src/api/message_view_service_api.dart';
import 'package:chat_api/src/api/notification_service_api.dart';
import 'package:chat_api/src/api/premium_channel_service_api.dart';
import 'package:chat_api/src/api/readstate_view_service_api.dart';
import 'package:chat_api/src/api/ringback_tone_service_api.dart';
import 'package:chat_api/src/api/search_service_api.dart';
import 'package:chat_api/src/api/sticker_view_service_api.dart';
import 'package:chat_api/src/api/suggestion_service_api.dart';
import 'package:chat_api/src/api/user_connect_service_api.dart';
import 'package:chat_api/src/api/user_management_service_api.dart';
import 'package:chat_api/src/api/user_profile_service_api.dart';
import 'package:chat_api/src/api/user_report_service_api.dart';
import 'package:chat_api/src/api/user_setting_service_api.dart';
import 'package:chat_api/src/api/user_view_service_api.dart';
import 'package:chat_api/src/api/voice_service_api.dart';
import 'package:chat_api/src/api/websocket_manager_service_api.dart';

class ChatApi {
  static const String basePath = r'http://localhost';

  final Dio dio;
  ChatApi({
    Dio? dio,
    String? basePathOverride,
    List<Interceptor>? interceptors,
  }) : this.dio = dio ??
            Dio(BaseOptions(
              baseUrl: basePathOverride ?? basePath,
              connectTimeout: const Duration(milliseconds: 5000),
              receiveTimeout: const Duration(milliseconds: 3000),
            )) {
    if (interceptors == null) {
      this.dio.interceptors.addAll([
        OAuthInterceptor(),
        BasicAuthInterceptor(),
        BearerAuthInterceptor(),
        ApiKeyAuthInterceptor(),
      ]);
    } else {
      this.dio.interceptors.addAll(interceptors);
    }
  }

  void setOAuthToken(String name, String token) {
    if (this.dio.interceptors.any((i) => i is OAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is OAuthInterceptor)
              as OAuthInterceptor)
          .tokens[name] = token;
    }
  }

  void setBearerAuth(String name, String token) {
    if (this.dio.interceptors.any((i) => i is BearerAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is BearerAuthInterceptor)
              as BearerAuthInterceptor)
          .tokens[name] = token;
    }
  }

  void setBasicAuth(String name, String username, String password) {
    if (this.dio.interceptors.any((i) => i is BasicAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is BasicAuthInterceptor)
              as BasicAuthInterceptor)
          .authInfo[name] = BasicAuthInfo(username, password);
    }
  }

  void setApiKey(String name, String apiKey) {
    if (this.dio.interceptors.any((i) => i is ApiKeyAuthInterceptor)) {
      (this
                  .dio
                  .interceptors
                  .firstWhere((element) => element is ApiKeyAuthInterceptor)
              as ApiKeyAuthInterceptor)
          .apiKeys[name] = apiKey;
    }
  }

  /// Get AuditLogViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  AuditLogViewServiceApi getAuditLogViewServiceApi() {
    return AuditLogViewServiceApi(dio);
  }

  /// Get AuthServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  AuthServiceApi getAuthServiceApi() {
    return AuthServiceApi(dio);
  }

  /// Get AvatarFrameServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  AvatarFrameServiceApi getAvatarFrameServiceApi() {
    return AvatarFrameServiceApi(dio);
  }

  /// Get CallServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  CallServiceApi getCallServiceApi() {
    return CallServiceApi(dio);
  }

  /// Get ChannelServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  ChannelServiceApi getChannelServiceApi() {
    return ChannelServiceApi(dio);
  }

  /// Get ChannelViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  ChannelViewServiceApi getChannelViewServiceApi() {
    return ChannelViewServiceApi(dio);
  }

  /// Get CrawlerServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  CrawlerServiceApi getCrawlerServiceApi() {
    return CrawlerServiceApi(dio);
  }

  /// Get FileStoreServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  FileStoreServiceApi getFileStoreServiceApi() {
    return FileStoreServiceApi(dio);
  }

  /// Get FileStoreViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  FileStoreViewServiceApi getFileStoreViewServiceApi() {
    return FileStoreViewServiceApi(dio);
  }

  /// Get FriendServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  FriendServiceApi getFriendServiceApi() {
    return FriendServiceApi(dio);
  }

  /// Get FriendViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  FriendViewServiceApi getFriendViewServiceApi() {
    return FriendViewServiceApi(dio);
  }

  /// Get HashcashServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  HashcashServiceApi getHashcashServiceApi() {
    return HashcashServiceApi(dio);
  }

  /// Get InternalFakerServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  InternalFakerServiceApi getInternalFakerServiceApi() {
    return InternalFakerServiceApi(dio);
  }

  /// Get InvitationServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  InvitationServiceApi getInvitationServiceApi() {
    return InvitationServiceApi(dio);
  }

  /// Get InvitationViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  InvitationViewServiceApi getInvitationViewServiceApi() {
    return InvitationViewServiceApi(dio);
  }

  /// Get MemberServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  MemberServiceApi getMemberServiceApi() {
    return MemberServiceApi(dio);
  }

  /// Get MemberViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  MemberViewServiceApi getMemberViewServiceApi() {
    return MemberViewServiceApi(dio);
  }

  /// Get MessageServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  MessageServiceApi getMessageServiceApi() {
    return MessageServiceApi(dio);
  }

  /// Get MessageViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  MessageViewServiceApi getMessageViewServiceApi() {
    return MessageViewServiceApi(dio);
  }

  /// Get NotificationServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  NotificationServiceApi getNotificationServiceApi() {
    return NotificationServiceApi(dio);
  }

  /// Get PremiumChannelServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  PremiumChannelServiceApi getPremiumChannelServiceApi() {
    return PremiumChannelServiceApi(dio);
  }

  /// Get ReadstateViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  ReadstateViewServiceApi getReadstateViewServiceApi() {
    return ReadstateViewServiceApi(dio);
  }

  /// Get RingbackToneServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  RingbackToneServiceApi getRingbackToneServiceApi() {
    return RingbackToneServiceApi(dio);
  }

  /// Get SearchServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  SearchServiceApi getSearchServiceApi() {
    return SearchServiceApi(dio);
  }

  /// Get StickerViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  StickerViewServiceApi getStickerViewServiceApi() {
    return StickerViewServiceApi(dio);
  }

  /// Get SuggestionServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  SuggestionServiceApi getSuggestionServiceApi() {
    return SuggestionServiceApi(dio);
  }

  /// Get UserConnectServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  UserConnectServiceApi getUserConnectServiceApi() {
    return UserConnectServiceApi(dio);
  }

  /// Get UserManagementServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  UserManagementServiceApi getUserManagementServiceApi() {
    return UserManagementServiceApi(dio);
  }

  /// Get UserProfileServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  UserProfileServiceApi getUserProfileServiceApi() {
    return UserProfileServiceApi(dio);
  }

  /// Get UserReportServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  UserReportServiceApi getUserReportServiceApi() {
    return UserReportServiceApi(dio);
  }

  /// Get UserSettingServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  UserSettingServiceApi getUserSettingServiceApi() {
    return UserSettingServiceApi(dio);
  }

  /// Get UserViewServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  UserViewServiceApi getUserViewServiceApi() {
    return UserViewServiceApi(dio);
  }

  /// Get VoiceServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  VoiceServiceApi getVoiceServiceApi() {
    return VoiceServiceApi(dio);
  }

  /// Get WebsocketManagerServiceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  WebsocketManagerServiceApi getWebsocketManagerServiceApi() {
    return WebsocketManagerServiceApi(dio);
  }
}
