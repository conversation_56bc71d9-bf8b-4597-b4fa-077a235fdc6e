//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_avatar_type_enum.dart';
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_suggest_friend.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SuggestFriend {
  /// Returns a new [V3SuggestFriend] instance.
  V3SuggestFriend({
    this.userId,
    this.displayName,
    this.username,
    this.avatar,
    this.videoAvatar,
    this.avatarType,
    this.decoratedAvatar,
    this.userBadgeType,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON><PERSON>(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @Json<PERSON>ey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @JsonKey(
    name: r'videoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? videoAvatar;

  @JsonKey(
    name: r'avatarType',
    required: false,
    includeIfNull: false,
  )
  final V3UserAvatarTypeEnum? avatarType;

  @JsonKey(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @JsonKey(
    name: r'userBadgeType',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? userBadgeType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SuggestFriend &&
          other.userId == userId &&
          other.displayName == displayName &&
          other.username == username &&
          other.avatar == avatar &&
          other.videoAvatar == videoAvatar &&
          other.avatarType == avatarType &&
          other.decoratedAvatar == decoratedAvatar &&
          other.userBadgeType == userBadgeType;

  @override
  int get hashCode =>
      userId.hashCode +
      displayName.hashCode +
      username.hashCode +
      avatar.hashCode +
      videoAvatar.hashCode +
      avatarType.hashCode +
      decoratedAvatar.hashCode +
      userBadgeType.hashCode;

  factory V3SuggestFriend.fromJson(Map<String, dynamic> json) =>
      _$V3SuggestFriendFromJson(json);

  Map<String, dynamic> toJson() => _$V3SuggestFriendToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
