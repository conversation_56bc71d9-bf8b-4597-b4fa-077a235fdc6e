//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'common_authenticator_selection_criteria.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonAuthenticatorSelectionCriteria {
  /// Returns a new [CommonAuthenticatorSelectionCriteria] instance.
  CommonAuthenticatorSelectionCriteria({
    this.authenticatorAttachment,
    this.requireResidentKey,
    this.userVerification,
  });

  /// The preferred attachment modality for the authenticator.
  @JsonKey(
    name: r'authenticatorAttachment',
    required: false,
    includeIfNull: false,
  )
  final String? authenticatorAttachment;

  @JsonKey(
    name: r'requireResidentKey',
    required: false,
    includeIfNull: false,
  )
  final bool? requireResidentKey;

  /// The preferred user verification method for the authenticator.
  @JsonKey(
    name: r'userVerification',
    required: false,
    includeIfNull: false,
  )
  final String? userVerification;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonAuthenticatorSelectionCriteria &&
          other.authenticatorAttachment == authenticatorAttachment &&
          other.requireResidentKey == requireResidentKey &&
          other.userVerification == userVerification;

  @override
  int get hashCode =>
      authenticatorAttachment.hashCode +
      requireResidentKey.hashCode +
      userVerification.hashCode;

  factory CommonAuthenticatorSelectionCriteria.fromJson(
          Map<String, dynamic> json) =>
      _$CommonAuthenticatorSelectionCriteriaFromJson(json);

  Map<String, dynamic> toJson() =>
      _$CommonAuthenticatorSelectionCriteriaToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
