//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_phone_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserPhoneRequest {
  /// Returns a new [V3UpdateUserPhoneRequest] instance.
  V3UpdateUserPhoneRequest({
    this.phoneNumber,
  });

  @JsonKey(
    name: r'phoneNumber',
    required: false,
    includeIfNull: false,
  )
  final String? phoneNumber;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserPhoneRequest && other.phoneNumber == phoneNumber;

  @override
  int get hashCode => phoneNumber.hashCode;

  factory V3UpdateUserPhoneRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserPhoneRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserPhoneRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
