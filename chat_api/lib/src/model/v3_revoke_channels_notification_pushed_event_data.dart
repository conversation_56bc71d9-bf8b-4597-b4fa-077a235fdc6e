//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_revoke_channels_notification_pushed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RevokeChannelsNotificationPushedEventData {
  /// Returns a new [V3RevokeChannelsNotificationPushedEventData] instance.
  V3RevokeChannelsNotificationPushedEventData({
    this.channelsIds,
  });

  @JsonKey(
    name: r'channelsIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? channelsIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RevokeChannelsNotificationPushedEventData &&
          other.channelsIds == channelsIds;

  @override
  int get hashCode => channelsIds.hashCode;

  factory V3RevokeChannelsNotificationPushedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3RevokeChannelsNotificationPushedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3RevokeChannelsNotificationPushedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
