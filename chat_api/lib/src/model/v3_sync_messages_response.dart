//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_message_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sync_messages_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SyncMessagesResponse {
  /// Returns a new [V3SyncMessagesResponse] instance.
  V3SyncMessagesResponse({
    this.data,
    this.messagesDeleted,
    this.lastMessageDeleted,
    this.syncTime,
    this.includes,
  });

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3MessageData>? data;

  @Json<PERSON>ey(
    name: r'messagesDeleted',
    required: false,
    includeIfNull: false,
  )
  final List<String>? messagesDeleted;

  @Json<PERSON>ey(
    name: r'lastMessageDeleted',
    required: false,
    includeIfNull: false,
  )
  final String? lastMessageDeleted;

  @Json<PERSON>ey(
    name: r'syncTime',
    required: false,
    includeIfNull: false,
  )
  final String? syncTime;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SyncMessagesResponse &&
          other.data == data &&
          other.messagesDeleted == messagesDeleted &&
          other.lastMessageDeleted == lastMessageDeleted &&
          other.syncTime == syncTime &&
          other.includes == includes;

  @override
  int get hashCode =>
      data.hashCode +
      messagesDeleted.hashCode +
      lastMessageDeleted.hashCode +
      syncTime.hashCode +
      includes.hashCode;

  factory V3SyncMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SyncMessagesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SyncMessagesResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
