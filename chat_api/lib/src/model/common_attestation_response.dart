//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'common_attestation_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonAttestationResponse {
  /// Returns a new [CommonAttestationResponse] instance.
  CommonAttestationResponse({
    this.clientDataJSON,
    this.attestationObject,
  });

  @J<PERSON><PERSON>ey(
    name: r'clientDataJSON',
    required: false,
    includeIfNull: false,
  )
  final String? clientDataJSON;

  @JsonKey(
    name: r'attestationObject',
    required: false,
    includeIfNull: false,
  )
  final String? attestationObject;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonAttestationResponse &&
          other.clientDataJSON == clientDataJSON &&
          other.attestationObject == attestationObject;

  @override
  int get hashCode => clientDataJSON.hashCode + attestationObject.hashCode;

  factory CommonAttestationResponse.fromJson(Map<String, dynamic> json) =>
      _$CommonAttestationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CommonAttestationResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
