//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_mark_dmas_read_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MarkDMAsReadRequest {
  /// Returns a new [V3MarkDMAsReadRequest] instance.
  V3MarkDMAsReadRequest({
    this.userId,
    this.messageId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MarkDMAsReadRequest &&
          other.userId == userId &&
          other.messageId == messageId;

  @override
  int get hashCode => userId.hashCode + messageId.hashCode;

  factory V3MarkDMAsReadRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MarkDMAsReadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MarkDMAsReadRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
