//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_friends_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchFriendsRequest {
  /// Returns a new [V3SearchFriendsRequest] instance.
  V3SearchFriendsRequest({
    this.keyword,
    this.limit,
    this.nextPageToken,
    this.prevPageToken,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'keyword',
    required: false,
    includeIfNull: false,
  )
  final String? keyword;

  /// The maximum number of search results to retrieve.
  @Json<PERSON>ey(
    name: r'limit',
    required: false,
    includeIfNull: false,
  )
  final int? limit;

  /// A token to retrieve the next page of search results.
  @Json<PERSON>ey(
    name: r'nextPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? nextPageToken;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'prevPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? prevPageToken;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchFriendsRequest &&
          other.keyword == keyword &&
          other.limit == limit &&
          other.nextPageToken == nextPageToken &&
          other.prevPageToken == prevPageToken;

  @override
  int get hashCode =>
      keyword.hashCode +
      limit.hashCode +
      nextPageToken.hashCode +
      prevPageToken.hashCode;

  factory V3SearchFriendsRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SearchFriendsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchFriendsRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
