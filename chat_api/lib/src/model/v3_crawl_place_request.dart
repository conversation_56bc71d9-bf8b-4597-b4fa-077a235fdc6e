//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_crawl_place_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CrawlPlaceRequest {
  /// Returns a new [V3CrawlPlaceRequest] instance.
  V3CrawlPlaceRequest({
    this.latitude,
    this.longitude,
  });

  @JsonKey(
    name: r'latitude',
    required: false,
    includeIfNull: false,
  )
  final String? latitude;

  @JsonKey(
    name: r'longitude',
    required: false,
    includeIfNull: false,
  )
  final String? longitude;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CrawlPlaceRequest &&
          other.latitude == latitude &&
          other.longitude == longitude;

  @override
  int get hashCode => latitude.hashCode + longitude.hashCode;

  factory V3CrawlPlaceRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CrawlPlaceRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CrawlPlaceRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
