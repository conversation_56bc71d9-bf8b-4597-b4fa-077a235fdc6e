//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_assertion_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'common_assertion_result.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonAssertionResult {
  /// Returns a new [CommonAssertionResult] instance.
  CommonAssertionResult({
    this.id,
    this.rawId,
    this.type,
    this.response,
  });

  /// The result's identification.
  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  /// The raw credential identifier used during the assertion process.
  @JsonKey(
    name: r'rawId',
    required: false,
    includeIfNull: false,
  )
  final String? rawId;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final String? type;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'response',
    required: false,
    includeIfNull: false,
  )
  final CommonAssertionResponse? response;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonAssertionResult &&
          other.id == id &&
          other.rawId == rawId &&
          other.type == type &&
          other.response == response;

  @override
  int get hashCode =>
      id.hashCode + rawId.hashCode + type.hashCode + response.hashCode;

  factory CommonAssertionResult.fromJson(Map<String, dynamic> json) =>
      _$CommonAssertionResultFromJson(json);

  Map<String, dynamic> toJson() => _$CommonAssertionResultToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
