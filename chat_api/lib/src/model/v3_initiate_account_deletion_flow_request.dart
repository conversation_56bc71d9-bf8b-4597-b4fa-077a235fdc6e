//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_account_deletion_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateAccountDeletionFlowRequest {
  /// Returns a new [V3InitiateAccountDeletionFlowRequest] instance.
  V3InitiateAccountDeletionFlowRequest({
    this.reqChallenge,
  });

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateAccountDeletionFlowRequest &&
          other.reqChallenge == reqChallenge;

  @override
  int get hashCode => reqChallenge.hashCode;

  factory V3InitiateAccountDeletionFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateAccountDeletionFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateAccountDeletionFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
