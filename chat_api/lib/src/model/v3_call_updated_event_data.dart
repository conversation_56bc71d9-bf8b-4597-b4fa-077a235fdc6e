//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_call_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_call_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CallUpdatedEventData {
  /// Returns a new [V3CallUpdatedEventData] instance.
  V3CallUpdatedEventData({
    this.callData,
  });

  @JsonKey(
    name: r'callData',
    required: false,
    includeIfNull: false,
  )
  final V3CallData? callData;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CallUpdatedEventData && other.callData == callData;

  @override
  int get hashCode => callData.hashCode;

  factory V3CallUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3CallUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CallUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
