//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_complete_file_collection_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CompleteFileCollectionRequest {
  /// Returns a new [V3CompleteFileCollectionRequest] instance.
  V3CompleteFileCollectionRequest({
    this.collectionId,
  });

  @JsonKey(
    name: r'collectionId',
    required: false,
    includeIfNull: false,
  )
  final String? collectionId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CompleteFileCollectionRequest &&
          other.collectionId == collectionId;

  @override
  int get hashCode => collectionId.hashCode;

  factory V3CompleteFileCollectionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CompleteFileCollectionRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3CompleteFileCollectionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
