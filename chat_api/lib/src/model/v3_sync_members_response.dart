//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_member_data.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sync_members_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SyncMembersResponse {
  /// Returns a new [V3SyncMembersResponse] instance.
  V3SyncMembersResponse({
    this.data,
    this.memberDeleted,
    this.syncTime,
    this.includes,
  });

  /// List of values for the Friend interface.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3MemberData>? data;

  /// List of userId as member deleted.
  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'memberDeleted',
    required: false,
    includeIfNull: false,
  )
  final List<String>? memberDeleted;

  @J<PERSON><PERSON><PERSON>(
    name: r'syncTime',
    required: false,
    includeIfNull: false,
  )
  final String? syncTime;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SyncMembersResponse &&
          other.data == data &&
          other.memberDeleted == memberDeleted &&
          other.syncTime == syncTime &&
          other.includes == includes;

  @override
  int get hashCode =>
      data.hashCode +
      memberDeleted.hashCode +
      syncTime.hashCode +
      includes.hashCode;

  factory V3SyncMembersResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SyncMembersResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SyncMembersResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
