//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_pow_challenge_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PowChallengeData {
  /// Returns a new [V3PowChallengeData] instance.
  V3PowChallengeData({
    this.level,
    this.challenge,
  });

  @JsonKey(
    name: r'level',
    required: false,
    includeIfNull: false,
  )
  final int? level;

  @Json<PERSON>ey(
    name: r'challenge',
    required: false,
    includeIfNull: false,
  )
  final String? challenge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PowChallengeData &&
          other.level == level &&
          other.challenge == challenge;

  @override
  int get hashCode => level.hashCode + challenge.hashCode;

  factory V3PowChallengeData.fromJson(Map<String, dynamic> json) =>
      _$V3PowChallengeDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3PowChallengeDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
