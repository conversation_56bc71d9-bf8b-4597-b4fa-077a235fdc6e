//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_create_invitation_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CreateInvitationRequest {
  /// Returns a new [V3CreateInvitationRequest] instance.
  V3CreateInvitationRequest({
    this.workspaceId,
    this.channelId,
    this.expiresIn,
    this.maxUses,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'expiresIn',
    required: false,
    includeIfNull: false,
  )
  final int? expiresIn;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'maxUses',
    required: false,
    includeIfNull: false,
  )
  final int? maxUses;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CreateInvitationRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.expiresIn == expiresIn &&
          other.maxUses == maxUses;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      expiresIn.hashCode +
      maxUses.hashCode;

  factory V3CreateInvitationRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CreateInvitationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CreateInvitationRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
