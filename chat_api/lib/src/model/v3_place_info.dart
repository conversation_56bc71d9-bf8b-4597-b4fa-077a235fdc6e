//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_place_info.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PlaceInfo {
  /// Returns a new [V3PlaceInfo] instance.
  V3PlaceInfo({
    this.description,
    this.thumbnailUrl,
  });

  @JsonKey(
    name: r'description',
    required: false,
    includeIfNull: false,
  )
  final String? description;

  @Json<PERSON>ey(
    name: r'thumbnailUrl',
    required: false,
    includeIfNull: false,
  )
  final String? thumbnailUrl;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PlaceInfo &&
          other.description == description &&
          other.thumbnailUrl == thumbnailUrl;

  @override
  int get hashCode => description.hashCode + thumbnailUrl.hashCode;

  factory V3PlaceInfo.fromJson(Map<String, dynamic> json) =>
      _$V3PlaceInfoFromJson(json);

  Map<String, dynamic> toJson() => _$V3PlaceInfoToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
