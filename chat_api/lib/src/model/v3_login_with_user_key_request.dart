//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_assertion_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_login_with_user_key_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LoginWithUserKeyRequest {
  /// Returns a new [V3LoginWithUserKeyRequest] instance.
  V3LoginWithUserKeyRequest({
    this.reqId,
    this.reqVerifier,
    this.assertion,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  /// The randomly generated value created by the client.
  @JsonKey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'assertion',
    required: false,
    includeIfNull: false,
  )
  final CommonAssertionResult? assertion;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LoginWithUserKeyRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.assertion == assertion;

  @override
  int get hashCode =>
      reqId.hashCode + reqVerifier.hashCode + assertion.hashCode;

  factory V3LoginWithUserKeyRequest.fromJson(Map<String, dynamic> json) =>
      _$V3LoginWithUserKeyRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3LoginWithUserKeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
