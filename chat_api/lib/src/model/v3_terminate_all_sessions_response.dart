//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/iamv3_terminate_session.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_terminate_all_sessions_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TerminateAllSessionsResponse {
  /// Returns a new [V3TerminateAllSessionsResponse] instance.
  V3TerminateAllSessionsResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<Iamv3TerminateSession>? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TerminateAllSessionsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3TerminateAllSessionsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3TerminateAllSessionsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3TerminateAllSessionsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
