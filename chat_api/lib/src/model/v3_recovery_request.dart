//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_public_key_credential_creation_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_recovery_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RecoveryRequest {
  /// Returns a new [V3RecoveryRequest] instance.
  V3RecoveryRequest({
    this.reqId,
    this.credentialCreationOptions,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'credentialCreationOptions',
    required: false,
    includeIfNull: false,
  )
  final CommonPublicKeyCredentialCreationOptions? credentialCreationOptions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RecoveryRequest &&
          other.reqId == reqId &&
          other.credentialCreationOptions == credentialCreationOptions;

  @override
  int get hashCode => reqId.hashCode + credentialCreationOptions.hashCode;

  factory V3RecoveryRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RecoveryRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RecoveryRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
