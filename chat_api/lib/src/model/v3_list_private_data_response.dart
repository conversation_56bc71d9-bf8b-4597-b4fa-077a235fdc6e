//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_private_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_private_data_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListPrivateDataResponse {
  /// Returns a new [V3ListPrivateDataResponse] instance.
  V3ListPrivateDataResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3PrivateData? data;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListPrivateDataResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + paging.hashCode;

  factory V3ListPrivateDataResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListPrivateDataResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListPrivateDataResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
