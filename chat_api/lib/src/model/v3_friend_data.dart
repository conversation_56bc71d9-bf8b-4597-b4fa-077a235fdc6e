//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_friend.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_friend_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3FriendData {
  /// Returns a new [V3FriendData] instance.
  V3FriendData({
    this.friend,
  });

  @JsonKey(
    name: r'friend',
    required: false,
    includeIfNull: false,
  )
  final V3Friend? friend;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is V3FriendData && other.friend == friend;

  @override
  int get hashCode => friend.hashCode;

  factory V3FriendData.fromJson(Map<String, dynamic> json) =>
      _$V3FriendDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3FriendDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
