//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_rtc_session_description.dart';
import 'package:chat_api/src/model/v3_ice_candidate.dart';
import 'package:chat_api/src/model/v3_user_avatar_type_enum.dart';
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_participant.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Participant {
  /// Returns a new [V3Participant] instance.
  V3Participant({
    this.userId,
    this.username,
    this.displayName,
    this.avatar,
    this.originalAvatar,
    this.iceCandidates,
    this.rtcSessionDescription,
    this.avatarType,
    this.userBadgeType,
    this.videoAvatar,
    this.decoratedAvatar,
    this.originalDecoratedAvatar,
    this.thumbVideoAvatar,
  });

  @Json<PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @JsonKey(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  @JsonKey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @JsonKey(
    name: r'originalAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? originalAvatar;

  @JsonKey(
    name: r'iceCandidates',
    required: false,
    includeIfNull: false,
  )
  final List<V3IceCandidate>? iceCandidates;

  @JsonKey(
    name: r'rtcSessionDescription',
    required: false,
    includeIfNull: false,
  )
  final V3RTCSessionDescription? rtcSessionDescription;

  @JsonKey(
    name: r'avatarType',
    required: false,
    includeIfNull: false,
  )
  final V3UserAvatarTypeEnum? avatarType;

  @JsonKey(
    name: r'userBadgeType',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? userBadgeType;

  @JsonKey(
    name: r'videoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? videoAvatar;

  @JsonKey(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @JsonKey(
    name: r'originalDecoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? originalDecoratedAvatar;

  @JsonKey(
    name: r'thumbVideoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? thumbVideoAvatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Participant &&
          other.userId == userId &&
          other.username == username &&
          other.displayName == displayName &&
          other.avatar == avatar &&
          other.originalAvatar == originalAvatar &&
          other.iceCandidates == iceCandidates &&
          other.rtcSessionDescription == rtcSessionDescription &&
          other.avatarType == avatarType &&
          other.userBadgeType == userBadgeType &&
          other.videoAvatar == videoAvatar &&
          other.decoratedAvatar == decoratedAvatar &&
          other.originalDecoratedAvatar == originalDecoratedAvatar &&
          other.thumbVideoAvatar == thumbVideoAvatar;

  @override
  int get hashCode =>
      userId.hashCode +
      username.hashCode +
      displayName.hashCode +
      avatar.hashCode +
      originalAvatar.hashCode +
      iceCandidates.hashCode +
      rtcSessionDescription.hashCode +
      avatarType.hashCode +
      userBadgeType.hashCode +
      videoAvatar.hashCode +
      decoratedAvatar.hashCode +
      originalDecoratedAvatar.hashCode +
      thumbVideoAvatar.hashCode;

  factory V3Participant.fromJson(Map<String, dynamic> json) =>
      _$V3ParticipantFromJson(json);

  Map<String, dynamic> toJson() => _$V3ParticipantToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
