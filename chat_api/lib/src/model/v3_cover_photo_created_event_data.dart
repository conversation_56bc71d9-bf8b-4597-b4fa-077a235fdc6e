//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cover_photo_created_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CoverPhotoCreatedEventData {
  /// Returns a new [V3CoverPhotoCreatedEventData] instance.
  V3CoverPhotoCreatedEventData({
    this.userId,
    this.cover,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON><PERSON>(
    name: r'cover',
    required: false,
    includeIfNull: false,
  )
  final String? cover;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CoverPhotoCreatedEventData &&
          other.userId == userId &&
          other.cover == cover;

  @override
  int get hashCode => userId.hashCode + cover.hashCode;

  factory V3CoverPhotoCreatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3CoverPhotoCreatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CoverPhotoCreatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
