//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/user_setting_session_expiration_setting.dart';
import 'package:chat_api/src/model/user_setting_recovery_code_setting.dart';
import 'package:chat_api/src/model/user_setting_security_key_setting.dart';
import 'package:chat_api/src/model/user_setting_smart_opt_setting.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_setting_security_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class UserSettingSecuritySetting {
  /// Returns a new [UserSettingSecuritySetting] instance.
  UserSettingSecuritySetting({
    this.smartOtp,
    this.recoveryCode,
    this.sessionExpiration,
    this.securityKey,
  });

  @JsonKey(
    name: r'smartOtp',
    required: false,
    includeIfNull: false,
  )
  final UserSettingSmartOptSetting? smartOtp;

  @JsonKey(
    name: r'recoveryCode',
    required: false,
    includeIfNull: false,
  )
  final UserSettingRecoveryCodeSetting? recoveryCode;

  @JsonKey(
    name: r'sessionExpiration',
    required: false,
    includeIfNull: false,
  )
  final UserSettingSessionExpirationSetting? sessionExpiration;

  @JsonKey(
    name: r'securityKey',
    required: false,
    includeIfNull: false,
  )
  final UserSettingSecurityKeySetting? securityKey;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingSecuritySetting &&
          other.smartOtp == smartOtp &&
          other.recoveryCode == recoveryCode &&
          other.sessionExpiration == sessionExpiration &&
          other.securityKey == securityKey;

  @override
  int get hashCode =>
      smartOtp.hashCode +
      recoveryCode.hashCode +
      sessionExpiration.hashCode +
      securityKey.hashCode;

  factory UserSettingSecuritySetting.fromJson(Map<String, dynamic> json) =>
      _$UserSettingSecuritySettingFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingSecuritySettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
