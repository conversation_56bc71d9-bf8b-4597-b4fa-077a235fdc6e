//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_forward_messages_to_dm_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ForwardMessagesToDMChannelRequest {
  /// Returns a new [V3ForwardMessagesToDMChannelRequest] instance.
  V3ForwardMessagesToDMChannelRequest({
    this.userId,
    this.originalMessageIds,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'originalMessageIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? originalMessageIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ForwardMessagesToDMChannelRequest &&
          other.userId == userId &&
          other.originalMessageIds == originalMessageIds;

  @override
  int get hashCode => userId.hashCode + originalMessageIds.hashCode;

  factory V3ForwardMessagesToDMChannelRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3ForwardMessagesToDMChannelRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ForwardMessagesToDMChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
