//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_subscribe_all_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SubscribeAllRequest {
  /// Returns a new [V3SubscribeAllRequest] instance.
  V3SubscribeAllRequest({
    this.appId,
    this.deviceToken,
  });

  /// The ID of the application associated with the channel.
  @JsonKey(
    name: r'appId',
    required: false,
    includeIfNull: false,
  )
  final String? appId;

  /// A token associated with the device making the subscription request.
  @JsonKey(
    name: r'deviceToken',
    required: false,
    includeIfNull: false,
  )
  final String? deviceToken;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SubscribeAllRequest &&
          other.appId == appId &&
          other.deviceToken == deviceToken;

  @override
  int get hashCode => appId.hashCode + deviceToken.hashCode;

  factory V3SubscribeAllRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SubscribeAllRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SubscribeAllRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
