//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_destination_cloud_event.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_notification_status_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelNotificationStatusUpdatedEventData {
  /// Returns a new [V3ChannelNotificationStatusUpdatedEventData] instance.
  V3ChannelNotificationStatusUpdatedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.notificationStatus,
    this.destination,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @Json<PERSON>ey(
    name: r'notificationStatus',
    required: false,
    includeIfNull: false,
  )
  final bool? notificationStatus;

  @JsonKey(
    name: r'destination',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelDestinationCloudEvent? destination;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelNotificationStatusUpdatedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.notificationStatus == notificationStatus &&
          other.destination == destination;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      notificationStatus.hashCode +
      destination.hashCode;

  factory V3ChannelNotificationStatusUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3ChannelNotificationStatusUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ChannelNotificationStatusUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
