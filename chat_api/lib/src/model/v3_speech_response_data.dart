//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_speech_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SpeechResponseData {
  /// Returns a new [V3SpeechResponseData] instance.
  V3SpeechResponseData({
    this.data,
    this.isFinal,
  });

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final String? data;

  @JsonKey(
    name: r'isFinal',
    required: false,
    includeIfNull: false,
  )
  final bool? isFinal;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SpeechResponseData &&
          other.data == data &&
          other.isFinal == isFinal;

  @override
  int get hashCode => data.hashCode + isFinal.hashCode;

  factory V3SpeechResponseData.fromJson(Map<String, dynamic> json) =>
      _$V3SpeechResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3SpeechResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
