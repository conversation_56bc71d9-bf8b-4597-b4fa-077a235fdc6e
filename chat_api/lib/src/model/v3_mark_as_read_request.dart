//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_mark_as_read_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MarkAsReadRequest {
  /// Returns a new [V3MarkAsReadRequest] instance.
  V3MarkAsReadRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MarkAsReadRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + messageId.hashCode;

  factory V3MarkAsReadRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MarkAsReadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MarkAsReadRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
