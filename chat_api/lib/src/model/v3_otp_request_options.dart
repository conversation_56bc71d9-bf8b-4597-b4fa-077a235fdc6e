//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_otp_request_options.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3OtpRequestOptions {
  /// Returns a new [V3OtpRequestOptions] instance.
  V3OtpRequestOptions({
    this.nonce,
    this.timeout,
  });

  @JsonKey(
    name: r'nonce',
    required: false,
    includeIfNull: false,
  )
  final String? nonce;

  /// After expiration, the nonce will no longer be valid.
  @JsonKey(
    name: r'timeout',
    required: false,
    includeIfNull: false,
  )
  final int? timeout;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3OtpRequestOptions &&
          other.nonce == nonce &&
          other.timeout == timeout;

  @override
  int get hashCode => nonce.hashCode + timeout.hashCode;

  factory V3OtpRequestOptions.fromJson(Map<String, dynamic> json) =>
      _$V3OtpRequestOptionsFromJson(json);

  Map<String, dynamic> toJson() => _$V3OtpRequestOptionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
