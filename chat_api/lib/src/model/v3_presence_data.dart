//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_presence_state_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_presence_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PresenceData {
  /// Returns a new [V3PresenceData] instance.
  V3PresenceData({
    this.lastUpdateTime,
    this.lastUpdateInSeconds,
    this.presenceState,
    this.customStatus,
  });

  @JsonKey(
    name: r'lastUpdateTime',
    required: false,
    includeIfNull: false,
  )
  final String? lastUpdateTime;

  @JsonKey(
    name: r'lastUpdateInSeconds',
    required: false,
    includeIfNull: false,
  )
  final int? lastUpdateInSeconds;

  @Json<PERSON>ey(
    name: r'presenceState',
    required: false,
    includeIfNull: false,
  )
  final V3PresenceStateEnum? presenceState;

  @Json<PERSON>ey(
    name: r'customStatus',
    required: false,
    includeIfNull: false,
  )
  final String? customStatus;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PresenceData &&
          other.lastUpdateTime == lastUpdateTime &&
          other.lastUpdateInSeconds == lastUpdateInSeconds &&
          other.presenceState == presenceState &&
          other.customStatus == customStatus;

  @override
  int get hashCode =>
      lastUpdateTime.hashCode +
      lastUpdateInSeconds.hashCode +
      presenceState.hashCode +
      customStatus.hashCode;

  factory V3PresenceData.fromJson(Map<String, dynamic> json) =>
      _$V3PresenceDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3PresenceDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
