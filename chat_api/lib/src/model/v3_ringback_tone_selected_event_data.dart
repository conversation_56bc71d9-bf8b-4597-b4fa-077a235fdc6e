//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_selected_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneSelectedEventData {
  /// Returns a new [V3RingbackToneSelectedEventData] instance.
  V3RingbackToneSelectedEventData({
    this.ringbackToneId,
    this.updateTime,
  });

  @Json<PERSON>ey(
    name: r'ringbackToneId',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneId;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneSelectedEventData &&
          other.ringbackToneId == ringbackToneId &&
          other.updateTime == updateTime;

  @override
  int get hashCode => ringbackToneId.hashCode + updateTime.hashCode;

  factory V3RingbackToneSelectedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneSelectedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3RingbackToneSelectedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
