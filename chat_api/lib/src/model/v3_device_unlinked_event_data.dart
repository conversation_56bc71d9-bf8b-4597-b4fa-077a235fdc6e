//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_device_unlinked_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeviceUnlinkedEventData {
  /// Returns a new [V3DeviceUnlinkedEventData] instance.
  V3DeviceUnlinkedEventData({
    this.userId,
    this.deviceId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeviceUnlinkedEventData &&
          other.userId == userId &&
          other.deviceId == deviceId;

  @override
  int get hashCode => userId.hashCode + deviceId.hashCode;

  factory V3DeviceUnlinkedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3DeviceUnlinkedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3DeviceUnlinkedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
