//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'common_public_key_cred_param.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonPublicKeyCredParam {
  /// Returns a new [CommonPublicKeyCredParam] instance.
  CommonPublicKeyCredParam({
    this.type,
    this.alg,
  });

  /// The type of public key cryptography parameter.
  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final String? type;

  /// The algorithm associated with the public key cryptography parameter.
  @JsonKey(
    name: r'alg',
    required: false,
    includeIfNull: false,
  )
  final int? alg;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonPublicKeyCredParam &&
          other.type == type &&
          other.alg == alg;

  @override
  int get hashCode => type.hashCode + alg.hashCode;

  factory CommonPublicKeyCredParam.fromJson(Map<String, dynamic> json) =>
      _$CommonPublicKeyCredParamFromJson(json);

  Map<String, dynamic> toJson() => _$CommonPublicKeyCredParamToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
