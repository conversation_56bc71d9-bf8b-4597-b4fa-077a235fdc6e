//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneData {
  /// Returns a new [V3RingbackToneData] instance.
  V3RingbackToneData({
    this.ringbackToneId,
    this.ringbackTonePath,
    this.name,
    this.isDefault,
    this.isActive,
    this.createTime,
    this.updateTime,
  });

  @JsonKey(
    name: r'ringbackToneId',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneId;

  @JsonKey(
    name: r'ringbackTonePath',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackTonePath;

  @<PERSON>son<PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'isDefault',
    required: false,
    includeIfNull: false,
  )
  final bool? isDefault;

  @<PERSON>son<PERSON><PERSON>(
    name: r'isActive',
    required: false,
    includeIfNull: false,
  )
  final bool? isActive;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneData &&
          other.ringbackToneId == ringbackToneId &&
          other.ringbackTonePath == ringbackTonePath &&
          other.name == name &&
          other.isDefault == isDefault &&
          other.isActive == isActive &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      ringbackToneId.hashCode +
      ringbackTonePath.hashCode +
      name.hashCode +
      isDefault.hashCode +
      isActive.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3RingbackToneData.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3RingbackToneDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
