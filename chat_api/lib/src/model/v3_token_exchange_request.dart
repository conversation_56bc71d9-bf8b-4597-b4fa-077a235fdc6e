//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_token_type.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_token_exchange_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TokenExchangeRequest {
  /// Returns a new [V3TokenExchangeRequest] instance.
  V3TokenExchangeRequest({
    this.tokenType,
  });

  @JsonKey(
    name: r'tokenType',
    required: false,
    includeIfNull: false,
  )
  final V3TokenType? tokenType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TokenExchangeRequest && other.tokenType == tokenType;

  @override
  int get hashCode => tokenType.hashCode;

  factory V3TokenExchangeRequest.fromJson(Map<String, dynamic> json) =>
      _$V3TokenExchangeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3TokenExchangeRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
