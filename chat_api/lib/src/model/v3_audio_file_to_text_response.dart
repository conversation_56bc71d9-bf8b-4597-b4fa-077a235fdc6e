//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_data_text_response.dart';
import 'package:chat_api/src/model/v3_data_buffer_response.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_audio_file_to_text_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AudioFileToTextResponse {
  /// Returns a new [V3AudioFileToTextResponse] instance.
  V3AudioFileToTextResponse({
    this.ok,
    this.error,
    this.dataText,
    this.dataBuffer,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'dataText',
    required: false,
    includeIfNull: false,
  )
  final V3DataTextResponse? dataText;

  @JsonKey(
    name: r'dataBuffer',
    required: false,
    includeIfNull: false,
  )
  final V3DataBufferResponse? dataBuffer;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AudioFileToTextResponse &&
          other.ok == ok &&
          other.error == error &&
          other.dataText == dataText &&
          other.dataBuffer == dataBuffer;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + dataText.hashCode + dataBuffer.hashCode;

  factory V3AudioFileToTextResponse.fromJson(Map<String, dynamic> json) =>
      _$V3AudioFileToTextResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3AudioFileToTextResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
