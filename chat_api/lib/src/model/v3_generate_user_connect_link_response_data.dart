//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_generate_user_connect_link_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GenerateUserConnectLinkResponseData {
  /// Returns a new [V3GenerateUserConnectLinkResponseData] instance.
  V3GenerateUserConnectLinkResponseData({
    this.link,
  });

  @JsonKey(
    name: r'link',
    required: false,
    includeIfNull: false,
  )
  final String? link;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GenerateUserConnectLinkResponseData && other.link == link;

  @override
  int get hashCode => link.hashCode;

  factory V3GenerateUserConnectLinkResponseData.fromJson(
          Map<String, dynamic> json) =>
      _$V3GenerateUserConnectLinkResponseDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3GenerateUserConnectLinkResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
