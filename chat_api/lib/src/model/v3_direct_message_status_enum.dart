//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - DIRECT_MESSAGE_STATUS_ENUM_PENDING: PENDING: The recipient has not replied or accept the message request  - DIRECT_MESSAGE_STATUS_ENUM_CONTACTED: CONTACTED: The recipient has accepted the message request or they were friends
enum V3DirectMessageStatusEnum {
  /// - DIRECT_MESSAGE_STATUS_ENUM_PENDING: PENDING: The recipient has not replied or accept the message request  - DIRECT_MESSAGE_STATUS_ENUM_CONTACTED: CONTACTED: The recipient has accepted the message request or they were friends
  @JsonValue(0)
  DIRECT_MESSAGE_STATUS_ENUM_PENDING('0'),

  /// - DIRECT_MESSAGE_STATUS_ENUM_PENDING: PENDING: The recipient has not replied or accept the message request  - DIRECT_MESSAGE_STATUS_ENUM_CONTACTED: CONTACTED: The recipient has accepted the message request or they were friends
  @JsonValue(1)
  DIRECT_MESSAGE_STATUS_ENUM_CONTACTED('1');

  const V3DirectMessageStatusEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
