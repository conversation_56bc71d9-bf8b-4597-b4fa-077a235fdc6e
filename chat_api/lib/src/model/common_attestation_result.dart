//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_attestation_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'common_attestation_result.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonAttestationResult {
  /// Returns a new [CommonAttestationResult] instance.
  CommonAttestationResult({
    this.id,
    this.rawId,
    this.transports,
    this.response,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  /// The raw identifier associated with the attestation result.
  @JsonKey(
    name: r'rawId',
    required: false,
    includeIfNull: false,
  )
  final String? rawId;

  /// The transports used during the attestation process.
  @JsonKey(
    name: r'transports',
    required: false,
    includeIfNull: false,
  )
  final List<String>? transports;

  @Json<PERSON>ey(
    name: r'response',
    required: false,
    includeIfNull: false,
  )
  final CommonAttestationResponse? response;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonAttestationResult &&
          other.id == id &&
          other.rawId == rawId &&
          other.transports == transports &&
          other.response == response;

  @override
  int get hashCode =>
      id.hashCode + rawId.hashCode + transports.hashCode + response.hashCode;

  factory CommonAttestationResult.fromJson(Map<String, dynamic> json) =>
      _$CommonAttestationResultFromJson(json);

  Map<String, dynamic> toJson() => _$CommonAttestationResultToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
