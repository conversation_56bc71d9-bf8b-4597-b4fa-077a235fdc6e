//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_unblock_user_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UnblockUserRequest {
  /// Returns a new [V3UnblockUserRequest] instance.
  V3UnblockUserRequest({
    this.targetUserId,
  });

  @JsonKey(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UnblockUserRequest && other.targetUserId == targetUserId;

  @override
  int get hashCode => targetUserId.hashCode;

  factory V3UnblockUserRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UnblockUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UnblockUserRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
