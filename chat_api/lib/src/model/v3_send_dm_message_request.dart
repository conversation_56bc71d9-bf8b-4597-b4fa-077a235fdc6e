//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_dm_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendDMMessageRequest {
  /// Returns a new [V3SendDMMessageRequest] instance.
  V3SendDMMessageRequest({
    this.userId,
    this.content,
    this.ref,
    this.contentLocale,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON>ey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendDMMessageRequest &&
          other.userId == userId &&
          other.content == content &&
          other.ref == ref &&
          other.contentLocale == contentLocale;

  @override
  int get hashCode =>
      userId.hashCode +
      content.hashCode +
      ref.hashCode +
      contentLocale.hashCode;

  factory V3SendDMMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendDMMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendDMMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
