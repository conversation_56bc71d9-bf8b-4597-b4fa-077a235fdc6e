//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_verify_migrate_passkey_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3VerifyMigratePasskeyResponse {
  /// Returns a new [V3VerifyMigratePasskeyResponse] instance.
  V3VerifyMigratePasskeyResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final bool? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3VerifyMigratePasskeyResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3VerifyMigratePasskeyResponse.fromJson(Map<String, dynamic> json) =>
      _$V3VerifyMigratePasskeyResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3VerifyMigratePasskeyResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
