//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/sharedv3_channel_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_in_coming_message_requests_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListInComingMessageRequestsResponse {
  /// Returns a new [V3ListInComingMessageRequestsResponse] instance.
  V3ListInComingMessageRequestsResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
    this.includes,
  });

  @Json<PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  /// List of values for the Channel interface.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<Sharedv3ChannelData>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListInComingMessageRequestsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode +
      error.hashCode +
      data.hashCode +
      paging.hashCode +
      includes.hashCode;

  factory V3ListInComingMessageRequestsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListInComingMessageRequestsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListInComingMessageRequestsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
