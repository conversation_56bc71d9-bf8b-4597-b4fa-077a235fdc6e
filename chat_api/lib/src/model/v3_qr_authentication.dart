//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_qr_auth_code_request_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_qr_authentication.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3QRAuthentication {
  /// Returns a new [V3QRAuthentication] instance.
  V3QRAuthentication({
    this.reqId,
    this.qrAuthCodeRequestOptions,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'qrAuthCodeRequestOptions',
    required: false,
    includeIfNull: false,
  )
  final V3QRAuthCodeRequestOptions? qrAuthCodeRequestOptions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3QRAuthentication &&
          other.reqId == reqId &&
          other.qrAuthCodeRequestOptions == qrAuthCodeRequestOptions;

  @override
  int get hashCode => reqId.hashCode + qrAuthCodeRequestOptions.hashCode;

  factory V3QRAuthentication.fromJson(Map<String, dynamic> json) =>
      _$V3QRAuthenticationFromJson(json);

  Map<String, dynamic> toJson() => _$V3QRAuthenticationToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
