//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_renew_qr_auth_code_options.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RenewQrAuthCodeOptions {
  /// Returns a new [V3RenewQrAuthCodeOptions] instance.
  V3RenewQrAuthCodeOptions({
    this.qrAuthCode,
    this.timeout,
  });

  @Json<PERSON>ey(
    name: r'qrAuthCode',
    required: false,
    includeIfNull: false,
  )
  final String? qrAuthCode;

  @JsonKey(
    name: r'timeout',
    required: false,
    includeIfNull: false,
  )
  final int? timeout;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RenewQrAuthCodeOptions &&
          other.qrAuthCode == qrAuthCode &&
          other.timeout == timeout;

  @override
  int get hashCode => qrAuthCode.hashCode + timeout.hashCode;

  factory V3RenewQrAuthCodeOptions.fromJson(Map<String, dynamic> json) =>
      _$V3RenewQrAuthCodeOptionsFromJson(json);

  Map<String, dynamic> toJson() => _$V3RenewQrAuthCodeOptionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
