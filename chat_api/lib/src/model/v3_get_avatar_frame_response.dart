//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_avatar_frame_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_avatar_frame_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetAvatarFrameResponse {
  /// Returns a new [V3GetAvatarFrameResponse] instance.
  V3GetAvatarFrameResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3AvatarFrameData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetAvatarFrameResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3GetAvatarFrameResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetAvatarFrameResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetAvatarFrameResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
