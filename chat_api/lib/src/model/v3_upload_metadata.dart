//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_upload_metadata.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UploadMetadata {
  /// Returns a new [V3UploadMetadata] instance.
  V3UploadMetadata({
    this.preview,
    this.checksumMd5,
  });

  /// Used to save the preview info of the file being uploaded.
  @JsonKey(
    name: r'preview',
    required: false,
    includeIfNull: false,
  )
  final String? preview;

  @JsonKey(
    name: r'checksumMd5',
    required: false,
    includeIfNull: false,
  )
  final String? checksumMd5;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UploadMetadata &&
          other.preview == preview &&
          other.checksumMd5 == checksumMd5;

  @override
  int get hashCode => preview.hashCode + checksumMd5.hashCode;

  factory V3UploadMetadata.fromJson(Map<String, dynamic> json) =>
      _$V3UploadMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UploadMetadataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
