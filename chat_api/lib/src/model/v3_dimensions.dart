//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_dimensions.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Dimensions {
  /// Returns a new [V3Dimensions] instance.
  V3Dimensions({
    this.height,
    this.width,
  });

  /// The height of file.
  @JsonKey(
    name: r'height',
    required: false,
    includeIfNull: false,
  )
  final int? height;

  /// The width of file.
  @JsonKey(
    name: r'width',
    required: false,
    includeIfNull: false,
  )
  final int? width;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Dimensions && other.height == height && other.width == width;

  @override
  int get hashCode => height.hashCode + width.hashCode;

  factory V3Dimensions.fromJson(Map<String, dynamic> json) =>
      _$V3DimensionsFromJson(json);

  Map<String, dynamic> toJson() => _$V3DimensionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
