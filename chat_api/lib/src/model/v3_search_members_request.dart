//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_members_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchMembersRequest {
  /// Returns a new [V3SearchMembersRequest] instance.
  V3SearchMembersRequest({
    this.workspaceId,
    this.channelId,
    this.keyword,
    this.limit,
    this.nextPageToken,
    this.prevPageToken,
  });

  @Json<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'keyword',
    required: false,
    includeIfNull: false,
  )
  final String? keyword;

  /// The maximum number of search results to retrieve.
  @J<PERSON><PERSON>ey(
    name: r'limit',
    required: false,
    includeIfNull: false,
  )
  final int? limit;

  /// A token to retrieve the next page of search results.
  @Json<PERSON>ey(
    name: r'nextPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? nextPageToken;

  @JsonKey(
    name: r'prevPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? prevPageToken;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchMembersRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.keyword == keyword &&
          other.limit == limit &&
          other.nextPageToken == nextPageToken &&
          other.prevPageToken == prevPageToken;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      keyword.hashCode +
      limit.hashCode +
      nextPageToken.hashCode +
      prevPageToken.hashCode;

  factory V3SearchMembersRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SearchMembersRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchMembersRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
