//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'premium_settings_boosted.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class PremiumSettingsBoosted {
  /// Returns a new [PremiumSettingsBoosted] instance.
  PremiumSettingsBoosted({
    this.enable,
  });

  @JsonKey(
    name: r'enable',
    required: false,
    includeIfNull: false,
  )
  final bool? enable;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PremiumSettingsBoosted && other.enable == enable;

  @override
  int get hashCode => enable.hashCode;

  factory PremiumSettingsBoosted.fromJson(Map<String, dynamic> json) =>
      _$PremiumSettingsBoostedFromJson(json);

  Map<String, dynamic> toJson() => _$PremiumSettingsBoostedToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
