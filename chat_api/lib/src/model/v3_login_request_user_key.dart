//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_public_key_credential_request_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_login_request_user_key.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LoginRequestUserKey {
  /// Returns a new [V3LoginRequestUserKey] instance.
  V3LoginRequestUserKey({
    this.reqId,
    this.credentialRequestOptions,
    this.passkeyMigrated,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'credentialRequestOptions',
    required: false,
    includeIfNull: false,
  )
  final CommonPublicKeyCredentialRequestOptions? credentialRequestOptions;

  @J<PERSON><PERSON><PERSON>(
    name: r'passkeyMigrated',
    required: false,
    includeIfNull: false,
  )
  final bool? passkeyMigrated;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LoginRequestUserKey &&
          other.reqId == reqId &&
          other.credentialRequestOptions == credentialRequestOptions &&
          other.passkeyMigrated == passkeyMigrated;

  @override
  int get hashCode =>
      reqId.hashCode +
      credentialRequestOptions.hashCode +
      passkeyMigrated.hashCode;

  factory V3LoginRequestUserKey.fromJson(Map<String, dynamic> json) =>
      _$V3LoginRequestUserKeyFromJson(json);

  Map<String, dynamic> toJson() => _$V3LoginRequestUserKeyToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
