//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_avatar_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserAvatarRequest {
  /// Returns a new [V3UpdateUserAvatarRequest] instance.
  V3UpdateUserAvatarRequest({
    this.avatarPath,
  });

  @JsonKey(
    name: r'avatarPath',
    required: false,
    includeIfNull: false,
  )
  final String? avatarPath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserAvatarRequest && other.avatarPath == avatarPath;

  @override
  int get hashCode => avatarPath.hashCode;

  factory V3UpdateUserAvatarRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserAvatarRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserAvatarRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
