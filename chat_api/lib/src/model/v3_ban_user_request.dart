//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ban_user_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3BanUserRequest {
  /// Returns a new [V3BanUserRequest] instance.
  V3BanUserRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3BanUserRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3BanUserRequest.fromJson(Map<String, dynamic> json) =>
      _$V3BanUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3BanUserRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
