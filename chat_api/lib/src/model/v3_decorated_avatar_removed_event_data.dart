//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_decorated_avatar_removed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DecoratedAvatarRemovedEventData {
  /// Returns a new [V3DecoratedAvatarRemovedEventData] instance.
  V3DecoratedAvatarRemovedEventData({
    this.actorId,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DecoratedAvatarRemovedEventData && other.actorId == actorId;

  @override
  int get hashCode => actorId.hashCode;

  factory V3DecoratedAvatarRemovedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3DecoratedAvatarRemovedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3DecoratedAvatarRemovedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
