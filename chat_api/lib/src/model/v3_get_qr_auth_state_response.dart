//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_login_request_state.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_qr_auth_state_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetQRAuthStateResponse {
  /// Returns a new [V3GetQRAuthStateResponse] instance.
  V3GetQRAuthStateResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3LoginRequestState? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetQRAuthStateResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3GetQRAuthStateResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetQRAuthStateResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetQRAuthStateResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
