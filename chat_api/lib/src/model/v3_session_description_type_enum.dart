//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

///  - SESSION_DESCRIPTION_TYPE_UNSPECIFIED: The session description unspecified  - SESSION_DESCRIPTION_TYPE_ANSWER: This session description describes the agreed-upon configuration, and is being sent to finalize negotiation.  - SESSION_DESCRIPTION_TYPE_OFFER: The session description object describes the initial proposal in an offer/answer exchange. The session negotiation process begins with an offer being sent from the caller to the callee.  - SESSION_DESCRIPTION_TYPE_PRANSWER: The session description object describes a provisional answer; that is, a response to a previous offer that is not the final answer. It is usually employed by legacy hardware.  - SESSION_DESCRIPTION_TYPE_ROLLBACK: This special type with an empty session description is used to roll back to the previous stable state.
enum V3SessionDescriptionTypeEnum {
  ///  - SESSION_DESCRIPTION_TYPE_UNSPECIFIED: The session description unspecified  - SESSION_DESCRIPTION_TYPE_ANSWER: This session description describes the agreed-upon configuration, and is being sent to finalize negotiation.  - SESSION_DESCRIPTION_TYPE_OFFER: The session description object describes the initial proposal in an offer/answer exchange. The session negotiation process begins with an offer being sent from the caller to the callee.  - SESSION_DESCRIPTION_TYPE_PRANSWER: The session description object describes a provisional answer; that is, a response to a previous offer that is not the final answer. It is usually employed by legacy hardware.  - SESSION_DESCRIPTION_TYPE_ROLLBACK: This special type with an empty session description is used to roll back to the previous stable state.
  @JsonValue(0)
  SESSION_DESCRIPTION_TYPE_UNSPECIFIED('0'),

  ///  - SESSION_DESCRIPTION_TYPE_UNSPECIFIED: The session description unspecified  - SESSION_DESCRIPTION_TYPE_ANSWER: This session description describes the agreed-upon configuration, and is being sent to finalize negotiation.  - SESSION_DESCRIPTION_TYPE_OFFER: The session description object describes the initial proposal in an offer/answer exchange. The session negotiation process begins with an offer being sent from the caller to the callee.  - SESSION_DESCRIPTION_TYPE_PRANSWER: The session description object describes a provisional answer; that is, a response to a previous offer that is not the final answer. It is usually employed by legacy hardware.  - SESSION_DESCRIPTION_TYPE_ROLLBACK: This special type with an empty session description is used to roll back to the previous stable state.
  @JsonValue(1)
  SESSION_DESCRIPTION_TYPE_ANSWER('1'),

  ///  - SESSION_DESCRIPTION_TYPE_UNSPECIFIED: The session description unspecified  - SESSION_DESCRIPTION_TYPE_ANSWER: This session description describes the agreed-upon configuration, and is being sent to finalize negotiation.  - SESSION_DESCRIPTION_TYPE_OFFER: The session description object describes the initial proposal in an offer/answer exchange. The session negotiation process begins with an offer being sent from the caller to the callee.  - SESSION_DESCRIPTION_TYPE_PRANSWER: The session description object describes a provisional answer; that is, a response to a previous offer that is not the final answer. It is usually employed by legacy hardware.  - SESSION_DESCRIPTION_TYPE_ROLLBACK: This special type with an empty session description is used to roll back to the previous stable state.
  @JsonValue(2)
  SESSION_DESCRIPTION_TYPE_OFFER('2'),

  ///  - SESSION_DESCRIPTION_TYPE_UNSPECIFIED: The session description unspecified  - SESSION_DESCRIPTION_TYPE_ANSWER: This session description describes the agreed-upon configuration, and is being sent to finalize negotiation.  - SESSION_DESCRIPTION_TYPE_OFFER: The session description object describes the initial proposal in an offer/answer exchange. The session negotiation process begins with an offer being sent from the caller to the callee.  - SESSION_DESCRIPTION_TYPE_PRANSWER: The session description object describes a provisional answer; that is, a response to a previous offer that is not the final answer. It is usually employed by legacy hardware.  - SESSION_DESCRIPTION_TYPE_ROLLBACK: This special type with an empty session description is used to roll back to the previous stable state.
  @JsonValue(3)
  SESSION_DESCRIPTION_TYPE_PRANSWER('3'),

  ///  - SESSION_DESCRIPTION_TYPE_UNSPECIFIED: The session description unspecified  - SESSION_DESCRIPTION_TYPE_ANSWER: This session description describes the agreed-upon configuration, and is being sent to finalize negotiation.  - SESSION_DESCRIPTION_TYPE_OFFER: The session description object describes the initial proposal in an offer/answer exchange. The session negotiation process begins with an offer being sent from the caller to the callee.  - SESSION_DESCRIPTION_TYPE_PRANSWER: The session description object describes a provisional answer; that is, a response to a previous offer that is not the final answer. It is usually employed by legacy hardware.  - SESSION_DESCRIPTION_TYPE_ROLLBACK: This special type with an empty session description is used to roll back to the previous stable state.
  @JsonValue(4)
  SESSION_DESCRIPTION_TYPE_ROLLBACK('4');

  const V3SessionDescriptionTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
