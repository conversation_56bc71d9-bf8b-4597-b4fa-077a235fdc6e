//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_role_revoked_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberRoleRevokedEventData {
  /// Returns a new [V3MemberRoleRevokedEventData] instance.
  V3MemberRoleRevokedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.targetUserId,
    this.role,
  });

  @Json<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'role',
    required: false,
    includeIfNull: false,
  )
  final String? role;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberRoleRevokedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.targetUserId == targetUserId &&
          other.role == role;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      targetUserId.hashCode +
      role.hashCode;

  factory V3MemberRoleRevokedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MemberRoleRevokedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberRoleRevokedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
