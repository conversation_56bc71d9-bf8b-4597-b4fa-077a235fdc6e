//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_presence_data.dart';
import 'package:chat_api/src/model/v3_profile.dart';
import 'package:chat_api/src/model/v3_user_type_enum.dart';
import 'package:chat_api/src/model/v3_user_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3User {
  /// Returns a new [V3User] instance.
  V3User({
    this.userId,
    this.username,
    this.createTime,
    this.updateTime,
    this.profile,
    this.userType,
    this.presenceData,
    this.statusData,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @J<PERSON><PERSON><PERSON>(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'profile',
    required: false,
    includeIfNull: false,
  )
  final V3Profile? profile;

  @JsonKey(
    name: r'userType',
    required: false,
    includeIfNull: false,
  )
  final V3UserTypeEnum? userType;

  @JsonKey(
    name: r'presenceData',
    required: false,
    includeIfNull: false,
  )
  final V3PresenceData? presenceData;

  @JsonKey(
    name: r'statusData',
    required: false,
    includeIfNull: false,
  )
  final V3UserStatus? statusData;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3User &&
          other.userId == userId &&
          other.username == username &&
          other.createTime == createTime &&
          other.updateTime == updateTime &&
          other.profile == profile &&
          other.userType == userType &&
          other.presenceData == presenceData &&
          other.statusData == statusData;

  @override
  int get hashCode =>
      userId.hashCode +
      username.hashCode +
      createTime.hashCode +
      updateTime.hashCode +
      profile.hashCode +
      userType.hashCode +
      presenceData.hashCode +
      statusData.hashCode;

  factory V3User.fromJson(Map<String, dynamic> json) => _$V3UserFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
