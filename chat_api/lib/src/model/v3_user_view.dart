//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_presence_data.dart';
import 'package:chat_api/src/model/v3_friend.dart';
import 'package:chat_api/src/model/v3_profile.dart';
import 'package:chat_api/src/model/v3_media_permission_setting_enum.dart';
import 'package:chat_api/src/model/v3_user_type_enum.dart';
import 'package:chat_api/src/model/v3_user_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_view.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserView {
  /// Returns a new [V3UserView] instance.
  V3UserView({
    this.userId,
    this.username,
    this.friendData,
    this.mediaPermissionSetting,
    this.createTime,
    this.updateTime,
    this.profile,
    this.userType,
    this.presenceData,
    this.statusData,
    this.blocked,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @JsonKey(
    name: r'friendData',
    required: false,
    includeIfNull: false,
  )
  final V3Friend? friendData;

  @JsonKey(
    name: r'mediaPermissionSetting',
    required: false,
    includeIfNull: false,
  )
  final V3MediaPermissionSettingEnum? mediaPermissionSetting;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'profile',
    required: false,
    includeIfNull: false,
  )
  final V3Profile? profile;

  @JsonKey(
    name: r'userType',
    required: false,
    includeIfNull: false,
  )
  final V3UserTypeEnum? userType;

  @JsonKey(
    name: r'presenceData',
    required: false,
    includeIfNull: false,
  )
  final V3PresenceData? presenceData;

  @JsonKey(
    name: r'statusData',
    required: false,
    includeIfNull: false,
  )
  final V3UserStatus? statusData;

  @JsonKey(
    name: r'blocked',
    required: false,
    includeIfNull: false,
  )
  final bool? blocked;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserView &&
          other.userId == userId &&
          other.username == username &&
          other.friendData == friendData &&
          other.mediaPermissionSetting == mediaPermissionSetting &&
          other.createTime == createTime &&
          other.updateTime == updateTime &&
          other.profile == profile &&
          other.userType == userType &&
          other.presenceData == presenceData &&
          other.statusData == statusData &&
          other.blocked == blocked;

  @override
  int get hashCode =>
      userId.hashCode +
      username.hashCode +
      friendData.hashCode +
      mediaPermissionSetting.hashCode +
      createTime.hashCode +
      updateTime.hashCode +
      profile.hashCode +
      userType.hashCode +
      presenceData.hashCode +
      statusData.hashCode +
      blocked.hashCode;

  factory V3UserView.fromJson(Map<String, dynamic> json) =>
      _$V3UserViewFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserViewToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
