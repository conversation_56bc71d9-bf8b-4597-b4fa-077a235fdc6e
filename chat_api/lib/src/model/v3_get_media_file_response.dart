//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_media_file_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetMediaFileResponse {
  /// Returns a new [V3GetMediaFileResponse] instance.
  V3GetMediaFileResponse({
    this.ok,
    this.error,
    this.data,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? data;

  @Json<PERSON><PERSON>(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetMediaFileResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + includes.hashCode;

  factory V3GetMediaFileResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetMediaFileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetMediaFileResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
