//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_friend_status_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_friend.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Friend {
  /// Returns a new [V3Friend] instance.
  V3Friend({
    this.requestedFromUserId,
    this.requestedToUserId,
    this.status,
    this.friendId,
    this.participantIds,
    this.readTime,
    this.acceptTime,
    this.createTime,
    this.updateTime,
    this.deleteTime,
  });

  @Json<PERSON><PERSON>(
    name: r'requestedFromUserId',
    required: false,
    includeIfNull: false,
  )
  final String? requestedFromUserId;

  @JsonKey(
    name: r'requestedToUserId',
    required: false,
    includeIfNull: false,
  )
  final String? requestedToUserId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final V3FriendStatusEnum? status;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'friendId',
    required: false,
    includeIfNull: false,
  )
  final String? friendId;

  @JsonKey(
    name: r'participantIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? participantIds;

  /// The time has been read by the receiver.
  @JsonKey(
    name: r'readTime',
    required: false,
    includeIfNull: false,
  )
  final String? readTime;

  @JsonKey(
    name: r'acceptTime',
    required: false,
    includeIfNull: false,
  )
  final String? acceptTime;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'deleteTime',
    required: false,
    includeIfNull: false,
  )
  final String? deleteTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Friend &&
          other.requestedFromUserId == requestedFromUserId &&
          other.requestedToUserId == requestedToUserId &&
          other.status == status &&
          other.friendId == friendId &&
          other.participantIds == participantIds &&
          other.readTime == readTime &&
          other.acceptTime == acceptTime &&
          other.createTime == createTime &&
          other.updateTime == updateTime &&
          other.deleteTime == deleteTime;

  @override
  int get hashCode =>
      requestedFromUserId.hashCode +
      requestedToUserId.hashCode +
      status.hashCode +
      friendId.hashCode +
      participantIds.hashCode +
      readTime.hashCode +
      acceptTime.hashCode +
      createTime.hashCode +
      updateTime.hashCode +
      deleteTime.hashCode;

  factory V3Friend.fromJson(Map<String, dynamic> json) =>
      _$V3FriendFromJson(json);

  Map<String, dynamic> toJson() => _$V3FriendToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
