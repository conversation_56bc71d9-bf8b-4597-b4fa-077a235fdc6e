//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_renew_qr_auth_code_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_renew_qr_auth_code_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RenewQrAuthCodeResponse {
  /// Returns a new [V3RenewQrAuthCodeResponse] instance.
  V3RenewQrAuthCodeResponse({
    this.ok,
    this.error,
    this.renewQrAuthCodeOptions,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'renewQrAuthCodeOptions',
    required: false,
    includeIfNull: false,
  )
  final V3RenewQrAuthCodeOptions? renewQrAuthCodeOptions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RenewQrAuthCodeResponse &&
          other.ok == ok &&
          other.error == error &&
          other.renewQrAuthCodeOptions == renewQrAuthCodeOptions;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + renewQrAuthCodeOptions.hashCode;

  factory V3RenewQrAuthCodeResponse.fromJson(Map<String, dynamic> json) =>
      _$V3RenewQrAuthCodeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3RenewQrAuthCodeResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
