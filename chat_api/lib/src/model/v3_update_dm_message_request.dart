//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_dm_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateDMMessageRequest {
  /// Returns a new [V3UpdateDMMessageRequest] instance.
  V3UpdateDMMessageRequest({
    this.userId,
    this.messageId,
    this.content,
    this.ref,
    this.contentLocale,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @Json<PERSON>ey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateDMMessageRequest &&
          other.userId == userId &&
          other.messageId == messageId &&
          other.content == content &&
          other.ref == ref &&
          other.contentLocale == contentLocale;

  @override
  int get hashCode =>
      userId.hashCode +
      messageId.hashCode +
      content.hashCode +
      ref.hashCode +
      contentLocale.hashCode;

  factory V3UpdateDMMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateDMMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateDMMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
