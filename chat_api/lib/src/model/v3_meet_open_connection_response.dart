//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_meet_token_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_meet_open_connection_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MeetOpenConnectionResponse {
  /// Returns a new [V3MeetOpenConnectionResponse] instance.
  V3MeetOpenConnectionResponse({
    this.ok,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3MeetTokenData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MeetOpenConnectionResponse &&
          other.ok == ok &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + data.hashCode;

  factory V3MeetOpenConnectionResponse.fromJson(Map<String, dynamic> json) =>
      _$V3MeetOpenConnectionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3MeetOpenConnectionResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
