//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3CallSignalIntentEnum {
  @JsonValue(0)
  CALL_SIGNAL_INTENT_UNSPECIFIED('0'),
  @JsonValue(1)
  CALL_SIGNAL_INTENT_CAMERA_ON('1'),
  @JsonValue(2)
  CALL_SIGNAL_INTENT_CAMERA_OFF('2'),
  @JsonValue(3)
  CALL_SIGNAL_INTENT_MIC_ON('3'),
  @JsonValue(4)
  CALL_SIGNAL_INTENT_MIC_OFF('4'),
  @JsonValue(5)
  CALL_SIGNAL_INTENT_WILL_END('5');

  const V3CallSignalIntentEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
