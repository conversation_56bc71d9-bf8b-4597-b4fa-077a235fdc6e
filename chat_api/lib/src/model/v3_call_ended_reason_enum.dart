//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3CallEndedReasonEnum {
  @JsonValue(0)
  CALL_ENDED_REASON_UNSPECIFIED('0'),
  @JsonValue(1)
  CALL_ENDED_REASON_FAILED('1'),
  @JsonValue(2)
  CALL_ENDED_REASON_REMOTE_ENDED('2'),
  @JsonValue(3)
  CALL_ENDED_REASON_UNANSWERED('3'),
  @JsonValue(4)
  CALL_ENDED_REASON_ANSWERED_ELSEWHERE('4'),
  @JsonValue(5)
  CALL_ENDED_REASON_DECLINED_ELSEWHERE('5');

  const V3CallEndedReasonEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
