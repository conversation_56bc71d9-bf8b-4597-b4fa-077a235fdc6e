//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_translation_request_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TranslationRequestData {
  /// Returns a new [V3TranslationRequestData] instance.
  V3TranslationRequestData({
    this.text,
    this.ref,
    this.targetLanguage,
    this.sourceLanguage,
  });

  @Json<PERSON>ey(
    name: r'text',
    required: false,
    includeIfNull: false,
  )
  final String? text;

  @Json<PERSON><PERSON>(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @Json<PERSON>ey(
    name: r'targetLanguage',
    required: false,
    includeIfNull: false,
  )
  final String? targetLanguage;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'sourceLanguage',
    required: false,
    includeIfNull: false,
  )
  final String? sourceLanguage;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TranslationRequestData &&
          other.text == text &&
          other.ref == ref &&
          other.targetLanguage == targetLanguage &&
          other.sourceLanguage == sourceLanguage;

  @override
  int get hashCode =>
      text.hashCode +
      ref.hashCode +
      targetLanguage.hashCode +
      sourceLanguage.hashCode;

  factory V3TranslationRequestData.fromJson(Map<String, dynamic> json) =>
      _$V3TranslationRequestDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3TranslationRequestDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
