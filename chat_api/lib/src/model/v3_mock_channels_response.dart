//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_mocked_channel.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_mock_channels_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MockChannelsResponse {
  /// Returns a new [V3MockChannelsResponse] instance.
  V3MockChannelsResponse({
    this.ok,
    this.data,
    this.error,
  });

  @Json<PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3MockedChannel>? data;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MockChannelsResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3MockChannelsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3MockChannelsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3MockChannelsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
