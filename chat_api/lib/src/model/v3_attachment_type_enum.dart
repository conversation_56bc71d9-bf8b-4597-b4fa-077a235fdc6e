//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3AttachmentTypeEnum {
  @JsonValue(0)
  ATTACHMENT_TYPE_ENUM_UNSPECIFIED('0'),
  @JsonValue(1)
  ATTACHMENT_TYPE_ENUM_PHOTO('1'),
  @JsonValue(2)
  ATTACHMENT_TYPE_ENUM_VOICE_MESSAGE('2'),
  @JsonValue(3)
  ATTACHMENT_TYPE_ENUM_VIDEO_MESSAGE('3'),
  @JsonValue(4)
  ATTACHMENT_TYPE_ENUM_AUDIO('4'),
  @JsonValue(5)
  ATTACHMENT_TYPE_ENUM_VIDEO('5'),
  @JsonValue(6)
  ATTACHMENT_TYPE_ENUM_LINKS('6'),
  @JsonValue(7)
  ATTACHMENT_TYPE_ENUM_STICKER('7'),
  @JsonValue(8)
  ATTACHMENT_TYPE_ENUM_MEDIA('8'),
  @JsonValue(9)
  ATTACHMENT_TYPE_ENUM_MENTION('9'),
  @JsonValue(10)
  ATTACHMENT_TYPE_ENUM_LOCATION('10'),
  @JsonValue(11)
  ATTACHMENT_TYPE_ENUM_FILE('11');

  const V3AttachmentTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
