//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_unsubscribe_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UnsubscribeChannelRequest {
  /// Returns a new [V3UnsubscribeChannelRequest] instance.
  V3UnsubscribeChannelRequest({
    this.workspaceId,
    this.channelId,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  /// The ID of the channel to unsubscribe from.
  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UnsubscribeChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode;

  factory V3UnsubscribeChannelRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UnsubscribeChannelRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UnsubscribeChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
