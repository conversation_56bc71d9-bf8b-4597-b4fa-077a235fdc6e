//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - PRETENDING_TO_UNSPECIFIED: The user pretends unspecified.  - PRETENDING_TO_ME: The user pretends to be me.  - PRETENDING_TO_FRIEND: The user pretends to be a friend.  - PRETENDING_TO_CELEBRITY: The user pretends to be a celebrity.
enum V3PretendingTo {
  /// - PRETENDING_TO_UNSPECIFIED: The user pretends unspecified.  - PRETENDING_TO_ME: The user pretends to be me.  - PRETENDING_TO_FRIEND: The user pretends to be a friend.  - PRETENDING_TO_CELEBRITY: The user pretends to be a celebrity.
  @JsonValue(0)
  PRETENDING_TO_UNSPECIFIED('0'),

  /// - PRETENDING_TO_UNSPECIFIED: The user pretends unspecified.  - PRETENDING_TO_ME: The user pretends to be me.  - PRETENDING_TO_FRIEND: The user pretends to be a friend.  - PRETENDING_TO_CELEBRITY: The user pretends to be a celebrity.
  @JsonValue(1)
  PRETENDING_TO_ME('1'),

  /// - PRETENDING_TO_UNSPECIFIED: The user pretends unspecified.  - PRETENDING_TO_ME: The user pretends to be me.  - PRETENDING_TO_FRIEND: The user pretends to be a friend.  - PRETENDING_TO_CELEBRITY: The user pretends to be a celebrity.
  @JsonValue(2)
  PRETENDING_TO_FRIEND('2'),

  /// - PRETENDING_TO_UNSPECIFIED: The user pretends unspecified.  - PRETENDING_TO_ME: The user pretends to be me.  - PRETENDING_TO_FRIEND: The user pretends to be a friend.  - PRETENDING_TO_CELEBRITY: The user pretends to be a celebrity.
  @JsonValue(3)
  PRETENDING_TO_CELEBRITY('3');

  const V3PretendingTo(this.value);

  final String value;

  @override
  String toString() => value;
}
