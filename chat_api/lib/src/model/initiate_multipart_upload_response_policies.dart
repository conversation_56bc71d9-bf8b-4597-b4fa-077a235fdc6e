//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'initiate_multipart_upload_response_policies.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class InitiateMultipartUploadResponsePolicies {
  /// Returns a new [InitiateMultipartUploadResponsePolicies] instance.
  InitiateMultipartUploadResponsePolicies({
    this.minChunkSize,
    this.maxChunkSize,
    this.strictObjectIntegrityCheck,
    this.mimetypes,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'minChunkSize',
    required: false,
    includeIfNull: false,
  )
  final int? minChunkSize;

  @Json<PERSON>ey(
    name: r'maxChunkSize',
    required: false,
    includeIfNull: false,
  )
  final int? maxChunkSize;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'strictObjectIntegrityCheck',
    required: false,
    includeIfNull: false,
  )
  final bool? strictObjectIntegrityCheck;

  /// The MIME type of the file. MIME types are used to identify the nature and format of a file on the internet.
  @JsonKey(
    name: r'mimetypes',
    required: false,
    includeIfNull: false,
  )
  final List<String>? mimetypes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InitiateMultipartUploadResponsePolicies &&
          other.minChunkSize == minChunkSize &&
          other.maxChunkSize == maxChunkSize &&
          other.strictObjectIntegrityCheck == strictObjectIntegrityCheck &&
          other.mimetypes == mimetypes;

  @override
  int get hashCode =>
      minChunkSize.hashCode +
      maxChunkSize.hashCode +
      strictObjectIntegrityCheck.hashCode +
      mimetypes.hashCode;

  factory InitiateMultipartUploadResponsePolicies.fromJson(
          Map<String, dynamic> json) =>
      _$InitiateMultipartUploadResponsePoliciesFromJson(json);

  Map<String, dynamic> toJson() =>
      _$InitiateMultipartUploadResponsePoliciesToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
