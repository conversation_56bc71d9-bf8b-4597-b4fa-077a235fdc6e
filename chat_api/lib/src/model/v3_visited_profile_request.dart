//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_visited_profile_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3VisitedProfileRequest {
  /// Returns a new [V3VisitedProfileRequest] instance.
  V3VisitedProfileRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3VisitedProfileRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3VisitedProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$V3VisitedProfileRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3VisitedProfileRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
