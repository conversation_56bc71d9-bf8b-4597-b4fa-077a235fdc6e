//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_decorated_avatar_uploaded_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DecoratedAvatarUploadedEventData {
  /// Returns a new [V3DecoratedAvatarUploadedEventData] instance.
  V3DecoratedAvatarUploadedEventData({
    this.actorId,
    this.avatarFrameId,
    this.decoratedAvatar,
    this.originalDecoratedAvatar,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @J<PERSON><PERSON><PERSON>(
    name: r'avatarFrameId',
    required: false,
    includeIfNull: false,
  )
  final String? avatarFrameId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @J<PERSON><PERSON><PERSON>(
    name: r'originalDecoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? originalDecoratedAvatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DecoratedAvatarUploadedEventData &&
          other.actorId == actorId &&
          other.avatarFrameId == avatarFrameId &&
          other.decoratedAvatar == decoratedAvatar &&
          other.originalDecoratedAvatar == originalDecoratedAvatar;

  @override
  int get hashCode =>
      actorId.hashCode +
      avatarFrameId.hashCode +
      decoratedAvatar.hashCode +
      originalDecoratedAvatar.hashCode;

  factory V3DecoratedAvatarUploadedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3DecoratedAvatarUploadedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3DecoratedAvatarUploadedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
