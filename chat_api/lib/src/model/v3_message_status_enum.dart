//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - MESSAGE_STATUS_ENUM_PENDING: Waiting for process from server  - MESSAGE_STATUS_ENUM_SUCCESS: Send message success  - MESSAGE_STATUS_ENUM_FAILURE: Send message failed
enum V3MessageStatusEnum {
  /// - MESSAGE_STATUS_ENUM_PENDING: Waiting for process from server  - MESSAGE_STATUS_ENUM_SUCCESS: Send message success  - MESSAGE_STATUS_ENUM_FAILURE: Send message failed
  @JsonValue(0)
  MESSAGE_STATUS_ENUM_PENDING('0'),

  /// - MESSAGE_STATUS_ENUM_PENDING: Waiting for process from server  - MESSAGE_STATUS_ENUM_SUCCESS: Send message success  - MESSAGE_STATUS_ENUM_FAILURE: Send message failed
  @JsonValue(1)
  MESSAGE_STATUS_ENUM_SUCCESS('1'),

  /// - MESSAGE_STATUS_ENUM_PENDING: Waiting for process from server  - MESSAGE_STATUS_ENUM_SUCCESS: Send message success  - MESSAGE_STATUS_ENUM_FAILURE: Send message failed
  @JsonValue(2)
  MESSAGE_STATUS_ENUM_FAILURE('2');

  const V3MessageStatusEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
