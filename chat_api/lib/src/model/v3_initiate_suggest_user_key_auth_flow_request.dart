//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_suggest_user_key_auth_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateSuggestUserKeyAuthFlowRequest {
  /// Returns a new [V3InitiateSuggestUserKeyAuthFlowRequest] instance.
  V3InitiateSuggestUserKeyAuthFlowRequest({
    this.reqChallenge,
    this.deviceId,
  });

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateSuggestUserKeyAuthFlowRequest &&
          other.reqChallenge == reqChallenge &&
          other.deviceId == deviceId;

  @override
  int get hashCode => reqChallenge.hashCode + deviceId.hashCode;

  factory V3InitiateSuggestUserKeyAuthFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateSuggestUserKeyAuthFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateSuggestUserKeyAuthFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
