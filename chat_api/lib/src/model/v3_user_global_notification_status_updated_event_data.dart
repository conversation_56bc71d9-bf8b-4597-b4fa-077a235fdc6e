//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_global_notification_status_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserGlobalNotificationStatusUpdatedEventData {
  /// Returns a new [V3UserGlobalNotificationStatusUpdatedEventData] instance.
  V3UserGlobalNotificationStatusUpdatedEventData({
    this.userId,
    this.globalNotificationStatus,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'globalNotificationStatus',
    required: false,
    includeIfNull: false,
  )
  final bool? globalNotificationStatus;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserGlobalNotificationStatusUpdatedEventData &&
          other.userId == userId &&
          other.globalNotificationStatus == globalNotificationStatus;

  @override
  int get hashCode => userId.hashCode + globalNotificationStatus.hashCode;

  factory V3UserGlobalNotificationStatusUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserGlobalNotificationStatusUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserGlobalNotificationStatusUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
