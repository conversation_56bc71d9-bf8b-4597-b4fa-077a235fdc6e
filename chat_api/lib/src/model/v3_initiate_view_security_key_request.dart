//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_view_security_key_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateViewSecurityKeyRequest {
  /// Returns a new [V3InitiateViewSecurityKeyRequest] instance.
  V3InitiateViewSecurityKeyRequest({
    this.reqChallenge,
  });

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateViewSecurityKeyRequest &&
          other.reqChallenge == reqChallenge;

  @override
  int get hashCode => reqChallenge.hashCode;

  factory V3InitiateViewSecurityKeyRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateViewSecurityKeyRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateViewSecurityKeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
