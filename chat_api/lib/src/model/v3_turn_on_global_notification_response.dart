//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_status.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_turn_on_global_notification_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TurnOnGlobalNotificationResponse {
  /// Returns a new [V3TurnOnGlobalNotificationResponse] instance.
  V3TurnOnGlobalNotificationResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3ChannelStatus>? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TurnOnGlobalNotificationResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3TurnOnGlobalNotificationResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3TurnOnGlobalNotificationResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3TurnOnGlobalNotificationResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
