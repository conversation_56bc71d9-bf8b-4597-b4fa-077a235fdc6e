//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_delete_file_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeleteFileResponse {
  /// Returns a new [V3DeleteFileResponse] instance.
  V3DeleteFileResponse({
    this.ok,
    this.error,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeleteFileResponse && other.ok == ok && other.error == error;

  @override
  int get hashCode => ok.hashCode + error.hashCode;

  factory V3DeleteFileResponse.fromJson(Map<String, dynamic> json) =>
      _$V3DeleteFileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3DeleteFileResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
