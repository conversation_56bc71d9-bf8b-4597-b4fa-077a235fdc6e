//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_friend.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_outgoing_friend_request_canceled_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3OutgoingFriendRequestCanceledEventData {
  /// Returns a new [V3OutgoingFriendRequestCanceledEventData] instance.
  V3OutgoingFriendRequestCanceledEventData({
    this.friendRequest,
    this.includes,
  });

  @JsonKey(
    name: r'friendRequest',
    required: false,
    includeIfNull: false,
  )
  final V3Friend? friendRequest;

  @Json<PERSON>ey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3OutgoingFriendRequestCanceledEventData &&
          other.friendRequest == friendRequest &&
          other.includes == includes;

  @override
  int get hashCode => friendRequest.hashCode + includes.hashCode;

  factory V3OutgoingFriendRequestCanceledEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3OutgoingFriendRequestCanceledEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3OutgoingFriendRequestCanceledEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
