//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_reaction_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ReactionData {
  /// Returns a new [V3ReactionData] instance.
  V3ReactionData({
    this.isReacted,
    this.total,
  });

  @Json<PERSON>ey(
    name: r'isReacted',
    required: false,
    includeIfNull: false,
  )
  final bool? isReacted;

  @J<PERSON><PERSON>ey(
    name: r'total',
    required: false,
    includeIfNull: false,
  )
  final int? total;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ReactionData &&
          other.isReacted == isReacted &&
          other.total == total;

  @override
  int get hashCode => isReacted.hashCode + total.hashCode;

  factory V3ReactionData.fromJson(Map<String, dynamic> json) =>
      _$V3ReactionDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3ReactionDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
