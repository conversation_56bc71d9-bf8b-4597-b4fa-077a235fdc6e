//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_destination_cloud_event.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_all_messages_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AllMessagesDeletedEventData {
  /// Returns a new [V3AllMessagesDeletedEventData] instance.
  V3AllMessagesDeletedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.destination,
  });

  @J<PERSON><PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @J<PERSON><PERSON><PERSON>(
    name: r'destination',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelDestinationCloudEvent? destination;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AllMessagesDeletedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.destination == destination;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      destination.hashCode;

  factory V3AllMessagesDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3AllMessagesDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3AllMessagesDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
