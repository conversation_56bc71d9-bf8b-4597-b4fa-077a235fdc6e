//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_confirm_account_deletion_by_security_key_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ConfirmAccountDeletionBySecurityKeyRequest {
  /// Returns a new [V3ConfirmAccountDeletionBySecurityKeyRequest] instance.
  V3ConfirmAccountDeletionBySecurityKeyRequest({
    this.accountDeletionKey,
    this.securityKey,
  });

  @JsonKey(
    name: r'accountDeletionKey',
    required: false,
    includeIfNull: false,
  )
  final String? accountDeletionKey;

  @JsonKey(
    name: r'securityKey',
    required: false,
    includeIfNull: false,
  )
  final String? securityKey;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ConfirmAccountDeletionBySecurityKeyRequest &&
          other.accountDeletionKey == accountDeletionKey &&
          other.securityKey == securityKey;

  @override
  int get hashCode => accountDeletionKey.hashCode + securityKey.hashCode;

  factory V3ConfirmAccountDeletionBySecurityKeyRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3ConfirmAccountDeletionBySecurityKeyRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ConfirmAccountDeletionBySecurityKeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
