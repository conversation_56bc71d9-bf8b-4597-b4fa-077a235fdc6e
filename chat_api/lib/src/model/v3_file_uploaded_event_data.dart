//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_attachment_file_status_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_file_uploaded_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3FileUploadedEventData {
  /// Returns a new [V3FileUploadedEventData] instance.
  V3FileUploadedEventData({
    this.actorId,
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.attachmentFileStatus,
    this.fileRef,
    this.fileName,
    this.fileSize,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @JsonKey(
    name: r'attachmentFileStatus',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentFileStatusEnum? attachmentFileStatus;

  @JsonKey(
    name: r'fileRef',
    required: false,
    includeIfNull: false,
  )
  final String? fileRef;

  @JsonKey(
    name: r'fileName',
    required: false,
    includeIfNull: false,
  )
  final String? fileName;

  @JsonKey(
    name: r'fileSize',
    required: false,
    includeIfNull: false,
  )
  final int? fileSize;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3FileUploadedEventData &&
          other.actorId == actorId &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.attachmentFileStatus == attachmentFileStatus &&
          other.fileRef == fileRef &&
          other.fileName == fileName &&
          other.fileSize == fileSize;

  @override
  int get hashCode =>
      actorId.hashCode +
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      attachmentFileStatus.hashCode +
      fileRef.hashCode +
      fileName.hashCode +
      fileSize.hashCode;

  factory V3FileUploadedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3FileUploadedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3FileUploadedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
