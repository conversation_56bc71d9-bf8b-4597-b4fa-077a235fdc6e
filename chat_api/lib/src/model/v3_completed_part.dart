//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_completed_part.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CompletedPart {
  /// Returns a new [V3CompletedPart] instance.
  V3CompletedPart({
    this.etag,
    this.partNumber,
  });

  /// The entity tag is an object hash.
  @JsonKey(
    name: r'etag',
    required: false,
    includeIfNull: false,
  )
  final String? etag;

  @JsonKey(
    name: r'partNumber',
    required: false,
    includeIfNull: false,
  )
  final int? partNumber;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CompletedPart &&
          other.etag == etag &&
          other.partNumber == partNumber;

  @override
  int get hashCode => etag.hashCode + partNumber.hashCode;

  factory V3CompletedPart.fromJson(Map<String, dynamic> json) =>
      _$V3CompletedPartFromJson(json);

  Map<String, dynamic> toJson() => _$V3CompletedPartToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
