//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_otp_request_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'login_request_smart_otp.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class LoginRequestSmartOtp {
  /// Returns a new [LoginRequestSmartOtp] instance.
  LoginRequestSmartOtp({
    this.reqId,
    this.otpRequestOptions,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'otpRequestOptions',
    required: false,
    includeIfNull: false,
  )
  final V3OtpRequestOptions? otpRequestOptions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginRequestSmartOtp &&
          other.reqId == reqId &&
          other.otpRequestOptions == otpRequestOptions;

  @override
  int get hashCode => reqId.hashCode + otpRequestOptions.hashCode;

  factory LoginRequestSmartOtp.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestSmartOtpFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestSmartOtpToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
