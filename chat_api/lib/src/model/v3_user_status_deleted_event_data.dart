//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_status_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserStatusDeletedEventData {
  /// Returns a new [V3UserStatusDeletedEventData] instance.
  V3UserStatusDeletedEventData({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserStatusDeletedEventData && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3UserStatusDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserStatusDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserStatusDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
