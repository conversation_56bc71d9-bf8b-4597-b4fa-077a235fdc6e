//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cancel_boost_dm_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CancelBoostDMRequest {
  /// Returns a new [V3CancelBoostDMRequest] instance.
  V3CancelBoostDMRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CancelBoostDMRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3CancelBoostDMRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CancelBoostDMRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CancelBoostDMRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
