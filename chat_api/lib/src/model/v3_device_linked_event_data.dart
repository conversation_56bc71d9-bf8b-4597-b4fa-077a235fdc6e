//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_device_linked_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeviceLinkedEventData {
  /// Returns a new [V3DeviceLinkedEventData] instance.
  V3DeviceLinkedEventData({
    this.userId,
    this.deviceId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeviceLinkedEventData &&
          other.userId == userId &&
          other.deviceId == deviceId;

  @override
  int get hashCode => userId.hashCode + deviceId.hashCode;

  factory V3DeviceLinkedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3DeviceLinkedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3DeviceLinkedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
