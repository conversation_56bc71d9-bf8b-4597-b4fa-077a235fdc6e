//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_sessions_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_all_active_sessions_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListAllActiveSessionsResponse {
  /// Returns a new [V3ListAllActiveSessionsResponse] instance.
  V3ListAllActiveSessionsResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3SessionsData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListAllActiveSessionsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3ListAllActiveSessionsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListAllActiveSessionsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListAllActiveSessionsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
