//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_location_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendLocationRequest {
  /// Returns a new [V3SendLocationRequest] instance.
  V3SendLocationRequest({
    this.workspaceId,
    this.channelId,
    this.content,
    this.latitude,
    this.longitude,
    this.description,
    this.ref,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'latitude',
    required: false,
    includeIfNull: false,
  )
  final String? latitude;

  @J<PERSON><PERSON><PERSON>(
    name: r'longitude',
    required: false,
    includeIfNull: false,
  )
  final String? longitude;

  @J<PERSON><PERSON><PERSON>(
    name: r'description',
    required: false,
    includeIfNull: false,
  )
  final String? description;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendLocationRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.content == content &&
          other.latitude == latitude &&
          other.longitude == longitude &&
          other.description == description &&
          other.ref == ref;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      content.hashCode +
      latitude.hashCode +
      longitude.hashCode +
      description.hashCode +
      ref.hashCode;

  factory V3SendLocationRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendLocationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendLocationRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
