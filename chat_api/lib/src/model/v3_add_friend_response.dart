//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_friend_data.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_add_friend_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AddFriendResponse {
  /// Returns a new [V3AddFriendResponse] instance.
  V3AddFriendResponse({
    this.ok,
    this.data,
    this.error,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3FriendData? data;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AddFriendResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + data.hashCode + error.hashCode + includes.hashCode;

  factory V3AddFriendResponse.fromJson(Map<String, dynamic> json) =>
      _$V3AddFriendResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3AddFriendResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
