//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_list_message_reactions_data.dart';
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_dm_message_reactions_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListDMMessageReactionsResponse {
  /// Returns a new [V3ListDMMessageReactionsResponse] instance.
  V3ListDMMessageReactionsResponse({
    this.ok,
    this.data,
    this.error,
    this.paging,
    this.includes,
  });

  @<PERSON>sonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3ListMessageReactionsData? data;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListDMMessageReactionsResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.paging == paging &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode +
      data.hashCode +
      error.hashCode +
      paging.hashCode +
      includes.hashCode;

  factory V3ListDMMessageReactionsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListDMMessageReactionsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListDMMessageReactionsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
