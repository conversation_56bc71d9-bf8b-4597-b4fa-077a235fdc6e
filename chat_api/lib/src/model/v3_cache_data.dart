//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_cache_data_embed.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_cache_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CacheData {
  /// Returns a new [V3CacheData] instance.
  V3CacheData({
    this.firstframe,
    this.totalDuration,
    this.frameCount,
    this.full,
    this.md,
    this.sm,
  });

  @JsonKey(
    name: r'firstframe',
    required: false,
    includeIfNull: false,
  )
  final String? firstframe;

  @JsonKey(
    name: r'totalDuration',
    required: false,
    includeIfNull: false,
  )
  final double? totalDuration;

  @JsonKey(
    name: r'frameCount',
    required: false,
    includeIfNull: false,
  )
  final int? frameCount;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'full',
    required: false,
    includeIfNull: false,
  )
  final V3CacheDataEmbed? full;

  @Json<PERSON>ey(
    name: r'md',
    required: false,
    includeIfNull: false,
  )
  final V3CacheDataEmbed? md;

  @JsonKey(
    name: r'sm',
    required: false,
    includeIfNull: false,
  )
  final V3CacheDataEmbed? sm;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CacheData &&
          other.firstframe == firstframe &&
          other.totalDuration == totalDuration &&
          other.frameCount == frameCount &&
          other.full == full &&
          other.md == md &&
          other.sm == sm;

  @override
  int get hashCode =>
      firstframe.hashCode +
      totalDuration.hashCode +
      frameCount.hashCode +
      full.hashCode +
      md.hashCode +
      sm.hashCode;

  factory V3CacheData.fromJson(Map<String, dynamic> json) =>
      _$V3CacheDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CacheDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
