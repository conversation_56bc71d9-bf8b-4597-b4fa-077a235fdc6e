//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_renew_qr_auth_code_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RenewQrAuthCodeRequest {
  /// Returns a new [V3RenewQrAuthCodeRequest] instance.
  V3RenewQrAuthCodeRequest({
    this.reqId,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RenewQrAuthCodeRequest && other.reqId == reqId;

  @override
  int get hashCode => reqId.hashCode;

  factory V3RenewQrAuthCodeRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RenewQrAuthCodeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RenewQrAuthCodeRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
