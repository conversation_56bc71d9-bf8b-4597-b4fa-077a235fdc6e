//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_permission_setting_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_global_media_permission_setting_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserGlobalMediaPermissionSettingUpdatedEventData {
  /// Returns a new [V3UserGlobalMediaPermissionSettingUpdatedEventData] instance.
  V3UserGlobalMediaPermissionSettingUpdatedEventData({
    this.actorId,
    this.globalMediaPermissionSetting,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @Json<PERSON>ey(
    name: r'globalMediaPermissionSetting',
    required: false,
    includeIfNull: false,
  )
  final V3MediaPermissionSettingEnum? globalMediaPermissionSetting;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserGlobalMediaPermissionSettingUpdatedEventData &&
          other.actorId == actorId &&
          other.globalMediaPermissionSetting == globalMediaPermissionSetting;

  @override
  int get hashCode => actorId.hashCode + globalMediaPermissionSetting.hashCode;

  factory V3UserGlobalMediaPermissionSettingUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserGlobalMediaPermissionSettingUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserGlobalMediaPermissionSettingUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
