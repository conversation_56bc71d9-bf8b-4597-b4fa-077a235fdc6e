//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_device_session.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeviceSession {
  /// Returns a new [V3DeviceSession] instance.
  V3DeviceSession({
    this.sessionId,
    this.isPasskey,
    this.isRoot,
    this.applicationName,
    this.ipAddress,
    this.locationName,
    this.lastActiveTime,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'sessionId',
    required: false,
    includeIfNull: false,
  )
  final String? sessionId;

  @Json<PERSON>ey(
    name: r'isPasskey',
    required: false,
    includeIfNull: false,
  )
  final bool? isPasskey;

  @JsonKey(
    name: r'isRoot',
    required: false,
    includeIfNull: false,
  )
  final bool? isRoot;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'applicationName',
    required: false,
    includeIfNull: false,
  )
  final String? applicationName;

  @J<PERSON><PERSON><PERSON>(
    name: r'ipAddress',
    required: false,
    includeIfNull: false,
  )
  final String? ipAddress;

  @JsonKey(
    name: r'locationName',
    required: false,
    includeIfNull: false,
  )
  final String? locationName;

  @JsonKey(
    name: r'lastActiveTime',
    required: false,
    includeIfNull: false,
  )
  final String? lastActiveTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeviceSession &&
          other.sessionId == sessionId &&
          other.isPasskey == isPasskey &&
          other.isRoot == isRoot &&
          other.applicationName == applicationName &&
          other.ipAddress == ipAddress &&
          other.locationName == locationName &&
          other.lastActiveTime == lastActiveTime;

  @override
  int get hashCode =>
      sessionId.hashCode +
      isPasskey.hashCode +
      isRoot.hashCode +
      applicationName.hashCode +
      ipAddress.hashCode +
      locationName.hashCode +
      lastActiveTime.hashCode;

  factory V3DeviceSession.fromJson(Map<String, dynamic> json) =>
      _$V3DeviceSessionFromJson(json);

  Map<String, dynamic> toJson() => _$V3DeviceSessionToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
