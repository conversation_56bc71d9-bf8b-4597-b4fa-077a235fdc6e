//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_dimensions.dart';
import 'package:chat_api/src/model/v3_matrix.dart';
import 'package:chat_api/src/model/v3_orientation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_layout_metadata.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LayoutMetadata {
  /// Returns a new [V3LayoutMetadata] instance.
  V3LayoutMetadata({
    this.layoutId,
    this.matrix,
    this.dimensions,
    this.orientation,
    this.isRowSpan,
    this.fileRef,
  });

  @JsonKey(
    name: r'layoutId',
    required: false,
    includeIfNull: false,
  )
  final String? layoutId;

  @<PERSON>son<PERSON><PERSON>(
    name: r'matrix',
    required: false,
    includeIfNull: false,
  )
  final V3Matrix? matrix;

  @J<PERSON><PERSON><PERSON>(
    name: r'dimensions',
    required: false,
    includeIfNull: false,
  )
  final V3Dimensions? dimensions;

  @Json<PERSON><PERSON>(
    name: r'orientation',
    required: false,
    includeIfNull: false,
  )
  final V3Orientation? orientation;

  @JsonKey(
    name: r'isRowSpan',
    required: false,
    includeIfNull: false,
  )
  final bool? isRowSpan;

  @JsonKey(
    name: r'fileRef',
    required: false,
    includeIfNull: false,
  )
  final String? fileRef;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LayoutMetadata &&
          other.layoutId == layoutId &&
          other.matrix == matrix &&
          other.dimensions == dimensions &&
          other.orientation == orientation &&
          other.isRowSpan == isRowSpan &&
          other.fileRef == fileRef;

  @override
  int get hashCode =>
      layoutId.hashCode +
      matrix.hashCode +
      dimensions.hashCode +
      orientation.hashCode +
      isRowSpan.hashCode +
      fileRef.hashCode;

  factory V3LayoutMetadata.fromJson(Map<String, dynamic> json) =>
      _$V3LayoutMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$V3LayoutMetadataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
