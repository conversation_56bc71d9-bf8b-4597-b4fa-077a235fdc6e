//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_search_friend_result.dart';
import 'package:chat_api/src/model/v3_search_user_result.dart';
import 'package:chat_api/src/model/v3_search_channel_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_share_to_incoming_result.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ShareToIncomingResult {
  /// Returns a new [V3ShareToIncomingResult] instance.
  V3ShareToIncomingResult({
    this.user,
    this.channel,
    this.friend,
  });

  @Json<PERSON>ey(
    name: r'user',
    required: false,
    includeIfNull: false,
  )
  final V3SearchUserResult? user;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3SearchChannelResult? channel;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'friend',
    required: false,
    includeIfNull: false,
  )
  final V3SearchFriendResult? friend;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ShareToIncomingResult &&
          other.user == user &&
          other.channel == channel &&
          other.friend == friend;

  @override
  int get hashCode => user.hashCode + channel.hashCode + friend.hashCode;

  factory V3ShareToIncomingResult.fromJson(Map<String, dynamic> json) =>
      _$V3ShareToIncomingResultFromJson(json);

  Map<String, dynamic> toJson() => _$V3ShareToIncomingResultToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
