//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_sync_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelSyncData {
  /// Returns a new [V3ChannelSyncData] instance.
  V3ChannelSyncData({
    this.id,
    this.version,
    this.source_,
    this.unreadCount,
    this.lastSeenMessageId,
    this.pinned,
    this.sort,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  @J<PERSON><PERSON><PERSON>(
    name: r'version',
    required: false,
    includeIfNull: false,
  )
  final int? version;

  @Json<PERSON>ey(
    name: r'source',
    required: false,
    includeIfNull: false,
  )
  final String? source_;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'unreadCount',
    required: false,
    includeIfNull: false,
  )
  final int? unreadCount;

  @J<PERSON><PERSON><PERSON>(
    name: r'lastSeenMessageId',
    required: false,
    includeIfNull: false,
  )
  final String? lastSeenMessageId;

  @JsonKey(
    name: r'pinned',
    required: false,
    includeIfNull: false,
  )
  final bool? pinned;

  @JsonKey(
    name: r'sort',
    required: false,
    includeIfNull: false,
  )
  final int? sort;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelSyncData &&
          other.id == id &&
          other.version == version &&
          other.source_ == source_ &&
          other.unreadCount == unreadCount &&
          other.lastSeenMessageId == lastSeenMessageId &&
          other.pinned == pinned &&
          other.sort == sort;

  @override
  int get hashCode =>
      id.hashCode +
      version.hashCode +
      source_.hashCode +
      unreadCount.hashCode +
      lastSeenMessageId.hashCode +
      pinned.hashCode +
      sort.hashCode;

  factory V3ChannelSyncData.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelSyncDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelSyncDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
