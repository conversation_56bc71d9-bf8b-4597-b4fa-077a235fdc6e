//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_pretending_to.dart';
import 'package:chat_api/src/model/v3_report_category.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_report_dm_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ReportDMMessageRequest {
  /// Returns a new [V3ReportDMMessageRequest] instance.
  V3ReportDMMessageRequest({
    this.userId,
    this.messageId,
    this.reportCategory,
    this.pretendingTo,
    this.reportReason,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @J<PERSON><PERSON><PERSON>(
    name: r'reportCategory',
    required: false,
    includeIfNull: false,
  )
  final V3ReportCategory? reportCategory;

  @J<PERSON><PERSON>ey(
    name: r'pretendingTo',
    required: false,
    includeIfNull: false,
  )
  final V3PretendingTo? pretendingTo;

  @JsonKey(
    name: r'reportReason',
    required: false,
    includeIfNull: false,
  )
  final String? reportReason;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ReportDMMessageRequest &&
          other.userId == userId &&
          other.messageId == messageId &&
          other.reportCategory == reportCategory &&
          other.pretendingTo == pretendingTo &&
          other.reportReason == reportReason;

  @override
  int get hashCode =>
      userId.hashCode +
      messageId.hashCode +
      reportCategory.hashCode +
      pretendingTo.hashCode +
      reportReason.hashCode;

  factory V3ReportDMMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3ReportDMMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3ReportDMMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
