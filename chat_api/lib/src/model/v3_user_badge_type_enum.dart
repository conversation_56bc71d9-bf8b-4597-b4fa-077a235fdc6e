//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3UserBadgeTypeEnum {
  @JsonValue(0)
  USER_BADGE_TYPE_DEFAULT('0'),
  @JsonValue(1)
  USER_BADGE_TYPE_BLUE('1'),
  @JsonValue(2)
  USER_BADGE_TYPE_GRAY('2'),
  @JsonValue(3)
  USER_BADGE_TYPE_YELLOW('3');

  const V3UserBadgeTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
