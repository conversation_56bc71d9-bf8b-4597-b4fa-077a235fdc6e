//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_room.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_meeting_room_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListMeetingRoomResponse {
  /// Returns a new [V3ListMeetingRoomResponse] instance.
  V3ListMeetingRoomResponse({
    this.ok,
    this.data,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3Room>? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListMeetingRoomResponse &&
          other.ok == ok &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + data.hashCode;

  factory V3ListMeetingRoomResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListMeetingRoomResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListMeetingRoomResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
