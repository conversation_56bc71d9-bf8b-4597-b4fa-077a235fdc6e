//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_gateway_connected_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GatewayConnectedEventData {
  /// Returns a new [V3GatewayConnectedEventData] instance.
  V3GatewayConnectedEventData({
    this.userId,
    this.deviceId,
    this.message,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON><PERSON>(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @Json<PERSON>ey(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final String? message;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GatewayConnectedEventData &&
          other.userId == userId &&
          other.deviceId == deviceId &&
          other.message == message;

  @override
  int get hashCode => userId.hashCode + deviceId.hashCode + message.hashCode;

  factory V3GatewayConnectedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3GatewayConnectedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3GatewayConnectedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
