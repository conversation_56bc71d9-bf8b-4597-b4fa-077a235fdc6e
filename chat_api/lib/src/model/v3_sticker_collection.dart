//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_sticker.dart';
import 'package:chat_api/src/model/v3_sticker_collection_type_enum.dart';
import 'package:chat_api/src/model/v3_cache_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sticker_collection.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3StickerCollection {
  /// Returns a new [V3StickerCollection] instance.
  V3StickerCollection({
    this.collectionId,
    this.name,
    this.avatar,
    this.description,
    this.type,
    this.stickers,
    this.cacheData,
    this.weight,
    this.updateTime,
    this.thumbnail,
  });

  @<PERSON>sonKey(
    name: r'collectionId',
    required: false,
    includeIfNull: false,
  )
  final String? collectionId;

  @J<PERSON><PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @Json<PERSON>ey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @JsonKey(
    name: r'description',
    required: false,
    includeIfNull: false,
  )
  final String? description;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final V3StickerCollectionTypeEnum? type;

  @JsonKey(
    name: r'stickers',
    required: false,
    includeIfNull: false,
  )
  final List<V3Sticker>? stickers;

  @JsonKey(
    name: r'cacheData',
    required: false,
    includeIfNull: false,
  )
  final V3CacheData? cacheData;

  @JsonKey(
    name: r'weight',
    required: false,
    includeIfNull: false,
  )
  final int? weight;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'thumbnail',
    required: false,
    includeIfNull: false,
  )
  final String? thumbnail;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3StickerCollection &&
          other.collectionId == collectionId &&
          other.name == name &&
          other.avatar == avatar &&
          other.description == description &&
          other.type == type &&
          other.stickers == stickers &&
          other.cacheData == cacheData &&
          other.weight == weight &&
          other.updateTime == updateTime &&
          other.thumbnail == thumbnail;

  @override
  int get hashCode =>
      collectionId.hashCode +
      name.hashCode +
      avatar.hashCode +
      description.hashCode +
      type.hashCode +
      stickers.hashCode +
      cacheData.hashCode +
      weight.hashCode +
      updateTime.hashCode +
      thumbnail.hashCode;

  factory V3StickerCollection.fromJson(Map<String, dynamic> json) =>
      _$V3StickerCollectionFromJson(json);

  Map<String, dynamic> toJson() => _$V3StickerCollectionToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
