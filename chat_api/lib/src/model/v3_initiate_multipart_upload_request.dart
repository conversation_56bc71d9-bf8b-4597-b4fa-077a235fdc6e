//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_upload_request.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_multipart_upload_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateMultipartUploadRequest {
  /// Returns a new [V3InitiateMultipartUploadRequest] instance.
  V3InitiateMultipartUploadRequest({
    this.uploadRequest,
    this.numberOfChunks,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'uploadRequest',
    required: false,
    includeIfNull: false,
  )
  final V3UploadRequest? uploadRequest;

  @JsonKey(
    name: r'numberOfChunks',
    required: false,
    includeIfNull: false,
  )
  final int? numberOfChunks;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateMultipartUploadRequest &&
          other.uploadRequest == uploadRequest &&
          other.numberOfChunks == numberOfChunks;

  @override
  int get hashCode => uploadRequest.hashCode + numberOfChunks.hashCode;

  factory V3InitiateMultipartUploadRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateMultipartUploadRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateMultipartUploadRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
