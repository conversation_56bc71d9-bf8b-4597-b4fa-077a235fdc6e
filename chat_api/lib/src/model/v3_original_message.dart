//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:chat_api/src/model/v3_media_attachment.dart';
import 'package:chat_api/src/model/v3_message_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_original_message.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3OriginalMessage {
  /// Returns a new [V3OriginalMessage] instance.
  V3OriginalMessage({
    this.messageId,
    this.content,
    this.attachmentType,
    this.mediaAttachments,
    this.messageType,
    this.contentLocale,
    this.contentArguments,
    this.userId,
    this.editTime,
    this.createTime,
    this.updateTime,
  });

  @<PERSON>son<PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @Json<PERSON>ey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @JsonKey(
    name: r'mediaAttachments',
    required: false,
    includeIfNull: false,
  )
  final V3MediaAttachment? mediaAttachments;

  @JsonKey(
    name: r'messageType',
    required: false,
    includeIfNull: false,
  )
  final V3MessageTypeEnum? messageType;

  @JsonKey(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @JsonKey(
    name: r'contentArguments',
    required: false,
    includeIfNull: false,
  )
  final List<String>? contentArguments;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'editTime',
    required: false,
    includeIfNull: false,
  )
  final String? editTime;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3OriginalMessage &&
          other.messageId == messageId &&
          other.content == content &&
          other.attachmentType == attachmentType &&
          other.mediaAttachments == mediaAttachments &&
          other.messageType == messageType &&
          other.contentLocale == contentLocale &&
          other.contentArguments == contentArguments &&
          other.userId == userId &&
          other.editTime == editTime &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      messageId.hashCode +
      content.hashCode +
      attachmentType.hashCode +
      mediaAttachments.hashCode +
      messageType.hashCode +
      contentLocale.hashCode +
      contentArguments.hashCode +
      userId.hashCode +
      editTime.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3OriginalMessage.fromJson(Map<String, dynamic> json) =>
      _$V3OriginalMessageFromJson(json);

  Map<String, dynamic> toJson() => _$V3OriginalMessageToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
