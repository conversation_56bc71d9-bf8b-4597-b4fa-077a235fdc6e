//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3UserStatusExpireAfterTimeEnum {
  @JsonValue(0)
  USER_STATUS_EXPIRES_AFTER_TIME_ENUM_UNSPECIFIED('0'),
  @JsonValue(1)
  USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_1_HOUR('1'),
  @JsonValue(2)
  USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_4_HOUR('2'),
  @JsonValue(3)
  USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_8_HOUR('3'),
  @JsonValue(4)
  USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_24_HOUR('4'),
  @JsonValue(99)
  USER_STATUS_EXPIRES_AFTER_TIME_ENUM_NEVER('99');

  const V3UserStatusExpireAfterTimeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
