//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cancel_registration_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CancelRegistrationRequest {
  /// Returns a new [V3CancelRegistrationRequest] instance.
  V3CancelRegistrationRequest({
    this.reqId,
    this.userKey,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'userKey',
    required: false,
    includeIfNull: false,
  )
  final String? userKey;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CancelRegistrationRequest &&
          other.reqId == reqId &&
          other.userKey == userKey;

  @override
  int get hashCode => reqId.hashCode + userKey.hashCode;

  factory V3CancelRegistrationRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CancelRegistrationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CancelRegistrationRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
