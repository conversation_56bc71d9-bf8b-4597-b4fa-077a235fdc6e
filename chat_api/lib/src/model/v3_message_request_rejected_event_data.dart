//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_message_request_rejected_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MessageRequestRejectedEventData {
  /// Returns a new [V3MessageRequestRejectedEventData] instance.
  V3MessageRequestRejectedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.targetUserId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MessageRequestRejectedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.targetUserId == targetUserId;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      targetUserId.hashCode;

  factory V3MessageRequestRejectedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3MessageRequestRejectedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3MessageRequestRejectedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
