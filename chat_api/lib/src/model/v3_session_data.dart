//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_session_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SessionData {
  /// Returns a new [V3SessionData] instance.
  V3SessionData({
    this.userId,
    this.sessionId,
    this.sessionToken,
    this.user,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON><PERSON>(
    name: r'sessionId',
    required: false,
    includeIfNull: false,
  )
  final String? sessionId;

  @JsonKey(
    name: r'sessionToken',
    required: false,
    includeIfNull: false,
  )
  final String? sessionToken;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'user',
    required: false,
    includeIfNull: false,
  )
  final V3User? user;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SessionData &&
          other.userId == userId &&
          other.sessionId == sessionId &&
          other.sessionToken == sessionToken &&
          other.user == user;

  @override
  int get hashCode =>
      userId.hashCode +
      sessionId.hashCode +
      sessionToken.hashCode +
      user.hashCode;

  factory V3SessionData.fromJson(Map<String, dynamic> json) =>
      _$V3SessionDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3SessionDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
