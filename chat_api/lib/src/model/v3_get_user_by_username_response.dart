//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_view.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_user_by_username_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetUserByUsernameResponse {
  /// Returns a new [V3GetUserByUsernameResponse] instance.
  V3GetUserByUsernameResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3UserView? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetUserByUsernameResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3GetUserByUsernameResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetUserByUsernameResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetUserByUsernameResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
