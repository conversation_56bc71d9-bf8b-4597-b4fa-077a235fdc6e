//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_login_with_qr_auth_code_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LoginWithQRAuthCodeRequest {
  /// Returns a new [V3LoginWithQRAuthCodeRequest] instance.
  V3LoginWithQRAuthCodeRequest({
    this.reqId,
    this.reqVerifier,
    this.qrAuthCode,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'qrAuthCode',
    required: false,
    includeIfNull: false,
  )
  final String? qrAuthCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LoginWithQRAuthCodeRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.qrAuthCode == qrAuthCode;

  @override
  int get hashCode =>
      reqId.hashCode + reqVerifier.hashCode + qrAuthCode.hashCode;

  factory V3LoginWithQRAuthCodeRequest.fromJson(Map<String, dynamic> json) =>
      _$V3LoginWithQRAuthCodeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3LoginWithQRAuthCodeRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
