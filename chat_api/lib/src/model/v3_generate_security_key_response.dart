//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_security_key.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_generate_security_key_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GenerateSecurityKeyResponse {
  /// Returns a new [V3GenerateSecurityKeyResponse] instance.
  V3GenerateSecurityKeyResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3SecurityKey? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GenerateSecurityKeyResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3GenerateSecurityKeyResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GenerateSecurityKeyResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GenerateSecurityKeyResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
