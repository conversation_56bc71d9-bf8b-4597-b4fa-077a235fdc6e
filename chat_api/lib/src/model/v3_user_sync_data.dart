//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_sync_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserSyncData {
  /// Returns a new [V3UserSyncData] instance.
  V3UserSyncData({
    this.id,
    this.version,
    this.source_,
    this.dmId,
    this.blocked,
    this.aliasName,
  });

  @JsonKey(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  @J<PERSON><PERSON><PERSON>(
    name: r'version',
    required: false,
    includeIfNull: false,
  )
  final int? version;

  @Json<PERSON>ey(
    name: r'source',
    required: false,
    includeIfNull: false,
  )
  final String? source_;

  @JsonKey(
    name: r'dmId',
    required: false,
    includeIfNull: false,
  )
  final String? dmId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'blocked',
    required: false,
    includeIfNull: false,
  )
  final bool? blocked;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'aliasName',
    required: false,
    includeIfNull: false,
  )
  final String? aliasName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserSyncData &&
          other.id == id &&
          other.version == version &&
          other.source_ == source_ &&
          other.dmId == dmId &&
          other.blocked == blocked &&
          other.aliasName == aliasName;

  @override
  int get hashCode =>
      id.hashCode +
      version.hashCode +
      source_.hashCode +
      dmId.hashCode +
      blocked.hashCode +
      aliasName.hashCode;

  factory V3UserSyncData.fromJson(Map<String, dynamic> json) =>
      _$V3UserSyncDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserSyncDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
