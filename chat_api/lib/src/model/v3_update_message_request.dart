//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateMessageRequest {
  /// Returns a new [V3UpdateMessageRequest] instance.
  V3UpdateMessageRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.content,
    this.ref,
    this.contentLocale,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateMessageRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.content == content &&
          other.ref == ref &&
          other.contentLocale == contentLocale;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      content.hashCode +
      ref.hashCode +
      contentLocale.hashCode;

  factory V3UpdateMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
