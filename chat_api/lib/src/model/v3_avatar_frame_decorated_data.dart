//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_avatar_frame_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_avatar_frame_decorated_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AvatarFrameDecoratedData {
  /// Returns a new [V3AvatarFrameDecoratedData] instance.
  V3AvatarFrameDecoratedData({
    this.avatarFrame,
    this.decoratedAvatar,
    this.originalDecoratedAvatar,
  });

  @JsonKey(
    name: r'avatarFrame',
    required: false,
    includeIfNull: false,
  )
  final V3AvatarFrameData? avatarFrame;

  @JsonKey(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @J<PERSON><PERSON>ey(
    name: r'originalDecoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? originalDecoratedAvatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AvatarFrameDecoratedData &&
          other.avatarFrame == avatarFrame &&
          other.decoratedAvatar == decoratedAvatar &&
          other.originalDecoratedAvatar == originalDecoratedAvatar;

  @override
  int get hashCode =>
      avatarFrame.hashCode +
      decoratedAvatar.hashCode +
      originalDecoratedAvatar.hashCode;

  factory V3AvatarFrameDecoratedData.fromJson(Map<String, dynamic> json) =>
      _$V3AvatarFrameDecoratedDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3AvatarFrameDecoratedDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
