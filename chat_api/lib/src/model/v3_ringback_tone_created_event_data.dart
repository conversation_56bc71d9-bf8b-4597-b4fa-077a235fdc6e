//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_created_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneCreatedEventData {
  /// Returns a new [V3RingbackToneCreatedEventData] instance.
  V3RingbackToneCreatedEventData({
    this.ringbackToneId,
    this.name,
    this.isDefault,
    this.isActive,
    this.createTime,
    this.updateTime,
  });

  @Json<PERSON>ey(
    name: r'ringbackToneId',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneId;

  @<PERSON>son<PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'isDefault',
    required: false,
    includeIfNull: false,
  )
  final bool? isDefault;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'isActive',
    required: false,
    includeIfNull: false,
  )
  final bool? isActive;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneCreatedEventData &&
          other.ringbackToneId == ringbackToneId &&
          other.name == name &&
          other.isDefault == isDefault &&
          other.isActive == isActive &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      ringbackToneId.hashCode +
      name.hashCode +
      isDefault.hashCode +
      isActive.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3RingbackToneCreatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneCreatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3RingbackToneCreatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
