//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_generic_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GenericData {
  /// Returns a new [V3GenericData] instance.
  V3GenericData({
    this.subscribedChannels,
    this.unsubscribedChannels,
  });

  /// The names of channels that have been subscribed to.
  @JsonKey(
    name: r'subscribedChannels',
    required: false,
    includeIfNull: false,
  )
  final List<String>? subscribedChannels;

  /// The names of channels that have been unsubscribed from.
  @JsonKey(
    name: r'unsubscribedChannels',
    required: false,
    includeIfNull: false,
  )
  final List<String>? unsubscribedChannels;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GenericData &&
          other.subscribedChannels == subscribedChannels &&
          other.unsubscribedChannels == unsubscribedChannels;

  @override
  int get hashCode =>
      subscribedChannels.hashCode + unsubscribedChannels.hashCode;

  factory V3GenericData.fromJson(Map<String, dynamic> json) =>
      _$V3GenericDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3GenericDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
