//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_dimensions.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_file_metadata.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3FileMetadata {
  /// Returns a new [V3FileMetadata] instance.
  V3FileMetadata({
    this.filename,
    this.filesize,
    this.extension_,
    this.mimetype,
    this.dimensions,
    this.duration,
  });

  /// The name of file.
  @JsonKey(
    name: r'filename',
    required: false,
    includeIfNull: false,
  )
  final String? filename;

  /// The size of the file in bytes.
  @JsonKey(
    name: r'filesize',
    required: false,
    includeIfNull: false,
  )
  final int? filesize;

  /// The file extension.
  @<PERSON><PERSON><PERSON>ey(
    name: r'extension',
    required: false,
    includeIfNull: false,
  )
  final String? extension_;

  /// The MIME type of the file. MIME types are used to identify the nature and format of a file on the internet.
  @JsonKey(
    name: r'mimetype',
    required: false,
    includeIfNull: false,
  )
  final String? mimetype;

  @JsonKey(
    name: r'dimensions',
    required: false,
    includeIfNull: false,
  )
  final V3Dimensions? dimensions;

  @JsonKey(
    name: r'duration',
    required: false,
    includeIfNull: false,
  )
  final int? duration;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3FileMetadata &&
          other.filename == filename &&
          other.filesize == filesize &&
          other.extension_ == extension_ &&
          other.mimetype == mimetype &&
          other.dimensions == dimensions &&
          other.duration == duration;

  @override
  int get hashCode =>
      filename.hashCode +
      filesize.hashCode +
      extension_.hashCode +
      mimetype.hashCode +
      dimensions.hashCode +
      duration.hashCode;

  factory V3FileMetadata.fromJson(Map<String, dynamic> json) =>
      _$V3FileMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$V3FileMetadataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
