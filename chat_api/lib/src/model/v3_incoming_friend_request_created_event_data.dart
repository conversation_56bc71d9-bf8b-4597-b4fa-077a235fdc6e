//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_friend.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_incoming_friend_request_created_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3IncomingFriendRequestCreatedEventData {
  /// Returns a new [V3IncomingFriendRequestCreatedEventData] instance.
  V3IncomingFriendRequestCreatedEventData({
    this.friendRequest,
    this.includes,
  });

  @JsonKey(
    name: r'friendRequest',
    required: false,
    includeIfNull: false,
  )
  final V3Friend? friendRequest;

  @Json<PERSON>ey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3IncomingFriendRequestCreatedEventData &&
          other.friendRequest == friendRequest &&
          other.includes == includes;

  @override
  int get hashCode => friendRequest.hashCode + includes.hashCode;

  factory V3IncomingFriendRequestCreatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3IncomingFriendRequestCreatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3IncomingFriendRequestCreatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
