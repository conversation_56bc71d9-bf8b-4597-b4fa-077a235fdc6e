//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_permission_setting_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_media_permission_setting_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateMediaPermissionSettingRequest {
  /// Returns a new [V3UpdateMediaPermissionSettingRequest] instance.
  V3UpdateMediaPermissionSettingRequest({
    this.permissionType,
  });

  @JsonKey(
    name: r'permissionType',
    required: false,
    includeIfNull: false,
  )
  final V3MediaPermissionSettingEnum? permissionType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateMediaPermissionSettingRequest &&
          other.permissionType == permissionType;

  @override
  int get hashCode => permissionType.hashCode;

  factory V3UpdateMediaPermissionSettingRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateMediaPermissionSettingRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateMediaPermissionSettingRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
