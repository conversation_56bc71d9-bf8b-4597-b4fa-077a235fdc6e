//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3ICEConnectionStateEnum {
  @JsonValue(0)
  ICE_CONNECTION_STATE_UNSPECIFIED('0'),
  @JsonValue(1)
  ICE_CONNECTION_STATE_NEW('1'),
  @JsonValue(2)
  ICE_CONNECTION_STATE_CHECKING('2'),
  @JsonValue(3)
  ICE_CONNECTION_STATE_CONNECTED('3'),
  @JsonValue(4)
  ICE_CONNECTION_STATE_COMPLETED('4'),
  @JsonValue(5)
  ICE_CONNECTION_STATE_FAILED('5'),
  @JsonValue(6)
  ICE_CONNECTION_STATE_DISCONNECTED('6'),
  @JsonValue(7)
  ICE_CONNECTION_STATE_CLOSE('7');

  const V3ICEConnectionStateEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
