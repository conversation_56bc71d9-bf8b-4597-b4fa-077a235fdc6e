//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/v3_speech_to_text_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'stream_result_of_v3_speech_to_text_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class StreamResultOfV3SpeechToTextResponse {
  /// Returns a new [StreamResultOfV3SpeechToTextResponse] instance.
  StreamResultOfV3SpeechToTextResponse({
    this.result,
    this.error,
  });

  @JsonKey(
    name: r'result',
    required: false,
    includeIfNull: false,
  )
  final V3SpeechToTextResponse? result;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final RpcStatus? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreamResultOfV3SpeechToTextResponse &&
          other.result == result &&
          other.error == error;

  @override
  int get hashCode => result.hashCode + error.hashCode;

  factory StreamResultOfV3SpeechToTextResponse.fromJson(
          Map<String, dynamic> json) =>
      _$StreamResultOfV3SpeechToTextResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$StreamResultOfV3SpeechToTextResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
