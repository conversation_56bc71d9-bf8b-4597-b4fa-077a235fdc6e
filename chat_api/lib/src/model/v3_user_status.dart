//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_status_expire_after_time_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_status.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserStatus {
  /// Returns a new [V3UserStatus] instance.
  V3UserStatus({
    this.content,
    this.status,
    this.expireAfterTime,
    this.createTime,
    this.updateTime,
    this.endTime,
  });

  @Json<PERSON>ey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @JsonKey(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final String? status;

  @JsonKey(
    name: r'expireAfterTime',
    required: false,
    includeIfNull: false,
  )
  final V3UserStatusExpireAfterTimeEnum? expireAfterTime;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'endTime',
    required: false,
    includeIfNull: false,
  )
  final String? endTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserStatus &&
          other.content == content &&
          other.status == status &&
          other.expireAfterTime == expireAfterTime &&
          other.createTime == createTime &&
          other.updateTime == updateTime &&
          other.endTime == endTime;

  @override
  int get hashCode =>
      content.hashCode +
      status.hashCode +
      expireAfterTime.hashCode +
      createTime.hashCode +
      updateTime.hashCode +
      endTime.hashCode;

  factory V3UserStatus.fromJson(Map<String, dynamic> json) =>
      _$V3UserStatusFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserStatusToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
