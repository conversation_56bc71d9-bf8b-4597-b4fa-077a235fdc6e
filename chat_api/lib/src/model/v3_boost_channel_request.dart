//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_boost_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3BoostChannelRequest {
  /// Returns a new [V3BoostChannelRequest] instance.
  V3BoostChannelRequest({
    this.workspaceId,
    this.channelId,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3BoostChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode;

  factory V3BoostChannelRequest.fromJson(Map<String, dynamic> json) =>
      _$V3BoostChannelRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3BoostChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
