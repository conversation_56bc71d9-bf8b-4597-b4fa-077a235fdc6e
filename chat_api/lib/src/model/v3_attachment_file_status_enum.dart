//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED: Unspecified, default value  - ATTACHMENT_FILE_STATUS_ENUM_UPLOADING: File Uploading to server  - ATTACHMENT_FILE_STATUS_ENUM_SUCCESS: File uploaded success  - ATTACHMENT_FILE_STATUS_ENUM_FAILURE: File uploaded failed
enum V3AttachmentFileStatusEnum {
  /// - ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED: Unspecified, default value  - ATTACHMENT_FILE_STATUS_ENUM_UPLOADING: File Uploading to server  - ATTACHMENT_FILE_STATUS_ENUM_SUCCESS: File uploaded success  - ATTACHMENT_FILE_STATUS_ENUM_FAILURE: File uploaded failed
  @JsonValue(0)
  ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED('0'),

  /// - ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED: Unspecified, default value  - ATTACHMENT_FILE_STATUS_ENUM_UPLOADING: File Uploading to server  - ATTACHMENT_FILE_STATUS_ENUM_SUCCESS: File uploaded success  - ATTACHMENT_FILE_STATUS_ENUM_FAILURE: File uploaded failed
  @JsonValue(1)
  ATTACHMENT_FILE_STATUS_ENUM_UPLOADING('1'),

  /// - ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED: Unspecified, default value  - ATTACHMENT_FILE_STATUS_ENUM_UPLOADING: File Uploading to server  - ATTACHMENT_FILE_STATUS_ENUM_SUCCESS: File uploaded success  - ATTACHMENT_FILE_STATUS_ENUM_FAILURE: File uploaded failed
  @JsonValue(2)
  ATTACHMENT_FILE_STATUS_ENUM_SUCCESS('2'),

  /// - ATTACHMENT_FILE_STATUS_ENUM_UNSPECIFIED: Unspecified, default value  - ATTACHMENT_FILE_STATUS_ENUM_UPLOADING: File Uploading to server  - ATTACHMENT_FILE_STATUS_ENUM_SUCCESS: File uploaded success  - ATTACHMENT_FILE_STATUS_ENUM_FAILURE: File uploaded failed
  @JsonValue(3)
  ATTACHMENT_FILE_STATUS_ENUM_FAILURE('3');

  const V3AttachmentFileStatusEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
