//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_login_with_smart_otp_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LoginWithSmartOtpRequest {
  /// Returns a new [V3LoginWithSmartOtpRequest] instance.
  V3LoginWithSmartOtpRequest({
    this.reqId,
    this.reqVerifier,
    this.nonce,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  /// he randomly generated value created by the client.
  @Json<PERSON>ey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'nonce',
    required: false,
    includeIfNull: false,
  )
  final String? nonce;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LoginWithSmartOtpRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.nonce == nonce;

  @override
  int get hashCode => reqId.hashCode + reqVerifier.hashCode + nonce.hashCode;

  factory V3LoginWithSmartOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$V3LoginWithSmartOtpRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3LoginWithSmartOtpRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
