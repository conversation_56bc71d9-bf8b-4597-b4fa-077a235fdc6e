//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_unfriend_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UnfriendRequest {
  /// Returns a new [V3UnfriendRequest] instance.
  V3UnfriendRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UnfriendRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3UnfriendRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UnfriendRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UnfriendRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
