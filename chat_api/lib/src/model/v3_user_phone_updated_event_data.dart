//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_phone_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserPhoneUpdatedEventData {
  /// Returns a new [V3UserPhoneUpdatedEventData] instance.
  V3UserPhoneUpdatedEventData({
    this.actorId,
    this.phone,
    this.phoneHash,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @J<PERSON><PERSON><PERSON>(
    name: r'phone',
    required: false,
    includeIfNull: false,
  )
  final String? phone;

  @JsonKey(
    name: r'phoneHash',
    required: false,
    includeIfNull: false,
  )
  final String? phoneHash;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserPhoneUpdatedEventData &&
          other.actorId == actorId &&
          other.phone == phone &&
          other.phoneHash == phoneHash;

  @override
  int get hashCode => actorId.hashCode + phone.hashCode + phoneHash.hashCode;

  factory V3UserPhoneUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserPhoneUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserPhoneUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
