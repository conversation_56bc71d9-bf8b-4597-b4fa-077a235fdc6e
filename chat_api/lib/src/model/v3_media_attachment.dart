//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:chat_api/src/model/v3_link_object.dart';
import 'package:chat_api/src/model/v3_sticker_object.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_media_attachment.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MediaAttachment {
  /// Returns a new [V3MediaAttachment] instance.
  V3MediaAttachment({
    this.link,
    this.sticker,
    this.photo,
    this.audio,
    this.video,
    this.voiceMessage,
    this.videoMessage,
    this.mediaMessage,
    this.file,
  });

  @JsonKey(
    name: r'link',
    required: false,
    includeIfNull: false,
  )
  final V3LinkObject? link;

  @J<PERSON><PERSON><PERSON>(
    name: r'sticker',
    required: false,
    includeIfNull: false,
  )
  final V3StickerObject? sticker;

  @Json<PERSON>ey(
    name: r'photo',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? photo;

  @JsonKey(
    name: r'audio',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? audio;

  @JsonKey(
    name: r'video',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? video;

  @JsonKey(
    name: r'voiceMessage',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? voiceMessage;

  @JsonKey(
    name: r'videoMessage',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? videoMessage;

  @JsonKey(
    name: r'mediaMessage',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? mediaMessage;

  @JsonKey(
    name: r'file',
    required: false,
    includeIfNull: false,
  )
  final V3MediaObject? file;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MediaAttachment &&
          other.link == link &&
          other.sticker == sticker &&
          other.photo == photo &&
          other.audio == audio &&
          other.video == video &&
          other.voiceMessage == voiceMessage &&
          other.videoMessage == videoMessage &&
          other.mediaMessage == mediaMessage &&
          other.file == file;

  @override
  int get hashCode =>
      link.hashCode +
      sticker.hashCode +
      photo.hashCode +
      audio.hashCode +
      video.hashCode +
      voiceMessage.hashCode +
      videoMessage.hashCode +
      mediaMessage.hashCode +
      file.hashCode;

  factory V3MediaAttachment.fromJson(Map<String, dynamic> json) =>
      _$V3MediaAttachmentFromJson(json);

  Map<String, dynamic> toJson() => _$V3MediaAttachmentToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
