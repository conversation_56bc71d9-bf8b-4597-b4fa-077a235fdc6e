//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/user_setting_media_permission_setting.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_setting_privacy_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class UserSettingPrivacySetting {
  /// Returns a new [UserSettingPrivacySetting] instance.
  UserSettingPrivacySetting({
    this.mediaPermission,
  });

  @JsonKey(
    name: r'mediaPermission',
    required: false,
    includeIfNull: false,
  )
  final UserSettingMediaPermissionSetting? mediaPermission;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingPrivacySetting &&
          other.mediaPermission == mediaPermission;

  @override
  int get hashCode => mediaPermission.hashCode;

  factory UserSettingPrivacySetting.fromJson(Map<String, dynamic> json) =>
      _$UserSettingPrivacySettingFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingPrivacySettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
