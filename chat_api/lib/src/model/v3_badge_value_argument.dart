//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_badge_value_argument.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3BadgeValueArgument {
  /// Returns a new [V3BadgeValueArgument] instance.
  V3BadgeValueArgument({
    this.unreadChannelIds,
    this.unreadFriendRequestIds,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'unreadChannelIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? unreadChannelIds;

  @J<PERSON><PERSON>ey(
    name: r'unreadFriendRequestIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? unreadFriendRequestIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3BadgeValueArgument &&
          other.unreadChannelIds == unreadChannelIds &&
          other.unreadFriendRequestIds == unreadFriendRequestIds;

  @override
  int get hashCode =>
      unreadChannelIds.hashCode + unreadFriendRequestIds.hashCode;

  factory V3BadgeValueArgument.fromJson(Map<String, dynamic> json) =>
      _$V3BadgeValueArgumentFromJson(json);

  Map<String, dynamic> toJson() => _$V3BadgeValueArgumentToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
