//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_paging.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Paging {
  /// Returns a new [V3Paging] instance.
  V3Paging({
    this.hasNext,
    this.hasPrev,
    this.nextPageToken,
    this.prevPageToken,
  });

  /// Returns true if there is information on the next page.
  @JsonKey(
    name: r'hasNext',
    required: false,
    includeIfNull: false,
  )
  final bool? hasNext;

  /// Returns true if there is information on the prev page.
  @JsonKey(
    name: r'hasPrev',
    required: false,
    includeIfNull: false,
  )
  final bool? hasPrev;

  /// is the token to send a request to get the next page's data.
  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'nextPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? nextPageToken;

  /// is the token to send a request to get previous page data.
  @JsonKey(
    name: r'prevPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? prevPageToken;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Paging &&
          other.hasNext == hasNext &&
          other.hasPrev == hasPrev &&
          other.nextPageToken == nextPageToken &&
          other.prevPageToken == prevPageToken;

  @override
  int get hashCode =>
      hasNext.hashCode +
      hasPrev.hashCode +
      nextPageToken.hashCode +
      prevPageToken.hashCode;

  factory V3Paging.fromJson(Map<String, dynamic> json) =>
      _$V3PagingFromJson(json);

  Map<String, dynamic> toJson() => _$V3PagingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
