//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_recovery_code_generation_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateRecoveryCodeGenerationFlowRequest {
  /// Returns a new [V3InitiateRecoveryCodeGenerationFlowRequest] instance.
  V3InitiateRecoveryCodeGenerationFlowRequest({
    this.reqChallenge,
  });

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateRecoveryCodeGenerationFlowRequest &&
          other.reqChallenge == reqChallenge;

  @override
  int get hashCode => reqChallenge.hashCode;

  factory V3InitiateRecoveryCodeGenerationFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateRecoveryCodeGenerationFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateRecoveryCodeGenerationFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
