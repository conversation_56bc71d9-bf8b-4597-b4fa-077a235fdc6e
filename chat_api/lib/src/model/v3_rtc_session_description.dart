//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_session_description_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_rtc_session_description.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RTCSessionDescription {
  /// Returns a new [V3RTCSessionDescription] instance.
  V3RTCSessionDescription({
    this.rtcSessionDescriptionType,
    this.rtcSessionDescriptionSdp,
  });

  @Json<PERSON>ey(
    name: r'rtcSessionDescriptionType',
    required: false,
    includeIfNull: false,
  )
  final V3SessionDescriptionTypeEnum? rtcSessionDescriptionType;

  /// The SDP which describes the session.
  @JsonKey(
    name: r'rtcSessionDescriptionSdp',
    required: false,
    includeIfNull: false,
  )
  final String? rtcSessionDescriptionSdp;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RTCSessionDescription &&
          other.rtcSessionDescriptionType == rtcSessionDescriptionType &&
          other.rtcSessionDescriptionSdp == rtcSessionDescriptionSdp;

  @override
  int get hashCode =>
      rtcSessionDescriptionType.hashCode + rtcSessionDescriptionSdp.hashCode;

  factory V3RTCSessionDescription.fromJson(Map<String, dynamic> json) =>
      _$V3RTCSessionDescriptionFromJson(json);

  Map<String, dynamic> toJson() => _$V3RTCSessionDescriptionToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
