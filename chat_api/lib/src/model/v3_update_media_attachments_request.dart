//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_media_attachments_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateMediaAttachmentsRequest {
  /// Returns a new [V3UpdateMediaAttachmentsRequest] instance.
  V3UpdateMediaAttachmentsRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.mediaObjects,
    this.ref,
  });

  @Json<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @J<PERSON><PERSON><PERSON>(
    name: r'mediaObjects',
    required: false,
    includeIfNull: false,
  )
  final List<V3MediaObject>? mediaObjects;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateMediaAttachmentsRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.mediaObjects == mediaObjects &&
          other.ref == ref;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      mediaObjects.hashCode +
      ref.hashCode;

  factory V3UpdateMediaAttachmentsRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateMediaAttachmentsRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateMediaAttachmentsRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
