//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_speech_to_text_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SpeechToTextRequest {
  /// Returns a new [V3SpeechToTextRequest] instance.
  V3SpeechToTextRequest({
    this.data,
  });

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final String? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SpeechToTextRequest && other.data == data;

  @override
  int get hashCode => data.hashCode;

  factory V3SpeechToTextRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SpeechToTextRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SpeechToTextRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
