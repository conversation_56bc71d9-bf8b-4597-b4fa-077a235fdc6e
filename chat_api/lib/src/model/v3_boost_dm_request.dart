//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_boost_dm_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3BoostDMRequest {
  /// Returns a new [V3BoostDMRequest] instance.
  V3BoostDMRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3BoostDMRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3BoostDMRequest.fromJson(Map<String, dynamic> json) =>
      _$V3BoostDMRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3BoostDMRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
