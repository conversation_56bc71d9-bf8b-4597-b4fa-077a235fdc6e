//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_message_sticker_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendMessageStickerRequest {
  /// Returns a new [V3SendMessageStickerRequest] instance.
  V3SendMessageStickerRequest({
    this.workspaceId,
    this.channelId,
    this.ref,
    this.stickerId,
    this.fileRef,
  });

  @Json<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'stickerId',
    required: false,
    includeIfNull: false,
  )
  final String? stickerId;

  @J<PERSON><PERSON>ey(
    name: r'fileRef',
    required: false,
    includeIfNull: false,
  )
  final String? fileRef;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendMessageStickerRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.ref == ref &&
          other.stickerId == stickerId &&
          other.fileRef == fileRef;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      ref.hashCode +
      stickerId.hashCode +
      fileRef.hashCode;

  factory V3SendMessageStickerRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendMessageStickerRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendMessageStickerRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
