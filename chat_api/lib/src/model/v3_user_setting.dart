//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_scope_enum.dart';
import 'package:chat_api/src/model/user_setting_privacy_setting.dart';
import 'package:chat_api/src/model/user_setting_security_setting.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserSetting {
  /// Returns a new [V3UserSetting] instance.
  V3UserSetting({
    this.security,
    this.privacy,
    this.callScope,
    this.messageScope,
  });

  @JsonKey(
    name: r'security',
    required: false,
    includeIfNull: false,
  )
  final UserSettingSecuritySetting? security;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'privacy',
    required: false,
    includeIfNull: false,
  )
  final UserSettingPrivacySetting? privacy;

  @J<PERSON><PERSON><PERSON>(
    name: r'callScope',
    required: false,
    includeIfNull: false,
  )
  final V3UserScopeEnum? callScope;

  @JsonKey(
    name: r'messageScope',
    required: false,
    includeIfNull: false,
  )
  final V3UserScopeEnum? messageScope;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserSetting &&
          other.security == security &&
          other.privacy == privacy &&
          other.callScope == callScope &&
          other.messageScope == messageScope;

  @override
  int get hashCode =>
      security.hashCode +
      privacy.hashCode +
      callScope.hashCode +
      messageScope.hashCode;

  factory V3UserSetting.fromJson(Map<String, dynamic> json) =>
      _$V3UserSettingFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserSettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
