//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_view.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_visited_profile_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3VisitedProfileData {
  /// Returns a new [V3VisitedProfileData] instance.
  V3VisitedProfileData({
    this.user,
    this.visitedTime,
    this.lastVisitedTime,
  });

  @JsonKey(
    name: r'user',
    required: false,
    includeIfNull: false,
  )
  final V3UserView? user;

  @JsonKey(
    name: r'visitedTime',
    required: false,
    includeIfNull: false,
  )
  final String? visitedTime;

  @JsonKey(
    name: r'lastVisitedTime',
    required: false,
    includeIfNull: false,
  )
  final String? lastVisitedTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3VisitedProfileData &&
          other.user == user &&
          other.visitedTime == visitedTime &&
          other.lastVisitedTime == lastVisitedTime;

  @override
  int get hashCode =>
      user.hashCode + visitedTime.hashCode + lastVisitedTime.hashCode;

  factory V3VisitedProfileData.fromJson(Map<String, dynamic> json) =>
      _$V3VisitedProfileDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3VisitedProfileDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
