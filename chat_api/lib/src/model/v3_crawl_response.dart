//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_embed.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_crawl_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CrawlResponse {
  /// Returns a new [V3CrawlResponse] instance.
  V3CrawlResponse({
    this.ok,
    this.data,
    this.error,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  /// It contains a list of Embed messages.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3Embed>? data;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CrawlResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3CrawlResponse.fromJson(Map<String, dynamic> json) =>
      _$V3CrawlResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3CrawlResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
