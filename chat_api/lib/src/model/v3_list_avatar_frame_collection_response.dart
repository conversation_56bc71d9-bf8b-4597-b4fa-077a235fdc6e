//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_avatar_frame_collection_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_avatar_frame_collection_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListAvatarFrameCollectionResponse {
  /// Returns a new [V3ListAvatarFrameCollectionResponse] instance.
  V3ListAvatarFrameCollectionResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3AvatarFrameCollectionData>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListAvatarFrameCollectionResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + paging.hashCode;

  factory V3ListAvatarFrameCollectionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListAvatarFrameCollectionResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListAvatarFrameCollectionResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
