//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_device_info.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeviceInfo {
  /// Returns a new [V3DeviceInfo] instance.
  V3DeviceInfo({
    this.platform,
    this.osVersion,
    this.deviceId,
    this.deviceName,
    this.manufacturer,
    this.sdkVersion,
  });

  @JsonKey(
    name: r'platform',
    required: false,
    includeIfNull: false,
  )
  final String? platform;

  @Json<PERSON><PERSON>(
    name: r'osVersion',
    required: false,
    includeIfNull: false,
  )
  final String? osVersion;

  @Json<PERSON>ey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'deviceName',
    required: false,
    includeIfNull: false,
  )
  final String? deviceName;

  @J<PERSON><PERSON><PERSON>(
    name: r'manufacturer',
    required: false,
    includeIfNull: false,
  )
  final String? manufacturer;

  @J<PERSON><PERSON><PERSON>(
    name: r'sdkVersion',
    required: false,
    includeIfNull: false,
  )
  final String? sdkVersion;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeviceInfo &&
          other.platform == platform &&
          other.osVersion == osVersion &&
          other.deviceId == deviceId &&
          other.deviceName == deviceName &&
          other.manufacturer == manufacturer &&
          other.sdkVersion == sdkVersion;

  @override
  int get hashCode =>
      platform.hashCode +
      osVersion.hashCode +
      deviceId.hashCode +
      deviceName.hashCode +
      manufacturer.hashCode +
      sdkVersion.hashCode;

  factory V3DeviceInfo.fromJson(Map<String, dynamic> json) =>
      _$V3DeviceInfoFromJson(json);

  Map<String, dynamic> toJson() => _$V3DeviceInfoToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
