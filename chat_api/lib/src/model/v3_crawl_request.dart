//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_crawl_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CrawlRequest {
  /// Returns a new [V3CrawlRequest] instance.
  V3CrawlRequest({
    this.urls,
  });

  @JsonKey(
    name: r'urls',
    required: false,
    includeIfNull: false,
  )
  final List<String>? urls;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is V3CrawlRequest && other.urls == urls;

  @override
  int get hashCode => urls.hashCode;

  factory V3CrawlRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CrawlRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CrawlRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
