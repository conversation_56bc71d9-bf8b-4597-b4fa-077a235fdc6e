//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_left_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberLeftEventData {
  /// Returns a new [V3MemberLeftEventData] instance.
  V3MemberLeftEventData({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberLeftEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + userId.hashCode;

  factory V3MemberLeftEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MemberLeftEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberLeftEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
