//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_blocked_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserBlockedEventData {
  /// Returns a new [V3UserBlockedEventData] instance.
  V3UserBlockedEventData({
    this.actorId,
    this.targetUserId,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserBlockedEventData &&
          other.actorId == actorId &&
          other.targetUserId == targetUserId;

  @override
  int get hashCode => actorId.hashCode + targetUserId.hashCode;

  factory V3UserBlockedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserBlockedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserBlockedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
