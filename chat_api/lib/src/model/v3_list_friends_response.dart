//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_friend_data.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_friends_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListFriendsResponse {
  /// Returns a new [V3ListFriendsResponse] instance.
  V3ListFriendsResponse({
    this.ok,
    this.data,
    this.error,
    this.paging,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  /// List of values for the Friend interface.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3FriendData>? data;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListFriendsResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.paging == paging &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode +
      data.hashCode +
      error.hashCode +
      paging.hashCode +
      includes.hashCode;

  factory V3ListFriendsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListFriendsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListFriendsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
