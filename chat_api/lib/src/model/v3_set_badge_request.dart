//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_set_badge_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SetBadgeRequest {
  /// Returns a new [V3SetBadgeRequest] instance.
  V3SetBadgeRequest({
    this.usernames,
    this.badge,
  });

  @Json<PERSON>ey(
    name: r'usernames',
    required: false,
    includeIfNull: false,
  )
  final List<String>? usernames;

  @JsonKey(
    name: r'badge',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? badge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SetBadgeRequest &&
          other.usernames == usernames &&
          other.badge == badge;

  @override
  int get hashCode => usernames.hashCode + badge.hashCode;

  factory V3SetBadgeRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SetBadgeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SetBadgeRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
