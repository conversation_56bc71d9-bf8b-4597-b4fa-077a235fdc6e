//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/protobuf_any.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_cloud_event.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CloudEvent {
  /// Returns a new [V3CloudEvent] instance.
  V3CloudEvent({
    this.id,
    this.type,
    this.source_,
    this.specversion,
    this.datacontenttype,
    this.dataschema,
    this.subject,
    this.time,
    this.data,
  });

  @Json<PERSON>ey(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final String? type;

  @JsonKey(
    name: r'source',
    required: false,
    includeIfNull: false,
  )
  final String? source_;

  /// The version of the CloudEvents specification which the event uses. This enables the interpretation of the context.
  @Json<PERSON>ey(
    name: r'specversion',
    required: false,
    includeIfNull: false,
  )
  final String? specversion;

  /// Content type of data value. This attribute enables data to carry any type of content, whereby format and encoding might differ from that of the chosen event format.
  @JsonKey(
    name: r'datacontenttype',
    required: false,
    includeIfNull: false,
  )
  final String? datacontenttype;

  /// Identifies the schema that data adheres to. Incompatible changes to the schema SHOULD be reflected by a different URI.
  @JsonKey(
    name: r'dataschema',
    required: false,
    includeIfNull: false,
  )
  final String? dataschema;

  @JsonKey(
    name: r'subject',
    required: false,
    includeIfNull: false,
  )
  final String? subject;

  @JsonKey(
    name: r'time',
    required: false,
    includeIfNull: false,
  )
  final String? time;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final ProtobufAny? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CloudEvent &&
          other.id == id &&
          other.type == type &&
          other.source_ == source_ &&
          other.specversion == specversion &&
          other.datacontenttype == datacontenttype &&
          other.dataschema == dataschema &&
          other.subject == subject &&
          other.time == time &&
          other.data == data;

  @override
  int get hashCode =>
      id.hashCode +
      type.hashCode +
      source_.hashCode +
      specversion.hashCode +
      datacontenttype.hashCode +
      dataschema.hashCode +
      subject.hashCode +
      time.hashCode +
      data.hashCode;

  factory V3CloudEvent.fromJson(Map<String, dynamic> json) =>
      _$V3CloudEventFromJson(json);

  Map<String, dynamic> toJson() => _$V3CloudEventToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
