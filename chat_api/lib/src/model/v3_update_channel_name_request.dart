//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_channel_name_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateChannelNameRequest {
  /// Returns a new [V3UpdateChannelNameRequest] instance.
  V3UpdateChannelNameRequest({
    this.workspaceId,
    this.channelId,
    this.name,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateChannelNameRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.name == name;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode + name.hashCode;

  factory V3UpdateChannelNameRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateChannelNameRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateChannelNameRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
