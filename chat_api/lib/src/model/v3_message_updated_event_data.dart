//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_message.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_message_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MessageUpdatedEventData {
  /// Returns a new [V3MessageUpdatedEventData] instance.
  V3MessageUpdatedEventData({
    this.message,
    this.includes,
  });

  @Json<PERSON>ey(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final V3Message? message;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MessageUpdatedEventData &&
          other.message == message &&
          other.includes == includes;

  @override
  int get hashCode => message.hashCode + includes.hashCode;

  factory V3MessageUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MessageUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MessageUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
