//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_set_ringback_tone_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SetRingbackToneRequest {
  /// Returns a new [V3SetRingbackToneRequest] instance.
  V3SetRingbackToneRequest({
    this.ringbackToneId,
  });

  @JsonKey(
    name: r'ringbackToneId',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SetRingbackToneRequest &&
          other.ringbackToneId == ringbackToneId;

  @override
  int get hashCode => ringbackToneId.hashCode;

  factory V3SetRingbackToneRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SetRingbackToneRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SetRingbackToneRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
