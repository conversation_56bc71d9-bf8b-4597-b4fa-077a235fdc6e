//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_create_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneCreateRequest {
  /// Returns a new [V3RingbackToneCreateRequest] instance.
  V3RingbackToneCreateRequest({
    this.name,
    this.ringbackTonePath,
  });

  @JsonKey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @JsonKey(
    name: r'ringbackTonePath',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackTonePath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneCreateRequest &&
          other.name == name &&
          other.ringbackTonePath == ringbackTonePath;

  @override
  int get hashCode => name.hashCode + ringbackTonePath.hashCode;

  factory V3RingbackToneCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RingbackToneCreateRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
