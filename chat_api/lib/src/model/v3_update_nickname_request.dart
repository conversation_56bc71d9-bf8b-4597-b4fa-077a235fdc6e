//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_nickname_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateNicknameRequest {
  /// Returns a new [V3UpdateNicknameRequest] instance.
  V3UpdateNicknameRequest({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.nickname,
  });

  @<PERSON>son<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'nickname',
    required: false,
    includeIfNull: false,
  )
  final String? nickname;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateNicknameRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId &&
          other.nickname == nickname;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      userId.hashCode +
      nickname.hashCode;

  factory V3UpdateNicknameRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateNicknameRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateNicknameRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
