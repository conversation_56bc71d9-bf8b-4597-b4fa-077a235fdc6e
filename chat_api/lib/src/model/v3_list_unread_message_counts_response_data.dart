//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_unread_message_counts_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListUnreadMessageCountsResponseData {
  /// Returns a new [V3ListUnreadMessageCountsResponseData] instance.
  V3ListUnreadMessageCountsResponseData({
    this.workspaceId,
    this.channelId,
    this.totalNewMessages,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'totalNewMessages',
    required: false,
    includeIfNull: false,
  )
  final int? totalNewMessages;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListUnreadMessageCountsResponseData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.totalNewMessages == totalNewMessages;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + totalNewMessages.hashCode;

  factory V3ListUnreadMessageCountsResponseData.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListUnreadMessageCountsResponseDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListUnreadMessageCountsResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
