//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_embed_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3EmbedData {
  /// Returns a new [V3EmbedData] instance.
  V3EmbedData({
    this.url,
    this.version,
    this.title,
    this.authorName,
    this.authorUrl,
    this.providerName,
    this.providerUrl,
    this.cacheAge,
    this.html,
    this.width,
    this.height,
    this.description,
    this.thumbnailUrl,
    this.thumbnailWidth,
    this.thumbnailHeight,
  });

  @Json<PERSON>ey(
    name: r'url',
    required: false,
    includeIfNull: false,
  )
  final String? url;

  @JsonKey(
    name: r'version',
    required: false,
    includeIfNull: false,
  )
  final String? version;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'title',
    required: false,
    includeIfNull: false,
  )
  final String? title;

  @J<PERSON><PERSON><PERSON>(
    name: r'authorName',
    required: false,
    includeIfNull: false,
  )
  final String? authorName;

  @Json<PERSON><PERSON>(
    name: r'authorUrl',
    required: false,
    includeIfNull: false,
  )
  final String? authorUrl;

  @JsonKey(
    name: r'providerName',
    required: false,
    includeIfNull: false,
  )
  final String? providerName;

  @JsonKey(
    name: r'providerUrl',
    required: false,
    includeIfNull: false,
  )
  final String? providerUrl;

  @JsonKey(
    name: r'cacheAge',
    required: false,
    includeIfNull: false,
  )
  final String? cacheAge;

  @JsonKey(
    name: r'html',
    required: false,
    includeIfNull: false,
  )
  final String? html;

  @JsonKey(
    name: r'width',
    required: false,
    includeIfNull: false,
  )
  final int? width;

  @JsonKey(
    name: r'height',
    required: false,
    includeIfNull: false,
  )
  final int? height;

  @JsonKey(
    name: r'description',
    required: false,
    includeIfNull: false,
  )
  final String? description;

  @JsonKey(
    name: r'thumbnailUrl',
    required: false,
    includeIfNull: false,
  )
  final String? thumbnailUrl;

  @JsonKey(
    name: r'thumbnailWidth',
    required: false,
    includeIfNull: false,
  )
  final String? thumbnailWidth;

  @JsonKey(
    name: r'thumbnailHeight',
    required: false,
    includeIfNull: false,
  )
  final String? thumbnailHeight;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3EmbedData &&
          other.url == url &&
          other.version == version &&
          other.title == title &&
          other.authorName == authorName &&
          other.authorUrl == authorUrl &&
          other.providerName == providerName &&
          other.providerUrl == providerUrl &&
          other.cacheAge == cacheAge &&
          other.html == html &&
          other.width == width &&
          other.height == height &&
          other.description == description &&
          other.thumbnailUrl == thumbnailUrl &&
          other.thumbnailWidth == thumbnailWidth &&
          other.thumbnailHeight == thumbnailHeight;

  @override
  int get hashCode =>
      url.hashCode +
      version.hashCode +
      title.hashCode +
      authorName.hashCode +
      authorUrl.hashCode +
      providerName.hashCode +
      providerUrl.hashCode +
      cacheAge.hashCode +
      html.hashCode +
      width.hashCode +
      height.hashCode +
      description.hashCode +
      thumbnailUrl.hashCode +
      thumbnailWidth.hashCode +
      thumbnailHeight.hashCode;

  factory V3EmbedData.fromJson(Map<String, dynamic> json) =>
      _$V3EmbedDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3EmbedDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
