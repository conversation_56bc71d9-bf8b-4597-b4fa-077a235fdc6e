//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_display_name_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserDisplayNameUpdatedEventData {
  /// Returns a new [V3UserDisplayNameUpdatedEventData] instance.
  V3UserDisplayNameUpdatedEventData({
    this.actorId,
    this.displayName,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserDisplayNameUpdatedEventData &&
          other.actorId == actorId &&
          other.displayName == displayName;

  @override
  int get hashCode => actorId.hashCode + displayName.hashCode;

  factory V3UserDisplayNameUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserDisplayNameUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserDisplayNameUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
