//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_add_dm_message_reaction_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AddDMMessageReactionRequest {
  /// Returns a new [V3AddDMMessageReactionRequest] instance.
  V3AddDMMessageReactionRequest({
    this.userId,
    this.messageId,
    this.emoji,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @JsonKey(
    name: r'emoji',
    required: false,
    includeIfNull: false,
  )
  final String? emoji;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AddDMMessageReactionRequest &&
          other.userId == userId &&
          other.messageId == messageId &&
          other.emoji == emoji;

  @override
  int get hashCode => userId.hashCode + messageId.hashCode + emoji.hashCode;

  factory V3AddDMMessageReactionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AddDMMessageReactionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AddDMMessageReactionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
