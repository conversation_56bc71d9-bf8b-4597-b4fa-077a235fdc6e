//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_cache_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sticker.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Sticker {
  /// Returns a new [V3Sticker] instance.
  V3Sticker({
    this.collectionId,
    this.stickerId,
    this.name,
    this.tags,
    this.defaultEmoji,
    this.basePath,
    this.fileName,
    this.fileSize,
    this.mimeType,
    this.stickerUrl,
    this.cacheData,
    this.weight,
    this.updateTime,
    this.stickerRenderUrl,
  });

  @JsonKey(
    name: r'collectionId',
    required: false,
    includeIfNull: false,
  )
  final String? collectionId;

  @Json<PERSON>ey(
    name: r'stickerId',
    required: false,
    includeIfNull: false,
  )
  final String? stickerId;

  @J<PERSON><PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @JsonKey(
    name: r'tags',
    required: false,
    includeIfNull: false,
  )
  final List<String>? tags;

  @JsonKey(
    name: r'defaultEmoji',
    required: false,
    includeIfNull: false,
  )
  final String? defaultEmoji;

  @JsonKey(
    name: r'basePath',
    required: false,
    includeIfNull: false,
  )
  final String? basePath;

  @JsonKey(
    name: r'fileName',
    required: false,
    includeIfNull: false,
  )
  final String? fileName;

  @JsonKey(
    name: r'fileSize',
    required: false,
    includeIfNull: false,
  )
  final int? fileSize;

  @JsonKey(
    name: r'mimeType',
    required: false,
    includeIfNull: false,
  )
  final String? mimeType;

  @JsonKey(
    name: r'stickerUrl',
    required: false,
    includeIfNull: false,
  )
  final String? stickerUrl;

  @JsonKey(
    name: r'cacheData',
    required: false,
    includeIfNull: false,
  )
  final V3CacheData? cacheData;

  @JsonKey(
    name: r'weight',
    required: false,
    includeIfNull: false,
  )
  final int? weight;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'stickerRenderUrl',
    required: false,
    includeIfNull: false,
  )
  final String? stickerRenderUrl;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Sticker &&
          other.collectionId == collectionId &&
          other.stickerId == stickerId &&
          other.name == name &&
          other.tags == tags &&
          other.defaultEmoji == defaultEmoji &&
          other.basePath == basePath &&
          other.fileName == fileName &&
          other.fileSize == fileSize &&
          other.mimeType == mimeType &&
          other.stickerUrl == stickerUrl &&
          other.cacheData == cacheData &&
          other.weight == weight &&
          other.updateTime == updateTime &&
          other.stickerRenderUrl == stickerRenderUrl;

  @override
  int get hashCode =>
      collectionId.hashCode +
      stickerId.hashCode +
      name.hashCode +
      tags.hashCode +
      defaultEmoji.hashCode +
      basePath.hashCode +
      fileName.hashCode +
      fileSize.hashCode +
      mimeType.hashCode +
      stickerUrl.hashCode +
      cacheData.hashCode +
      weight.hashCode +
      updateTime.hashCode +
      stickerRenderUrl.hashCode;

  factory V3Sticker.fromJson(Map<String, dynamic> json) =>
      _$V3StickerFromJson(json);

  Map<String, dynamic> toJson() => _$V3StickerToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
