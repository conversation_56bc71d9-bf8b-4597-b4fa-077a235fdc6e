//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_meet_token_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MeetTokenData {
  /// Returns a new [V3MeetTokenData] instance.
  V3MeetTokenData({
    this.host,
    this.token,
  });

  @JsonKey(
    name: r'host',
    required: false,
    includeIfNull: false,
  )
  final String? host;

  @J<PERSON><PERSON><PERSON>(
    name: r'token',
    required: false,
    includeIfNull: false,
  )
  final String? token;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MeetTokenData && other.host == host && other.token == token;

  @override
  int get hashCode => host.hashCode + token.hashCode;

  factory V3MeetTokenData.fromJson(Map<String, dynamic> json) =>
      _$V3MeetTokenDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MeetTokenDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
