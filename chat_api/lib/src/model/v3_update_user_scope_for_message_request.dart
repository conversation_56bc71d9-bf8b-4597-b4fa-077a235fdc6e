//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_scope_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_scope_for_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserScopeForMessageRequest {
  /// Returns a new [V3UpdateUserScopeForMessageRequest] instance.
  V3UpdateUserScopeForMessageRequest({
    this.userScope,
  });

  @JsonKey(
    name: r'userScope',
    required: false,
    includeIfNull: false,
  )
  final V3UserScopeEnum? userScope;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserScopeForMessageRequest &&
          other.userScope == userScope;

  @override
  int get hashCode => userScope.hashCode;

  factory V3UpdateUserScopeForMessageRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateUserScopeForMessageRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateUserScopeForMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
