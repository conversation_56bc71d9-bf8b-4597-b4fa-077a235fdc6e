//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_error.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Error {
  /// Returns a new [V3Error] instance.
  V3Error({
    this.code,
    this.message,
    this.details,
  });

  @JsonKey(
    name: r'code',
    required: false,
    includeIfNull: false,
  )
  final int? code;

  @Json<PERSON>ey(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final String? message;

  @Json<PERSON>ey(
    name: r'details',
    required: false,
    includeIfNull: false,
  )
  final List<String>? details;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Error &&
          other.code == code &&
          other.message == message &&
          other.details == details;

  @override
  int get hashCode => code.hashCode + message.hashCode + details.hashCode;

  factory V3Error.fromJson(Map<String, dynamic> json) =>
      _$V3ErrorFromJson(json);

  Map<String, dynamic> toJson() => _$V3ErrorToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
