//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_typing_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelTypingEventData {
  /// Returns a new [V3ChannelTypingEventData] instance.
  V3ChannelTypingEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.name,
    this.avatar,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelTypingEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.name == name &&
          other.avatar == avatar;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      name.hashCode +
      avatar.hashCode;

  factory V3ChannelTypingEventData.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelTypingEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelTypingEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
