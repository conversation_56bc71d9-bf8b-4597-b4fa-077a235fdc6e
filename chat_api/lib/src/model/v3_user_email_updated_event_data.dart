//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_email_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserEmailUpdatedEventData {
  /// Returns a new [V3UserEmailUpdatedEventData] instance.
  V3UserEmailUpdatedEventData({
    this.actorId,
    this.email,
    this.emailHash,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @J<PERSON><PERSON><PERSON>(
    name: r'email',
    required: false,
    includeIfNull: false,
  )
  final String? email;

  @JsonKey(
    name: r'emailHash',
    required: false,
    includeIfNull: false,
  )
  final String? emailHash;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserEmailUpdatedEventData &&
          other.actorId == actorId &&
          other.email == email &&
          other.emailHash == emailHash;

  @override
  int get hashCode => actorId.hashCode + email.hashCode + emailHash.hashCode;

  factory V3UserEmailUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserEmailUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserEmailUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
