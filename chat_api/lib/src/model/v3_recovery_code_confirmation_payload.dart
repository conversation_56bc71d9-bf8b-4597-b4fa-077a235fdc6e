//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_public_key_credential_request_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_recovery_code_confirmation_payload.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RecoveryCodeConfirmationPayload {
  /// Returns a new [V3RecoveryCodeConfirmationPayload] instance.
  V3RecoveryCodeConfirmationPayload({
    this.reqId,
    this.credentialRequestOptions,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'credentialRequestOptions',
    required: false,
    includeIfNull: false,
  )
  final CommonPublicKeyCredentialRequestOptions? credentialRequestOptions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RecoveryCodeConfirmationPayload &&
          other.reqId == reqId &&
          other.credentialRequestOptions == credentialRequestOptions;

  @override
  int get hashCode => reqId.hashCode + credentialRequestOptions.hashCode;

  factory V3RecoveryCodeConfirmationPayload.fromJson(
          Map<String, dynamic> json) =>
      _$V3RecoveryCodeConfirmationPayloadFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3RecoveryCodeConfirmationPayloadToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
