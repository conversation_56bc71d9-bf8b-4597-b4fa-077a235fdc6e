//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_message.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_message_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MessageData {
  /// Returns a new [V3MessageData] instance.
  V3MessageData({
    this.message,
  });

  @JsonKey(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final V3Message? message;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MessageData && other.message == message;

  @override
  int get hashCode => message.hashCode;

  factory V3MessageData.fromJson(Map<String, dynamic> json) =>
      _$V3MessageDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MessageDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
