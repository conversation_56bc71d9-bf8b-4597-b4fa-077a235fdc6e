//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_nickname_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberNicknameUpdatedEventData {
  /// Returns a new [V3MemberNicknameUpdatedEventData] instance.
  V3MemberNicknameUpdatedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.targetUserId,
    this.nickname,
  });

  @Json<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'nickname',
    required: false,
    includeIfNull: false,
  )
  final String? nickname;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberNicknameUpdatedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.targetUserId == targetUserId &&
          other.nickname == nickname;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      targetUserId.hashCode +
      nickname.hashCode;

  factory V3MemberNicknameUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3MemberNicknameUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3MemberNicknameUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
