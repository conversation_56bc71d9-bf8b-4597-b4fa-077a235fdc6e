//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_avatar_frame_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AvatarFrameData {
  /// Returns a new [V3AvatarFrameData] instance.
  V3AvatarFrameData({
    this.avatarFrameId,
    this.avatarFramePath,
    this.isDefault,
    this.isActive,
    this.createTime,
    this.updateTime,
  });

  @JsonKey(
    name: r'avatarFrameId',
    required: false,
    includeIfNull: false,
  )
  final String? avatarFrameId;

  @JsonKey(
    name: r'avatarFramePath',
    required: false,
    includeIfNull: false,
  )
  final String? avatarFramePath;

  @<PERSON>son<PERSON><PERSON>(
    name: r'isDefault',
    required: false,
    includeIfNull: false,
  )
  final bool? isDefault;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'isActive',
    required: false,
    includeIfNull: false,
  )
  final bool? isActive;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AvatarFrameData &&
          other.avatarFrameId == avatarFrameId &&
          other.avatarFramePath == avatarFramePath &&
          other.isDefault == isDefault &&
          other.isActive == isActive &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      avatarFrameId.hashCode +
      avatarFramePath.hashCode +
      isDefault.hashCode +
      isActive.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3AvatarFrameData.fromJson(Map<String, dynamic> json) =>
      _$V3AvatarFrameDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3AvatarFrameDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
