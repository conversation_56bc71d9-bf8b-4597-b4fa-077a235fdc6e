//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_search_user_result.dart';
import 'package:chat_api/src/model/v3_search_channel_result.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_everything_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchEverythingResponse {
  /// Returns a new [V3SearchEverythingResponse] instance.
  V3SearchEverythingResponse({
    this.ok,
    this.error,
    this.channels,
    this.users,
    this.paging,
  });

  /// Indicates whether the search operation was successful.
  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  /// An array of search result items containing channel information.
  @JsonKey(
    name: r'channels',
    required: false,
    includeIfNull: false,
  )
  final List<V3SearchChannelResult>? channels;

  /// An array of search result items containing user information.
  @JsonKey(
    name: r'users',
    required: false,
    includeIfNull: false,
  )
  final List<V3SearchUserResult>? users;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchEverythingResponse &&
          other.ok == ok &&
          other.error == error &&
          other.channels == channels &&
          other.users == users &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode +
      error.hashCode +
      channels.hashCode +
      users.hashCode +
      paging.hashCode;

  factory V3SearchEverythingResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SearchEverythingResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchEverythingResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
