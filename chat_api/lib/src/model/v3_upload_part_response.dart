//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_completed_part.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_upload_part_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UploadPartResponse {
  /// Returns a new [V3UploadPartResponse] instance.
  V3UploadPartResponse({
    this.ok,
    this.error,
    this.completedPart,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'completedPart',
    required: false,
    includeIfNull: false,
  )
  final V3CompletedPart? completedPart;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UploadPartResponse &&
          other.ok == ok &&
          other.error == error &&
          other.completedPart == completedPart;

  @override
  int get hashCode => ok.hashCode + error.hashCode + completedPart.hashCode;

  factory V3UploadPartResponse.fromJson(Map<String, dynamic> json) =>
      _$V3UploadPartResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3UploadPartResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
