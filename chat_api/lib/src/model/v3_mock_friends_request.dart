//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_mock_friends_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MockFriendsRequest {
  /// Returns a new [V3MockFriendsRequest] instance.
  V3MockFriendsRequest({
    this.quantity,
    this.type,
  });

  @JsonKey(
    name: r'quantity',
    required: false,
    includeIfNull: false,
  )
  final int? quantity;

  @Json<PERSON>ey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final int? type;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MockFriendsRequest &&
          other.quantity == quantity &&
          other.type == type;

  @override
  int get hashCode => quantity.hashCode + type.hashCode;

  factory V3MockFriendsRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MockFriendsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MockFriendsRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
