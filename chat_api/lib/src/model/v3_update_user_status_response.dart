//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_user_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_status_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserStatusResponse {
  /// Returns a new [V3UpdateUserStatusResponse] instance.
  V3UpdateUserStatusResponse({
    this.ok,
    this.data,
    this.error,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3UserStatus? data;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserStatusResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3UpdateUserStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserStatusResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserStatusResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
