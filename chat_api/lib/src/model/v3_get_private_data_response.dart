//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_private_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_private_data_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetPrivateDataResponse {
  /// Returns a new [V3GetPrivateDataResponse] instance.
  V3GetPrivateDataResponse({
    this.ok,
    this.error,
    this.data,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3PrivateData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetPrivateDataResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3GetPrivateDataResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetPrivateDataResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetPrivateDataResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
