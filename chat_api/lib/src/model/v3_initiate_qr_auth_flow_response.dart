//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_qr_authentication.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_qr_auth_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateQRAuthFlowResponse {
  /// Returns a new [V3InitiateQRAuthFlowResponse] instance.
  V3InitiateQRAuthFlowResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3QRAuthentication? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateQRAuthFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3InitiateQRAuthFlowResponse.fromJson(Map<String, dynamic> json) =>
      _$V3InitiateQRAuthFlowResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3InitiateQRAuthFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
