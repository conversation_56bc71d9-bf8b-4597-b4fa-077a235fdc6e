//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_created_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelCreatedEventData {
  /// Returns a new [V3ChannelCreatedEventData] instance.
  V3ChannelCreatedEventData({
    this.channel,
    this.includes,
  });

  @JsonKey(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3Channel? channel;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelCreatedEventData &&
          other.channel == channel &&
          other.includes == includes;

  @override
  int get hashCode => channel.hashCode + includes.hashCode;

  factory V3ChannelCreatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelCreatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelCreatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
