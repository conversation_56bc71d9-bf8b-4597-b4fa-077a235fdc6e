//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_invitation_data_channel_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_invitation_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InvitationData {
  /// Returns a new [V3InvitationData] instance.
  V3InvitationData({
    this.channel,
    this.code,
    this.isExpired,
    this.expireTime,
    this.isJoined,
    this.invitationLink,
    this.createTime,
    this.updateTime,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3InvitationDataChannelData? channel;

  @<PERSON><PERSON><PERSON>ey(
    name: r'code',
    required: false,
    includeIfNull: false,
  )
  final String? code;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'isExpired',
    required: false,
    includeIfNull: false,
  )
  final bool? isExpired;

  @J<PERSON><PERSON><PERSON>(
    name: r'expireTime',
    required: false,
    includeIfNull: false,
  )
  final String? expireTime;

  @JsonKey(
    name: r'isJoined',
    required: false,
    includeIfNull: false,
  )
  final bool? isJoined;

  @JsonKey(
    name: r'invitationLink',
    required: false,
    includeIfNull: false,
  )
  final String? invitationLink;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InvitationData &&
          other.channel == channel &&
          other.code == code &&
          other.isExpired == isExpired &&
          other.expireTime == expireTime &&
          other.isJoined == isJoined &&
          other.invitationLink == invitationLink &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      channel.hashCode +
      code.hashCode +
      isExpired.hashCode +
      expireTime.hashCode +
      isJoined.hashCode +
      invitationLink.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3InvitationData.fromJson(Map<String, dynamic> json) =>
      _$V3InvitationDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3InvitationDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
