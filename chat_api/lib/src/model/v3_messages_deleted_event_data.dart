//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_destination_cloud_event.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_messages_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MessagesDeletedEventData {
  /// Returns a new [V3MessagesDeletedEventData] instance.
  V3MessagesDeletedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.messageIds,
    this.destination,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @J<PERSON><PERSON><PERSON>(
    name: r'messageIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? messageIds;

  @J<PERSON><PERSON><PERSON>(
    name: r'destination',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelDestinationCloudEvent? destination;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MessagesDeletedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.messageIds == messageIds &&
          other.destination == destination;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      messageIds.hashCode +
      destination.hashCode;

  factory V3MessagesDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MessagesDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MessagesDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
