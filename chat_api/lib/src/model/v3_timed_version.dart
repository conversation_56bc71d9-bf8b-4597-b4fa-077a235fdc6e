//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_timed_version.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TimedVersion {
  /// Returns a new [V3TimedVersion] instance.
  V3TimedVersion({
    this.unixMicro,
    this.ticks,
  });

  @JsonKey(
    name: r'unixMicro',
    required: false,
    includeIfNull: false,
  )
  final String? unixMicro;

  @Json<PERSON>ey(
    name: r'ticks',
    required: false,
    includeIfNull: false,
  )
  final int? ticks;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TimedVersion &&
          other.unixMicro == unixMicro &&
          other.ticks == ticks;

  @override
  int get hashCode => unixMicro.hashCode + ticks.hashCode;

  factory V3TimedVersion.fromJson(Map<String, dynamic> json) =>
      _$V3TimedVersionFromJson(json);

  Map<String, dynamic> toJson() => _$V3TimedVersionToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
