//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_confirm_account_deletion_by_security_key_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ConfirmAccountDeletionBySecurityKeyResponse {
  /// Returns a new [V3ConfirmAccountDeletionBySecurityKeyResponse] instance.
  V3ConfirmAccountDeletionBySecurityKeyResponse({
    this.ok,
    this.error,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ConfirmAccountDeletionBySecurityKeyResponse &&
          other.ok == ok &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + error.hashCode;

  factory V3ConfirmAccountDeletionBySecurityKeyResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ConfirmAccountDeletionBySecurityKeyResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ConfirmAccountDeletionBySecurityKeyResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
