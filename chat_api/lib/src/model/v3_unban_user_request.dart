//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_unban_user_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UnbanUserRequest {
  /// Returns a new [V3UnbanUserRequest] instance.
  V3UnbanUserRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UnbanUserRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3UnbanUserRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UnbanUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UnbanUserRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
