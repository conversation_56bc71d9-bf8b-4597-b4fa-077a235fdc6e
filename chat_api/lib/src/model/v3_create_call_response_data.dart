//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_rtc_ice_server.dart';
import 'package:chat_api/src/model/v3_call_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_create_call_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CreateCallResponseData {
  /// Returns a new [V3CreateCallResponseData] instance.
  V3CreateCallResponseData({
    this.callData,
    this.rtcIceServers,
  });

  @JsonKey(
    name: r'callData',
    required: false,
    includeIfNull: false,
  )
  final V3CallData? callData;

  @JsonKey(
    name: r'rtcIceServers',
    required: false,
    includeIfNull: false,
  )
  final List<V3RTCIceServer>? rtcIceServers;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CreateCallResponseData &&
          other.callData == callData &&
          other.rtcIceServers == rtcIceServers;

  @override
  int get hashCode => callData.hashCode + rtcIceServers.hashCode;

  factory V3CreateCallResponseData.fromJson(Map<String, dynamic> json) =>
      _$V3CreateCallResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CreateCallResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
