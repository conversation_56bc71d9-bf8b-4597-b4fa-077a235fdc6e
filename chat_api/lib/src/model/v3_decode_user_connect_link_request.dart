//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_decode_user_connect_link_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DecodeUserConnectLinkRequest {
  /// Returns a new [V3DecodeUserConnectLinkRequest] instance.
  V3DecodeUserConnectLinkRequest({
    this.link,
  });

  @JsonKey(
    name: r'link',
    required: false,
    includeIfNull: false,
  )
  final String? link;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DecodeUserConnectLinkRequest && other.link == link;

  @override
  int get hashCode => link.hashCode;

  factory V3DecodeUserConnectLinkRequest.fromJson(Map<String, dynamic> json) =>
      _$V3DecodeUserConnectLinkRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3DecodeUserConnectLinkRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
