//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_leave_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LeaveChannelRequest {
  /// Returns a new [V3LeaveChannelRequest] instance.
  V3LeaveChannelRequest({
    this.workspaceId,
    this.channelId,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LeaveChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode;

  factory V3LeaveChannelRequest.fromJson(Map<String, dynamic> json) =>
      _$V3LeaveChannelRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3LeaveChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
