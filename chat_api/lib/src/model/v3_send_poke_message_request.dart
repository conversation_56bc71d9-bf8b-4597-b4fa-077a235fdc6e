//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_poke_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendPokeMessageRequest {
  /// Returns a new [V3SendPokeMessageRequest] instance.
  V3SendPokeMessageRequest({
    this.userId,
    this.ref,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendPokeMessageRequest &&
          other.userId == userId &&
          other.ref == ref;

  @override
  int get hashCode => userId.hashCode + ref.hashCode;

  factory V3SendPokeMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendPokeMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendPokeMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
