//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_accept_friend_request_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AcceptFriendRequestRequest {
  /// Returns a new [V3AcceptFriendRequestRequest] instance.
  V3AcceptFriendRequestRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AcceptFriendRequestRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3AcceptFriendRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AcceptFriendRequestRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AcceptFriendRequestRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
