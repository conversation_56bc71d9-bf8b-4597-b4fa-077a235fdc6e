//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_migrate_passkey_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MigratePasskeyRequest {
  /// Returns a new [V3MigratePasskeyRequest] instance.
  V3MigratePasskeyRequest({
    this.reqChallenge,
  });

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MigratePasskeyRequest && other.reqChallenge == reqChallenge;

  @override
  int get hashCode => reqChallenge.hashCode;

  factory V3MigratePasskeyRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MigratePasskeyRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MigratePasskeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
