//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_suggest_friend.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_suggested_friends_by_type_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListSuggestedFriendsByTypeResponse {
  /// Returns a new [V3ListSuggestedFriendsByTypeResponse] instance.
  V3ListSuggestedFriendsByTypeResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3SuggestFriend>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListSuggestedFriendsByTypeResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + paging.hashCode;

  factory V3ListSuggestedFriendsByTypeResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListSuggestedFriendsByTypeResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListSuggestedFriendsByTypeResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
