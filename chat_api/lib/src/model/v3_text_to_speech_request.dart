//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_ssml_voice_gender_enum.dart';
import 'package:chat_api/src/model/v3_audio_text_to_speech_encoding_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_text_to_speech_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TextToSpeechRequest {
  /// Returns a new [V3TextToSpeechRequest] instance.
  V3TextToSpeechRequest({
    this.text,
    this.languageCode,
    this.genderAudio,
    this.audioEncoding,
  });

  @Json<PERSON>ey(
    name: r'text',
    required: false,
    includeIfNull: false,
  )
  final String? text;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'languageCode',
    required: false,
    includeIfNull: false,
  )
  final String? languageCode;

  @Json<PERSON>ey(
    name: r'genderAudio',
    required: false,
    includeIfNull: false,
  )
  final V3SsmlVoiceGenderEnum? genderAudio;

  @JsonKey(
    name: r'audioEncoding',
    required: false,
    includeIfNull: false,
  )
  final V3AudioTextToSpeechEncodingEnum? audioEncoding;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TextToSpeechRequest &&
          other.text == text &&
          other.languageCode == languageCode &&
          other.genderAudio == genderAudio &&
          other.audioEncoding == audioEncoding;

  @override
  int get hashCode =>
      text.hashCode +
      languageCode.hashCode +
      genderAudio.hashCode +
      audioEncoding.hashCode;

  factory V3TextToSpeechRequest.fromJson(Map<String, dynamic> json) =>
      _$V3TextToSpeechRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3TextToSpeechRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
