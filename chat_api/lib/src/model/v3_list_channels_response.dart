//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/sharedv3_channel_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_channels_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListChannelsResponse {
  /// Returns a new [V3ListChannelsResponse] instance.
  V3ListChannelsResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  /// List of values for the Channel interface.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<Sharedv3ChannelData>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListChannelsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode +
      error.hashCode +
      data.hashCode +
      paging.hashCode +
      includes.hashCode;

  factory V3ListChannelsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListChannelsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListChannelsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
