//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - STICKER_COLLECTION_TYPE_ENUM_DEFAULT: Default sticker collection  - STICKER_COLLECTION_TYPE_ENUM_CUSTOM: Custom sticker collection
enum V3StickerCollectionTypeEnum {
  /// - STICKER_COLLECTION_TYPE_ENUM_DEFAULT: Default sticker collection  - STICKER_COLLECTION_TYPE_ENUM_CUSTOM: Custom sticker collection
  @JsonValue(0)
  STICKER_COLLECTION_TYPE_ENUM_DEFAULT('0'),

  /// - STICKER_COLLECTION_TYPE_ENUM_DEFAULT: Default sticker collection  - STICKER_COLLECTION_TYPE_ENUM_CUSTOM: Custom sticker collection
  @JsonValue(1)
  STICKER_COLLECTION_TYPE_ENUM_CUSTOM('1');

  const V3StickerCollectionTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
