//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_register_request_user_key.dart';
import 'package:chat_api/src/model/v3_login_request_user_key.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_user_key_auth_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateUserKeyAuthFlowResponse {
  /// Returns a new [V3InitiateUserKeyAuthFlowResponse] instance.
  V3InitiateUserKeyAuthFlowResponse({
    this.ok,
    this.error,
    this.loginRequest,
    this.registerRequest,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'loginRequest',
    required: false,
    includeIfNull: false,
  )
  final V3LoginRequestUserKey? loginRequest;

  @JsonKey(
    name: r'registerRequest',
    required: false,
    includeIfNull: false,
  )
  final V3RegisterRequestUserKey? registerRequest;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateUserKeyAuthFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.loginRequest == loginRequest &&
          other.registerRequest == registerRequest;

  @override
  int get hashCode =>
      ok.hashCode +
      error.hashCode +
      loginRequest.hashCode +
      registerRequest.hashCode;

  factory V3InitiateUserKeyAuthFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateUserKeyAuthFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateUserKeyAuthFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
