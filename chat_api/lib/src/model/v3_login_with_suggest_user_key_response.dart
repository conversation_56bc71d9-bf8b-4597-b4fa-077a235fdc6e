//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_session_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_login_with_suggest_user_key_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LoginWithSuggestUserKeyResponse {
  /// Returns a new [V3LoginWithSuggestUserKeyResponse] instance.
  V3LoginWithSuggestUserKeyResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3SessionData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LoginWithSuggestUserKeyResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3LoginWithSuggestUserKeyResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3LoginWithSuggestUserKeyResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3LoginWithSuggestUserKeyResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
