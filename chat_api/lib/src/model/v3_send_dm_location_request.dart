//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_dm_location_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendDMLocationRequest {
  /// Returns a new [V3SendDMLocationRequest] instance.
  V3SendDMLocationRequest({
    this.userId,
    this.content,
    this.latitude,
    this.longitude,
    this.description,
    this.ref,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @Json<PERSON>ey(
    name: r'latitude',
    required: false,
    includeIfNull: false,
  )
  final String? latitude;

  @<PERSON>sonKey(
    name: r'longitude',
    required: false,
    includeIfNull: false,
  )
  final String? longitude;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'description',
    required: false,
    includeIfNull: false,
  )
  final String? description;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendDMLocationRequest &&
          other.userId == userId &&
          other.content == content &&
          other.latitude == latitude &&
          other.longitude == longitude &&
          other.description == description &&
          other.ref == ref;

  @override
  int get hashCode =>
      userId.hashCode +
      content.hashCode +
      latitude.hashCode +
      longitude.hashCode +
      description.hashCode +
      ref.hashCode;

  factory V3SendDMLocationRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendDMLocationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendDMLocationRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
