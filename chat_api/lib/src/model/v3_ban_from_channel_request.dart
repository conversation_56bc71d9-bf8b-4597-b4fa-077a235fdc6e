//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ban_from_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3BanFromChannelRequest {
  /// Returns a new [V3BanFromChannelRequest] instance.
  V3BanFromChannelRequest({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3BanFromChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + userId.hashCode;

  factory V3BanFromChannelRequest.fromJson(Map<String, dynamic> json) =>
      _$V3BanFromChannelRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3BanFromChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
