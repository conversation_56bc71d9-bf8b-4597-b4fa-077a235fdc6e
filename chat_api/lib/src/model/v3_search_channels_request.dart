//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_channels_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchChannelsRequest {
  /// Returns a new [V3SearchChannelsRequest] instance.
  V3SearchChannelsRequest({
    this.keyword,
    this.limit,
    this.nextPageToken,
    this.prevPageToken,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'keyword',
    required: false,
    includeIfNull: false,
  )
  final String? keyword;

  /// The maximum number of search results to retrieve.
  @JsonKey(
    name: r'limit',
    required: false,
    includeIfNull: false,
  )
  final int? limit;

  /// A token to retrieve the next page of search results.
  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'nextPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? nextPageToken;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'prevPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? prevPageToken;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchChannelsRequest &&
          other.keyword == keyword &&
          other.limit == limit &&
          other.nextPageToken == nextPageToken &&
          other.prevPageToken == prevPageToken;

  @override
  int get hashCode =>
      keyword.hashCode +
      limit.hashCode +
      nextPageToken.hashCode +
      prevPageToken.hashCode;

  factory V3SearchChannelsRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SearchChannelsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchChannelsRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
