//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_decode_user_connect_link_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DecodeUserConnectLinkResponseData {
  /// Returns a new [V3DecodeUserConnectLinkResponseData] instance.
  V3DecodeUserConnectLinkResponseData({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DecodeUserConnectLinkResponseData && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3DecodeUserConnectLinkResponseData.fromJson(
          Map<String, dynamic> json) =>
      _$V3DecodeUserConnectLinkResponseDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3DecodeUserConnectLinkResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
