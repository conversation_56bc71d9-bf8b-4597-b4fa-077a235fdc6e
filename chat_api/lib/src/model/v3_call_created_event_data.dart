//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_rtc_ice_server.dart';
import 'package:chat_api/src/model/v3_call_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_call_created_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CallCreatedEventData {
  /// Returns a new [V3CallCreatedEventData] instance.
  V3CallCreatedEventData({
    this.callData,
    this.rtcIceServers,
  });

  @JsonKey(
    name: r'callData',
    required: false,
    includeIfNull: false,
  )
  final V3CallData? callData;

  @JsonKey(
    name: r'rtcIceServers',
    required: false,
    includeIfNull: false,
  )
  final List<V3RTCIceServer>? rtcIceServers;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CallCreatedEventData &&
          other.callData == callData &&
          other.rtcIceServers == rtcIceServers;

  @override
  int get hashCode => callData.hashCode + rtcIceServers.hashCode;

  factory V3CallCreatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3CallCreatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CallCreatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
