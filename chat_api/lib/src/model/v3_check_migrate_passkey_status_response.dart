//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_check_migrate_passkey_status_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CheckMigratePasskeyStatusResponse {
  /// Returns a new [V3CheckMigratePasskeyStatusResponse] instance.
  V3CheckMigratePasskeyStatusResponse({
    this.ok,
    this.error,
    this.data,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON>son<PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final bool? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CheckMigratePasskeyStatusResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3CheckMigratePasskeyStatusResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3CheckMigratePasskeyStatusResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3CheckMigratePasskeyStatusResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
