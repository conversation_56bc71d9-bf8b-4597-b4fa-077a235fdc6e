//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_avatar_frame_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_avatar_frame_collection_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AvatarFrameCollectionData {
  /// Returns a new [V3AvatarFrameCollectionData] instance.
  V3AvatarFrameCollectionData({
    this.collection,
    this.avatarFrames,
  });

  @JsonKey(
    name: r'collection',
    required: false,
    includeIfNull: false,
  )
  final String? collection;

  @JsonKey(
    name: r'avatarFrames',
    required: false,
    includeIfNull: false,
  )
  final List<V3AvatarFrameData>? avatarFrames;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AvatarFrameCollectionData &&
          other.collection == collection &&
          other.avatarFrames == avatarFrames;

  @override
  int get hashCode => collection.hashCode + avatarFrames.hashCode;

  factory V3AvatarFrameCollectionData.fromJson(Map<String, dynamic> json) =>
      _$V3AvatarFrameCollectionDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3AvatarFrameCollectionDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
