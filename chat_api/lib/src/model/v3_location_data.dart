//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_location_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LocationData {
  /// Returns a new [V3LocationData] instance.
  V3LocationData({
    this.latitude,
    this.longitude,
    this.description,
    this.thumbnailUrl,
  });

  @Json<PERSON>ey(
    name: r'latitude',
    required: false,
    includeIfNull: false,
  )
  final String? latitude;

  @Json<PERSON>ey(
    name: r'longitude',
    required: false,
    includeIfNull: false,
  )
  final String? longitude;

  @Json<PERSON>ey(
    name: r'description',
    required: false,
    includeIfNull: false,
  )
  final String? description;

  @Json<PERSON>ey(
    name: r'thumbnailUrl',
    required: false,
    includeIfNull: false,
  )
  final String? thumbnailUrl;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LocationData &&
          other.latitude == latitude &&
          other.longitude == longitude &&
          other.description == description &&
          other.thumbnailUrl == thumbnailUrl;

  @override
  int get hashCode =>
      latitude.hashCode +
      longitude.hashCode +
      description.hashCode +
      thumbnailUrl.hashCode;

  factory V3LocationData.fromJson(Map<String, dynamic> json) =>
      _$V3LocationDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3LocationDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
