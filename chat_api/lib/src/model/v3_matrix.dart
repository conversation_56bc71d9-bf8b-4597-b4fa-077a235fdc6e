//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_matrix.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Matrix {
  /// Returns a new [V3Matrix] instance.
  V3Matrix({
    this.row,
    this.column,
  });

  @JsonKey(
    name: r'row',
    required: false,
    includeIfNull: false,
  )
  final int? row;

  @JsonKey(
    name: r'column',
    required: false,
    includeIfNull: false,
  )
  final int? column;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Matrix && other.row == row && other.column == column;

  @override
  int get hashCode => row.hashCode + column.hashCode;

  factory V3Matrix.fromJson(Map<String, dynamic> json) =>
      _$V3MatrixFromJson(json);

  Map<String, dynamic> toJson() => _$V3MatrixToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
