//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - USER_DELETED: User deleted  - USER_BLOCKED: User blocked
enum V3UserDeletedTypeEnum {
  /// - USER_DELETED: User deleted  - USER_BLOCKED: User blocked
  @JsonValue(0)
  USER_DELETED('0'),

  /// - USER_DELETED: User deleted  - USER_BLOCKED: User blocked
  @JsonValue(1)
  USER_BLOCKED('1');

  const V3UserDeletedTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
