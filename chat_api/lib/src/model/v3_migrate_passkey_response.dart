//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_register_request_user_key.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_migrate_passkey_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MigratePasskeyResponse {
  /// Returns a new [V3MigratePasskeyResponse] instance.
  V3MigratePasskeyResponse({
    this.ok,
    this.error,
    this.data,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3RegisterRequestUserKey? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MigratePasskeyResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3MigratePasskeyResponse.fromJson(Map<String, dynamic> json) =>
      _$V3MigratePasskeyResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3MigratePasskeyResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
