//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserDeletedEventData {
  /// Returns a new [V3UserDeletedEventData] instance.
  V3UserDeletedEventData({
    this.userId,
    this.username,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserDeletedEventData &&
          other.userId == userId &&
          other.username == username;

  @override
  int get hashCode => userId.hashCode + username.hashCode;

  factory V3UserDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
