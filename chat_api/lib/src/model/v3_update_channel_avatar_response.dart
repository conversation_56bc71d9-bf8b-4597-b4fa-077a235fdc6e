//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/sharedv3_channel_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_channel_avatar_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateChannelAvatarResponse {
  /// Returns a new [V3UpdateChannelAvatarResponse] instance.
  V3UpdateChannelAvatarResponse({
    this.ok,
    this.data,
    this.error,
    this.includes,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final Sharedv3ChannelData? data;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateChannelAvatarResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + data.hashCode + error.hashCode + includes.hashCode;

  factory V3UpdateChannelAvatarResponse.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateChannelAvatarResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateChannelAvatarResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
