//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'user_setting_security_key_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class UserSettingSecurityKeySetting {
  /// Returns a new [UserSettingSecurityKeySetting] instance.
  UserSettingSecurityKeySetting({
    this.enable,
  });

  @JsonKey(
    name: r'enable',
    required: false,
    includeIfNull: false,
  )
  final bool? enable;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingSecurityKeySetting && other.enable == enable;

  @override
  int get hashCode => enable.hashCode;

  factory UserSettingSecurityKeySetting.fromJson(Map<String, dynamic> json) =>
      _$UserSettingSecurityKeySettingFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingSecurityKeySettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
