//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_status_expire_after_time_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_add_user_status_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AddUserStatusRequest {
  /// Returns a new [V3AddUserStatusRequest] instance.
  V3AddUserStatusRequest({
    this.content,
    this.status,
    this.expireAfterTime,
  });

  @Json<PERSON><PERSON>(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @JsonKey(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final String? status;

  @JsonKey(
    name: r'expireAfterTime',
    required: false,
    includeIfNull: false,
  )
  final V3UserStatusExpireAfterTimeEnum? expireAfterTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AddUserStatusRequest &&
          other.content == content &&
          other.status == status &&
          other.expireAfterTime == expireAfterTime;

  @override
  int get hashCode =>
      content.hashCode + status.hashCode + expireAfterTime.hashCode;

  factory V3AddUserStatusRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AddUserStatusRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AddUserStatusRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
