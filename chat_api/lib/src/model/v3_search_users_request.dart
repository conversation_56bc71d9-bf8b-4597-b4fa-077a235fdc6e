//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_users_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchUsersRequest {
  /// Returns a new [V3SearchUsersRequest] instance.
  V3SearchUsersRequest({
    this.keyword,
    this.limit,
    this.nextPageToken,
    this.prevPageToken,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'keyword',
    required: false,
    includeIfNull: false,
  )
  final String? keyword;

  /// The maximum number of search results to retrieve.
  @Json<PERSON>ey(
    name: r'limit',
    required: false,
    includeIfNull: false,
  )
  final int? limit;

  /// A token to retrieve the next page of search results.
  @Json<PERSON>ey(
    name: r'nextPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? nextPageToken;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'prevPageToken',
    required: false,
    includeIfNull: false,
  )
  final String? prevPageToken;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchUsersRequest &&
          other.keyword == keyword &&
          other.limit == limit &&
          other.nextPageToken == nextPageToken &&
          other.prevPageToken == prevPageToken;

  @override
  int get hashCode =>
      keyword.hashCode +
      limit.hashCode +
      nextPageToken.hashCode +
      prevPageToken.hashCode;

  factory V3SearchUsersRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SearchUsersRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchUsersRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
