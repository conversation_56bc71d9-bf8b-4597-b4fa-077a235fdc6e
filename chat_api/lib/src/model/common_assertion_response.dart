//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'common_assertion_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonAssertionResponse {
  /// Returns a new [CommonAssertionResponse] instance.
  CommonAssertionResponse({
    this.authenticatorData,
    this.clientDataJSON,
    this.signature,
    this.userHandle,
  });

  /// Information about the security key or device used for authentication.
  @J<PERSON><PERSON><PERSON>(
    name: r'authenticatorData',
    required: false,
    includeIfNull: false,
  )
  final String? authenticatorData;

  /// The client data in JSON format.
  @JsonKey(
    name: r'clientDataJSON',
    required: false,
    includeIfNull: false,
  )
  final String? clientDataJSON;

  /// The signature is a cryptographic value created by the authenticator to validate the data's validity.
  @JsonKey(
    name: r'signature',
    required: false,
    includeIfNull: false,
  )
  final String? signature;

  /// The user handle is an identification linked with the authenticating user.
  @JsonKey(
    name: r'userHandle',
    required: false,
    includeIfNull: false,
  )
  final String? userHandle;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonAssertionResponse &&
          other.authenticatorData == authenticatorData &&
          other.clientDataJSON == clientDataJSON &&
          other.signature == signature &&
          other.userHandle == userHandle;

  @override
  int get hashCode =>
      authenticatorData.hashCode +
      clientDataJSON.hashCode +
      signature.hashCode +
      userHandle.hashCode;

  factory CommonAssertionResponse.fromJson(Map<String, dynamic> json) =>
      _$CommonAssertionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CommonAssertionResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
