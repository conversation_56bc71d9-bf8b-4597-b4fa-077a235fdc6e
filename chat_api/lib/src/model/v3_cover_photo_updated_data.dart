//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cover_photo_updated_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CoverPhotoUpdatedData {
  /// Returns a new [V3CoverPhotoUpdatedData] instance.
  V3CoverPhotoUpdatedData({
    this.userId,
    this.cover,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'cover',
    required: false,
    includeIfNull: false,
  )
  final String? cover;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CoverPhotoUpdatedData &&
          other.userId == userId &&
          other.cover == cover;

  @override
  int get hashCode => userId.hashCode + cover.hashCode;

  factory V3CoverPhotoUpdatedData.fromJson(Map<String, dynamic> json) =>
      _$V3CoverPhotoUpdatedDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CoverPhotoUpdatedDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
