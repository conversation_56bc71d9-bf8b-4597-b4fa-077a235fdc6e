//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_security_key.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SecurityKey {
  /// Returns a new [V3SecurityKey] instance.
  V3SecurityKey({
    this.securityKey,
  });

  @JsonKey(
    name: r'securityKey',
    required: false,
    includeIfNull: false,
  )
  final String? securityKey;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SecurityKey && other.securityKey == securityKey;

  @override
  int get hashCode => securityKey.hashCode;

  factory V3SecurityKey.fromJson(Map<String, dynamic> json) =>
      _$V3SecurityKeyFromJson(json);

  Map<String, dynamic> toJson() => _$V3SecurityKeyToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
