//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_message.dart';
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_message_unpinned_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MessageUnpinnedEventData {
  /// Returns a new [V3MessageUnpinnedEventData] instance.
  V3MessageUnpinnedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.channel,
    this.message,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @Json<PERSON>ey(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3Channel? channel;

  @JsonKey(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final V3Message? message;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MessageUnpinnedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.channel == channel &&
          other.message == message;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      channel.hashCode +
      message.hashCode;

  factory V3MessageUnpinnedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MessageUnpinnedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MessageUnpinnedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
