//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_mocked_channel.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MockedChannel {
  /// Returns a new [V3MockedChannel] instance.
  V3MockedChannel({
    this.channelId,
    this.name,
    this.ownerId,
    this.memberIds,
    this.messageIds,
  });

  @<PERSON>son<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @Json<PERSON>ey(
    name: r'ownerId',
    required: false,
    includeIfNull: false,
  )
  final String? ownerId;

  @Json<PERSON><PERSON>(
    name: r'memberIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? memberIds;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'messageIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? messageIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MockedChannel &&
          other.channelId == channelId &&
          other.name == name &&
          other.ownerId == ownerId &&
          other.memberIds == memberIds &&
          other.messageIds == messageIds;

  @override
  int get hashCode =>
      channelId.hashCode +
      name.hashCode +
      ownerId.hashCode +
      memberIds.hashCode +
      messageIds.hashCode;

  factory V3MockedChannel.fromJson(Map<String, dynamic> json) =>
      _$V3MockedChannelFromJson(json);

  Map<String, dynamic> toJson() => _$V3MockedChannelToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
