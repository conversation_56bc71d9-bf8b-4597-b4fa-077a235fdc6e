//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelUpdatedEventData {
  /// Returns a new [V3ChannelUpdatedEventData] instance.
  V3ChannelUpdatedEventData({
    this.channel,
    this.includes,
  });

  @JsonKey(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3Channel? channel;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelUpdatedEventData &&
          other.channel == channel &&
          other.includes == includes;

  @override
  int get hashCode => channel.hashCode + includes.hashCode;

  factory V3ChannelUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
