//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_friend_result.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchFriendResult {
  /// Returns a new [V3SearchFriendResult] instance.
  V3SearchFriendResult({
    this.userId,
    this.displayName,
    this.username,
    this.avatar,
    this.decoratedAvatar,
    this.userBadgeType,
    this.videoAvatar,
  });

  /// The unique identifier for the user.
  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  /// The display name of the user.
  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  /// The username of the user.
  @Json<PERSON>ey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  /// The avatar image associated with the user.
  @JsonKey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @JsonKey(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @JsonKey(
    name: r'userBadgeType',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? userBadgeType;

  @JsonKey(
    name: r'videoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? videoAvatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchFriendResult &&
          other.userId == userId &&
          other.displayName == displayName &&
          other.username == username &&
          other.avatar == avatar &&
          other.decoratedAvatar == decoratedAvatar &&
          other.userBadgeType == userBadgeType &&
          other.videoAvatar == videoAvatar;

  @override
  int get hashCode =>
      userId.hashCode +
      displayName.hashCode +
      username.hashCode +
      avatar.hashCode +
      decoratedAvatar.hashCode +
      userBadgeType.hashCode +
      videoAvatar.hashCode;

  factory V3SearchFriendResult.fromJson(Map<String, dynamic> json) =>
      _$V3SearchFriendResultFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchFriendResultToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
