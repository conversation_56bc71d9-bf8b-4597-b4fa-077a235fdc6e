//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_joined_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberJoinedEventData {
  /// Returns a new [V3MemberJoinedEventData] instance.
  V3MemberJoinedEventData({
    this.workspaceId,
    this.channelId,
    this.joinedUserId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'joinedUserId',
    required: false,
    includeIfNull: false,
  )
  final String? joinedUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberJoinedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.joinedUserId == joinedUserId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + joinedUserId.hashCode;

  factory V3MemberJoinedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MemberJoinedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberJoinedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
