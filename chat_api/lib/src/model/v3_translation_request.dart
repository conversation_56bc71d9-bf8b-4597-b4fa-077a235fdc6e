//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_translation_request_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_translation_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TranslationRequest {
  /// Returns a new [V3TranslationRequest] instance.
  V3TranslationRequest({
    this.requestData,
  });

  @JsonKey(
    name: r'requestData',
    required: false,
    includeIfNull: false,
  )
  final List<V3TranslationRequestData>? requestData;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TranslationRequest && other.requestData == requestData;

  @override
  int get hashCode => requestData.hashCode;

  factory V3TranslationRequest.fromJson(Map<String, dynamic> json) =>
      _$V3TranslationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3TranslationRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
