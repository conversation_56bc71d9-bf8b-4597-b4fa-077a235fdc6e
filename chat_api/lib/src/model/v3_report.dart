//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_pretending_to.dart';
import 'package:chat_api/src/model/v3_report_category.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_report.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Report {
  /// Returns a new [V3Report] instance.
  V3Report({
    this.reportCategory,
    this.pretendingTo,
    this.reportReason,
    this.reportBy,
    this.reportTime,
  });

  @Json<PERSON><PERSON>(
    name: r'reportCategory',
    required: false,
    includeIfNull: false,
  )
  final V3ReportCategory? reportCategory;

  @JsonKey(
    name: r'pretendingTo',
    required: false,
    includeIfNull: false,
  )
  final V3PretendingTo? pretendingTo;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'reportReason',
    required: false,
    includeIfNull: false,
  )
  final String? reportReason;

  @Json<PERSON>ey(
    name: r'reportBy',
    required: false,
    includeIfNull: false,
  )
  final String? reportBy;

  @JsonKey(
    name: r'reportTime',
    required: false,
    includeIfNull: false,
  )
  final String? reportTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Report &&
          other.reportCategory == reportCategory &&
          other.pretendingTo == pretendingTo &&
          other.reportReason == reportReason &&
          other.reportBy == reportBy &&
          other.reportTime == reportTime;

  @override
  int get hashCode =>
      reportCategory.hashCode +
      pretendingTo.hashCode +
      reportReason.hashCode +
      reportBy.hashCode +
      reportTime.hashCode;

  factory V3Report.fromJson(Map<String, dynamic> json) =>
      _$V3ReportFromJson(json);

  Map<String, dynamic> toJson() => _$V3ReportToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
