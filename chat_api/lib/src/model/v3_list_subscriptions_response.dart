//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_status.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_subscriptions_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListSubscriptionsResponse {
  /// Returns a new [V3ListSubscriptionsResponse] instance.
  V3ListSubscriptionsResponse({
    this.ok,
    this.error,
    this.data,
  });

  @Json<PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3ChannelStatus>? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListSubscriptionsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3ListSubscriptionsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListSubscriptionsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListSubscriptionsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
