//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_role.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberRole {
  /// Returns a new [V3MemberRole] instance.
  V3MemberRole({
    this.role,
    this.weight,
  });

  @JsonKey(
    name: r'role',
    required: false,
    includeIfNull: false,
  )
  final String? role;

  @JsonKey(
    name: r'weight',
    required: false,
    includeIfNull: false,
  )
  final int? weight;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberRole && other.role == role && other.weight == weight;

  @override
  int get hashCode => role.hashCode + weight.hashCode;

  factory V3MemberRole.fromJson(Map<String, dynamic> json) =>
      _$V3MemberRoleFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberRoleToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
