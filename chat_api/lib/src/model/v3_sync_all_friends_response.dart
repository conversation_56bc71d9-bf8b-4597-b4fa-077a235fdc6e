//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/sync_all_friends_response_friend_identification.dart';
import 'package:chat_api/src/model/v3_friend_data.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sync_all_friends_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SyncAllFriendsResponse {
  /// Returns a new [V3SyncAllFriendsResponse] instance.
  V3SyncAllFriendsResponse({
    this.data,
    this.friendDeleted,
    this.syncTime,
    this.includes,
  });

  /// List of values for the Friend interface.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3FriendData>? data;

  /// List of friend deleted.
  @JsonKey(
    name: r'friendDeleted',
    required: false,
    includeIfNull: false,
  )
  final List<SyncAllFriendsResponseFriendIdentification>? friendDeleted;

  @JsonKey(
    name: r'syncTime',
    required: false,
    includeIfNull: false,
  )
  final String? syncTime;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SyncAllFriendsResponse &&
          other.data == data &&
          other.friendDeleted == friendDeleted &&
          other.syncTime == syncTime &&
          other.includes == includes;

  @override
  int get hashCode =>
      data.hashCode +
      friendDeleted.hashCode +
      syncTime.hashCode +
      includes.hashCode;

  factory V3SyncAllFriendsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SyncAllFriendsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SyncAllFriendsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
