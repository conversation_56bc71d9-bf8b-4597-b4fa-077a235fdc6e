//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_original_message.dart';
import 'package:chat_api/src/model/v3_report.dart';
import 'package:chat_api/src/model/v3_embed.dart';
import 'package:chat_api/src/model/v3_message_status_enum.dart';
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:chat_api/src/model/v3_media_attachment.dart';
import 'package:chat_api/src/model/v3_reaction_data.dart';
import 'package:chat_api/src/model/v3_message_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_message.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Message {
  /// Returns a new [V3Message] instance.
  V3Message({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.userId,
    this.content,
    this.ref,
    this.messageType,
    this.messageStatus,
    this.originalMessage,
    this.reactions,
    this.mentions,
    this.embed,
    this.attachmentType,
    this.reports,
    this.isThread,
    this.reportCount,
    this.isReported,
    this.attachmentCount,
    this.mediaAttachments,
    this.contentLocale,
    this.contentArguments,
    this.isPinned,
    this.pinTime,
    this.editTime,
    this.createTime,
    this.updateTime,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @JsonKey(
    name: r'messageType',
    required: false,
    includeIfNull: false,
  )
  final V3MessageTypeEnum? messageType;

  @JsonKey(
    name: r'messageStatus',
    required: false,
    includeIfNull: false,
  )
  final V3MessageStatusEnum? messageStatus;

  @JsonKey(
    name: r'originalMessage',
    required: false,
    includeIfNull: false,
  )
  final V3OriginalMessage? originalMessage;

  @JsonKey(
    name: r'reactions',
    required: false,
    includeIfNull: false,
  )
  final Map<String, V3ReactionData>? reactions;

  @JsonKey(
    name: r'mentions',
    required: false,
    includeIfNull: false,
  )
  final List<String>? mentions;

  @JsonKey(
    name: r'embed',
    required: false,
    includeIfNull: false,
  )
  final List<V3Embed>? embed;

  @JsonKey(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @JsonKey(
    name: r'reports',
    required: false,
    includeIfNull: false,
  )
  final List<V3Report>? reports;

  @JsonKey(
    name: r'isThread',
    required: false,
    includeIfNull: false,
  )
  final bool? isThread;

  @JsonKey(
    name: r'reportCount',
    required: false,
    includeIfNull: false,
  )
  final int? reportCount;

  @JsonKey(
    name: r'isReported',
    required: false,
    includeIfNull: false,
  )
  final bool? isReported;

  @JsonKey(
    name: r'attachmentCount',
    required: false,
    includeIfNull: false,
  )
  final int? attachmentCount;

  @JsonKey(
    name: r'mediaAttachments',
    required: false,
    includeIfNull: false,
  )
  final List<V3MediaAttachment>? mediaAttachments;

  @JsonKey(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @JsonKey(
    name: r'contentArguments',
    required: false,
    includeIfNull: false,
  )
  final List<String>? contentArguments;

  @JsonKey(
    name: r'isPinned',
    required: false,
    includeIfNull: false,
  )
  final bool? isPinned;

  @JsonKey(
    name: r'pinTime',
    required: false,
    includeIfNull: false,
  )
  final String? pinTime;

  @JsonKey(
    name: r'editTime',
    required: false,
    includeIfNull: false,
  )
  final String? editTime;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Message &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.userId == userId &&
          other.content == content &&
          other.ref == ref &&
          other.messageType == messageType &&
          other.messageStatus == messageStatus &&
          other.originalMessage == originalMessage &&
          other.reactions == reactions &&
          other.mentions == mentions &&
          other.embed == embed &&
          other.attachmentType == attachmentType &&
          other.reports == reports &&
          other.isThread == isThread &&
          other.reportCount == reportCount &&
          other.isReported == isReported &&
          other.attachmentCount == attachmentCount &&
          other.mediaAttachments == mediaAttachments &&
          other.contentLocale == contentLocale &&
          other.contentArguments == contentArguments &&
          other.isPinned == isPinned &&
          other.pinTime == pinTime &&
          other.editTime == editTime &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      userId.hashCode +
      content.hashCode +
      ref.hashCode +
      messageType.hashCode +
      messageStatus.hashCode +
      originalMessage.hashCode +
      reactions.hashCode +
      mentions.hashCode +
      embed.hashCode +
      attachmentType.hashCode +
      reports.hashCode +
      isThread.hashCode +
      reportCount.hashCode +
      isReported.hashCode +
      attachmentCount.hashCode +
      mediaAttachments.hashCode +
      contentLocale.hashCode +
      contentArguments.hashCode +
      isPinned.hashCode +
      pinTime.hashCode +
      editTime.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3Message.fromJson(Map<String, dynamic> json) =>
      _$V3MessageFromJson(json);

  Map<String, dynamic> toJson() => _$V3MessageToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
