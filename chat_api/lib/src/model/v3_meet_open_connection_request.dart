//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_meet_open_connection_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MeetOpenConnectionRequest {
  /// Returns a new [V3MeetOpenConnectionRequest] instance.
  V3MeetOpenConnectionRequest({
    this.workspaceId,
    this.channelId,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MeetOpenConnectionRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode;

  factory V3MeetOpenConnectionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MeetOpenConnectionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MeetOpenConnectionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
