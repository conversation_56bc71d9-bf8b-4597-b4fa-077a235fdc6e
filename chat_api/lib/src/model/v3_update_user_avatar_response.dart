//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_updated_avatar.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_avatar_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserAvatarResponse {
  /// Returns a new [V3UpdateUserAvatarResponse] instance.
  V3UpdateUserAvatarResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3UpdatedAvatar? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserAvatarResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3UpdateUserAvatarResponse.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserAvatarResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserAvatarResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
