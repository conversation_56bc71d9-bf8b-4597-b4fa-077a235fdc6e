//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_video_avatar_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserVideoAvatarDeletedEventData {
  /// Returns a new [V3UserVideoAvatarDeletedEventData] instance.
  V3UserVideoAvatarDeletedEventData({
    this.actorId,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserVideoAvatarDeletedEventData && other.actorId == actorId;

  @override
  int get hashCode => actorId.hashCode;

  factory V3UserVideoAvatarDeletedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserVideoAvatarDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserVideoAvatarDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
