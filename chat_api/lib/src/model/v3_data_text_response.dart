//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_data_text_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DataTextResponse {
  /// Returns a new [V3DataTextResponse] instance.
  V3DataTextResponse({
    this.originalText,
    this.translateText,
  });

  @JsonKey(
    name: r'originalText',
    required: false,
    includeIfNull: false,
  )
  final String? originalText;

  @JsonKey(
    name: r'translateText',
    required: false,
    includeIfNull: false,
  )
  final String? translateText;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DataTextResponse &&
          other.originalText == originalText &&
          other.translateText == translateText;

  @override
  int get hashCode => originalText.hashCode + translateText.hashCode;

  factory V3DataTextResponse.fromJson(Map<String, dynamic> json) =>
      _$V3DataTextResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3DataTextResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
