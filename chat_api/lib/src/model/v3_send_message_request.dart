//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendMessageRequest {
  /// Returns a new [V3SendMessageRequest] instance.
  V3SendMessageRequest({
    this.workspaceId,
    this.channelId,
    this.content,
    this.ref,
    this.contentLocale,
  });

  @<PERSON>son<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @J<PERSON><PERSON><PERSON>(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendMessageRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.content == content &&
          other.ref == ref &&
          other.contentLocale == contentLocale;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      content.hashCode +
      ref.hashCode +
      contentLocale.hashCode;

  factory V3SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
