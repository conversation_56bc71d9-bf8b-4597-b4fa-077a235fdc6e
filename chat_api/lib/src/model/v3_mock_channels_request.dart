//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_mock_channels_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MockChannelsRequest {
  /// Returns a new [V3MockChannelsRequest] instance.
  V3MockChannelsRequest({
    this.quantity,
    this.prefix,
    this.members,
    this.totalMessages,
    this.typeChannel,
  });

  /// The number of channels you want to create.
  @Json<PERSON>ey(
    name: r'quantity',
    required: false,
    includeIfNull: false,
  )
  final int? quantity;

  @Json<PERSON>ey(
    name: r'prefix',
    required: false,
    includeIfNull: false,
  )
  final String? prefix;

  @Json<PERSON>ey(
    name: r'members',
    required: false,
    includeIfNull: false,
  )
  final List<String>? members;

  @<PERSON>son<PERSON>ey(
    name: r'totalMessages',
    required: false,
    includeIfNull: false,
  )
  final int? totalMessages;

  @<PERSON>son<PERSON>ey(
    name: r'typeChannel',
    required: false,
    includeIfNull: false,
  )
  final int? typeChannel;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MockChannelsRequest &&
          other.quantity == quantity &&
          other.prefix == prefix &&
          other.members == members &&
          other.totalMessages == totalMessages &&
          other.typeChannel == typeChannel;

  @override
  int get hashCode =>
      quantity.hashCode +
      prefix.hashCode +
      members.hashCode +
      totalMessages.hashCode +
      typeChannel.hashCode;

  factory V3MockChannelsRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MockChannelsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MockChannelsRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
