//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_invitation_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendInvitationResponse {
  /// Returns a new [V3SendInvitationResponse] instance.
  V3SendInvitationResponse({
    this.ok,
    this.error,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendInvitationResponse &&
          other.ok == ok &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + error.hashCode;

  factory V3SendInvitationResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SendInvitationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendInvitationResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
