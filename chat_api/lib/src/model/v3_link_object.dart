//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_link_object.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LinkObject {
  /// Returns a new [V3LinkObject] instance.
  V3LinkObject({
    this.attachmentType,
    this.url,
    this.shortUrl,
  });

  @JsonKey(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @JsonKey(
    name: r'url',
    required: false,
    includeIfNull: false,
  )
  final String? url;

  @JsonKey(
    name: r'shortUrl',
    required: false,
    includeIfNull: false,
  )
  final String? shortUrl;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LinkObject &&
          other.attachmentType == attachmentType &&
          other.url == url &&
          other.shortUrl == shortUrl;

  @override
  int get hashCode =>
      attachmentType.hashCode + url.hashCode + shortUrl.hashCode;

  factory V3LinkObject.fromJson(Map<String, dynamic> json) =>
      _$V3LinkObjectFromJson(json);

  Map<String, dynamic> toJson() => _$V3LinkObjectToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
