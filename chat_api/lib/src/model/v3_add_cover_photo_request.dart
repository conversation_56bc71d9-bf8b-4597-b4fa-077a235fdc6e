//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_add_cover_photo_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AddCoverPhotoRequest {
  /// Returns a new [V3AddCoverPhotoRequest] instance.
  V3AddCoverPhotoRequest({
    this.coverPath,
  });

  @JsonKey(
    name: r'coverPath',
    required: false,
    includeIfNull: false,
  )
  final String? coverPath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AddCoverPhotoRequest && other.coverPath == coverPath;

  @override
  int get hashCode => coverPath.hashCode;

  factory V3AddCoverPhotoRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AddCoverPhotoRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AddCoverPhotoRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
