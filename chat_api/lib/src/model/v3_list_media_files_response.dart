//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_media_files_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListMediaFilesResponse {
  /// Returns a new [V3ListMediaFilesResponse] instance.
  V3ListMediaFilesResponse({
    this.ok,
    this.data,
    this.error,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3MediaObject>? data;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListMediaFilesResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + data.hashCode + error.hashCode + includes.hashCode;

  factory V3ListMediaFilesResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListMediaFilesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListMediaFilesResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
