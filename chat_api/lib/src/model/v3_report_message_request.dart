//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_pretending_to.dart';
import 'package:chat_api/src/model/v3_report_category.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_report_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ReportMessageRequest {
  /// Returns a new [V3ReportMessageRequest] instance.
  V3ReportMessageRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.reportCategory,
    this.pretendingTo,
    this.reportReason,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @Json<PERSON>ey(
    name: r'reportCategory',
    required: false,
    includeIfNull: false,
  )
  final V3ReportCategory? reportCategory;

  @JsonKey(
    name: r'pretendingTo',
    required: false,
    includeIfNull: false,
  )
  final V3PretendingTo? pretendingTo;

  @JsonKey(
    name: r'reportReason',
    required: false,
    includeIfNull: false,
  )
  final String? reportReason;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ReportMessageRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.reportCategory == reportCategory &&
          other.pretendingTo == pretendingTo &&
          other.reportReason == reportReason;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      reportCategory.hashCode +
      pretendingTo.hashCode +
      reportReason.hashCode;

  factory V3ReportMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3ReportMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3ReportMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
