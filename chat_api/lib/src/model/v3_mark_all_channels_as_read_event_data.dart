//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_mark_all_channels_as_read_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MarkAllChannelsAsReadEventData {
  /// Returns a new [V3MarkAllChannelsAsReadEventData] instance.
  V3MarkAllChannelsAsReadEventData({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MarkAllChannelsAsReadEventData && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3MarkAllChannelsAsReadEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3MarkAllChannelsAsReadEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3MarkAllChannelsAsReadEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
