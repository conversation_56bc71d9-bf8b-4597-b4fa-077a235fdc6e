//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_attestation_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_recovery_account_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RecoveryAccountRequest {
  /// Returns a new [V3RecoveryAccountRequest] instance.
  V3RecoveryAccountRequest({
    this.reqId,
    this.reqVerifier,
    this.credential,
    this.deviceInfo,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  /// The randomly generated value created by the client.
  @Json<PERSON>ey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'credential',
    required: false,
    includeIfNull: false,
  )
  final CommonAttestationResult? credential;

  @JsonKey(
    name: r'deviceInfo',
    required: false,
    includeIfNull: false,
  )
  final Map<String, String>? deviceInfo;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RecoveryAccountRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.credential == credential &&
          other.deviceInfo == deviceInfo;

  @override
  int get hashCode =>
      reqId.hashCode +
      reqVerifier.hashCode +
      credential.hashCode +
      deviceInfo.hashCode;

  factory V3RecoveryAccountRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RecoveryAccountRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RecoveryAccountRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
