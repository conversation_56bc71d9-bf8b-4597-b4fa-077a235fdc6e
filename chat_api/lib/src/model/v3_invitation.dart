//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_invitation_status_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_invitation.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Invitation {
  /// Returns a new [V3Invitation] instance.
  V3Invitation({
    this.workspaceId,
    this.channelId,
    this.code,
    this.expiresIn,
    this.expireTime,
    this.maxUses,
    this.status,
    this.invitationLink,
    this.createTime,
    this.updateTime,
  });

  @Json<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON>son<PERSON><PERSON>(
    name: r'code',
    required: false,
    includeIfNull: false,
  )
  final String? code;

  @J<PERSON><PERSON><PERSON>(
    name: r'expiresIn',
    required: false,
    includeIfNull: false,
  )
  final int? expiresIn;

  @JsonKey(
    name: r'expireTime',
    required: false,
    includeIfNull: false,
  )
  final String? expireTime;

  @JsonKey(
    name: r'maxUses',
    required: false,
    includeIfNull: false,
  )
  final int? maxUses;

  @JsonKey(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final V3InvitationStatusEnum? status;

  @JsonKey(
    name: r'invitationLink',
    required: false,
    includeIfNull: false,
  )
  final String? invitationLink;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Invitation &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.code == code &&
          other.expiresIn == expiresIn &&
          other.expireTime == expireTime &&
          other.maxUses == maxUses &&
          other.status == status &&
          other.invitationLink == invitationLink &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      code.hashCode +
      expiresIn.hashCode +
      expireTime.hashCode +
      maxUses.hashCode +
      status.hashCode +
      invitationLink.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3Invitation.fromJson(Map<String, dynamic> json) =>
      _$V3InvitationFromJson(json);

  Map<String, dynamic> toJson() => _$V3InvitationToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
