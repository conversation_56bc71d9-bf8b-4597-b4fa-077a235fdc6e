//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/sharedv3_channel_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_dm_channels_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListDMChannelsResponse {
  /// Returns a new [V3ListDMChannelsResponse] instance.
  V3ListDMChannelsResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  /// List of values for the Channel interface.
  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<Sharedv3ChannelData>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListDMChannelsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode +
      error.hashCode +
      data.hashCode +
      paging.hashCode +
      includes.hashCode;

  factory V3ListDMChannelsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListDMChannelsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListDMChannelsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
