//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_upload_chunk.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UploadChunk {
  /// Returns a new [V3UploadChunk] instance.
  V3UploadChunk({
    this.buffer,
    this.offset,
    this.checksumMd5,
  });

  @J<PERSON><PERSON>ey(
    name: r'buffer',
    required: false,
    includeIfNull: false,
  )
  final String? buffer;

  @Json<PERSON>ey(
    name: r'offset',
    required: false,
    includeIfNull: false,
  )
  final int? offset;

  @JsonKey(
    name: r'checksumMd5',
    required: false,
    includeIfNull: false,
  )
  final String? checksumMd5;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UploadChunk &&
          other.buffer == buffer &&
          other.offset == offset &&
          other.checksumMd5 == checksumMd5;

  @override
  int get hashCode => buffer.hashCode + offset.hashCode + checksumMd5.hashCode;

  factory V3UploadChunk.fromJson(Map<String, dynamic> json) =>
      _$V3UploadChunkFromJson(json);

  Map<String, dynamic> toJson() => _$V3UploadChunkToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
