//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_create_avatar_frame_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CreateAvatarFrameRequest {
  /// Returns a new [V3CreateAvatarFrameRequest] instance.
  V3CreateAvatarFrameRequest({
    this.avatarFramePath,
  });

  @JsonKey(
    name: r'avatarFramePath',
    required: false,
    includeIfNull: false,
  )
  final String? avatarFramePath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CreateAvatarFrameRequest &&
          other.avatarFramePath == avatarFramePath;

  @override
  int get hashCode => avatarFramePath.hashCode;

  factory V3CreateAvatarFrameRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CreateAvatarFrameRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CreateAvatarFrameRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
