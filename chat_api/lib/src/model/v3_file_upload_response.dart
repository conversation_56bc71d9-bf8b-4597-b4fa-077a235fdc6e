//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_message_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_file_upload_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3FileUploadResponse {
  /// Returns a new [V3FileUploadResponse] instance.
  V3FileUploadResponse({
    this.ok,
    this.error,
    this.data,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3MessageData? data;

  @Json<PERSON>ey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3FileUploadResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + includes.hashCode;

  factory V3FileUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$V3FileUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3FileUploadResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
