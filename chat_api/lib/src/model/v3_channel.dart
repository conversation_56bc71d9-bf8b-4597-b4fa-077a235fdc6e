//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_direct_message_status_enum.dart';
import 'package:chat_api/src/model/v3_message.dart';
import 'package:chat_api/src/model/v3_premium_settings.dart';
import 'package:chat_api/src/model/v3_privacy_settings.dart';
import 'package:chat_api/src/model/v3_channel_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Channel {
  /// Returns a new [V3Channel] instance.
  V3Channel({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.name,
    this.avatar,
    this.isPrivate,
    this.type,
    this.invitationLink,
    this.privacySettings,
    this.premiumSettings,
    this.originalAvatar,
    this.totalMembers,
    this.dmStatus,
    this.pinnedMessage,
    this.participantIds,
    this.rejectTime,
    this.acceptTime,
    this.createTime,
    this.updateTime,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  /// The channel's creator is identified by the user.
  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @JsonKey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  /// The channel is not yet private.
  @JsonKey(
    name: r'isPrivate',
    required: false,
    includeIfNull: false,
  )
  final bool? isPrivate;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelTypeEnum? type;

  @JsonKey(
    name: r'invitationLink',
    required: false,
    includeIfNull: false,
  )
  final String? invitationLink;

  @JsonKey(
    name: r'privacySettings',
    required: false,
    includeIfNull: false,
  )
  final V3PrivacySettings? privacySettings;

  @JsonKey(
    name: r'premiumSettings',
    required: false,
    includeIfNull: false,
  )
  final V3PremiumSettings? premiumSettings;

  @JsonKey(
    name: r'originalAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? originalAvatar;

  @JsonKey(
    name: r'totalMembers',
    required: false,
    includeIfNull: false,
  )
  final int? totalMembers;

  @JsonKey(
    name: r'dmStatus',
    required: false,
    includeIfNull: false,
  )
  final V3DirectMessageStatusEnum? dmStatus;

  @JsonKey(
    name: r'pinnedMessage',
    required: false,
    includeIfNull: false,
  )
  final V3Message? pinnedMessage;

  @JsonKey(
    name: r'participantIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? participantIds;

  @JsonKey(
    name: r'rejectTime',
    required: false,
    includeIfNull: false,
  )
  final String? rejectTime;

  @JsonKey(
    name: r'acceptTime',
    required: false,
    includeIfNull: false,
  )
  final String? acceptTime;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Channel &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId &&
          other.name == name &&
          other.avatar == avatar &&
          other.isPrivate == isPrivate &&
          other.type == type &&
          other.invitationLink == invitationLink &&
          other.privacySettings == privacySettings &&
          other.premiumSettings == premiumSettings &&
          other.originalAvatar == originalAvatar &&
          other.totalMembers == totalMembers &&
          other.dmStatus == dmStatus &&
          other.pinnedMessage == pinnedMessage &&
          other.participantIds == participantIds &&
          other.rejectTime == rejectTime &&
          other.acceptTime == acceptTime &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      userId.hashCode +
      name.hashCode +
      avatar.hashCode +
      isPrivate.hashCode +
      type.hashCode +
      invitationLink.hashCode +
      privacySettings.hashCode +
      premiumSettings.hashCode +
      originalAvatar.hashCode +
      totalMembers.hashCode +
      dmStatus.hashCode +
      pinnedMessage.hashCode +
      participantIds.hashCode +
      rejectTime.hashCode +
      acceptTime.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3Channel.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
