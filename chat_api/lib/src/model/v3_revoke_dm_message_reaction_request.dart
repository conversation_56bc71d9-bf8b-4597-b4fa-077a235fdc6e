//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_revoke_dm_message_reaction_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RevokeDMMessageReactionRequest {
  /// Returns a new [V3RevokeDMMessageReactionRequest] instance.
  V3RevokeDMMessageReactionRequest({
    this.userId,
    this.messageId,
    this.emoji,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @JsonKey(
    name: r'emoji',
    required: false,
    includeIfNull: false,
  )
  final String? emoji;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RevokeDMMessageReactionRequest &&
          other.userId == userId &&
          other.messageId == messageId &&
          other.emoji == emoji;

  @override
  int get hashCode => userId.hashCode + messageId.hashCode + emoji.hashCode;

  factory V3RevokeDMMessageReactionRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3RevokeDMMessageReactionRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3RevokeDMMessageReactionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
