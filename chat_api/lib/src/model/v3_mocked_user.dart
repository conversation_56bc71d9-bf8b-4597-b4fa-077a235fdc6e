//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_mocked_user.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MockedUser {
  /// Returns a new [V3MockedUser] instance.
  V3MockedUser({
    this.userId,
    this.username,
    this.token,
    this.securityKey,
    this.recoverKey,
    this.badge,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @Json<PERSON>ey(
    name: r'token',
    required: false,
    includeIfNull: false,
  )
  final String? token;

  @J<PERSON><PERSON><PERSON>(
    name: r'securityKey',
    required: false,
    includeIfNull: false,
  )
  final String? securityKey;

  @J<PERSON><PERSON><PERSON>(
    name: r'recoverKey',
    required: false,
    includeIfNull: false,
  )
  final String? recoverKey;

  @JsonKey(
    name: r'badge',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? badge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MockedUser &&
          other.userId == userId &&
          other.username == username &&
          other.token == token &&
          other.securityKey == securityKey &&
          other.recoverKey == recoverKey &&
          other.badge == badge;

  @override
  int get hashCode =>
      userId.hashCode +
      username.hashCode +
      token.hashCode +
      securityKey.hashCode +
      recoverKey.hashCode +
      badge.hashCode;

  factory V3MockedUser.fromJson(Map<String, dynamic> json) =>
      _$V3MockedUserFromJson(json);

  Map<String, dynamic> toJson() => _$V3MockedUserToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
