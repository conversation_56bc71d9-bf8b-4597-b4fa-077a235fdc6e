//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_destination_cloud_event.dart';
import 'package:chat_api/src/model/v3_reaction_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_message_reaction_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserMessageReactionUpdatedEventData {
  /// Returns a new [V3UserMessageReactionUpdatedEventData] instance.
  V3UserMessageReactionUpdatedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.messageId,
    this.emoji,
    this.isReacted,
    this.destination,
    this.reactions,
  });

  @<PERSON>sonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @JsonKey(
    name: r'emoji',
    required: false,
    includeIfNull: false,
  )
  final String? emoji;

  @JsonKey(
    name: r'isReacted',
    required: false,
    includeIfNull: false,
  )
  final bool? isReacted;

  @JsonKey(
    name: r'destination',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelDestinationCloudEvent? destination;

  @JsonKey(
    name: r'reactions',
    required: false,
    includeIfNull: false,
  )
  final Map<String, V3ReactionData>? reactions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserMessageReactionUpdatedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.messageId == messageId &&
          other.emoji == emoji &&
          other.isReacted == isReacted &&
          other.destination == destination &&
          other.reactions == reactions;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      messageId.hashCode +
      emoji.hashCode +
      isReacted.hashCode +
      destination.hashCode +
      reactions.hashCode;

  factory V3UserMessageReactionUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserMessageReactionUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserMessageReactionUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
