//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_invitation_data_channel_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InvitationDataChannelData {
  /// Returns a new [V3InvitationDataChannelData] instance.
  V3InvitationDataChannelData({
    this.workspaceId,
    this.channelId,
    this.name,
    this.avatar,
    this.totalMembers,
    this.members,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @Json<PERSON>ey(
    name: r'totalMembers',
    required: false,
    includeIfNull: false,
  )
  final int? totalMembers;

  /// List of values for the User interface.
  @JsonKey(
    name: r'members',
    required: false,
    includeIfNull: false,
  )
  final List<V3User>? members;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InvitationDataChannelData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.name == name &&
          other.avatar == avatar &&
          other.totalMembers == totalMembers &&
          other.members == members;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      name.hashCode +
      avatar.hashCode +
      totalMembers.hashCode +
      members.hashCode;

  factory V3InvitationDataChannelData.fromJson(Map<String, dynamic> json) =>
      _$V3InvitationDataChannelDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3InvitationDataChannelDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
