//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_scope_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_scope_for_call_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserScopeForCallUpdatedEventData {
  /// Returns a new [V3UserScopeForCallUpdatedEventData] instance.
  V3UserScopeForCallUpdatedEventData({
    this.actorId,
    this.userScope,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'userScope',
    required: false,
    includeIfNull: false,
  )
  final V3UserScopeEnum? userScope;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserScopeForCallUpdatedEventData &&
          other.actorId == actorId &&
          other.userScope == userScope;

  @override
  int get hashCode => actorId.hashCode + userScope.hashCode;

  factory V3UserScopeForCallUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserScopeForCallUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserScopeForCallUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
