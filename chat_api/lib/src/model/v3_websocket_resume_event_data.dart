//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_websocket_resume_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3WebsocketResumeEventData {
  /// Returns a new [V3WebsocketResumeEventData] instance.
  V3WebsocketResumeEventData({
    this.token,
  });

  @JsonKey(
    name: r'token',
    required: false,
    includeIfNull: false,
  )
  final String? token;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3WebsocketResumeEventData && other.token == token;

  @override
  int get hashCode => token.hashCode;

  factory V3WebsocketResumeEventData.fromJson(Map<String, dynamic> json) =>
      _$V3WebsocketResumeEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3WebsocketResumeEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
