//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_member_data.dart';
import 'package:chat_api/src/model/v3_user.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_message_reactions_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListMessageReactionsData {
  /// Returns a new [V3ListMessageReactionsData] instance.
  V3ListMessageReactionsData({
    this.emoji,
    this.users,
    this.isReacted,
    this.total,
    this.members,
  });

  @JsonKey(
    name: r'emoji',
    required: false,
    includeIfNull: false,
  )
  final String? emoji;

  @JsonKey(
    name: r'users',
    required: false,
    includeIfNull: false,
  )
  final List<V3User>? users;

  @<PERSON>son<PERSON><PERSON>(
    name: r'isReacted',
    required: false,
    includeIfNull: false,
  )
  final bool? isReacted;

  @JsonKey(
    name: r'total',
    required: false,
    includeIfNull: false,
  )
  final int? total;

  @JsonKey(
    name: r'members',
    required: false,
    includeIfNull: false,
  )
  final List<V3MemberData>? members;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListMessageReactionsData &&
          other.emoji == emoji &&
          other.users == users &&
          other.isReacted == isReacted &&
          other.total == total &&
          other.members == members;

  @override
  int get hashCode =>
      emoji.hashCode +
      users.hashCode +
      isReacted.hashCode +
      total.hashCode +
      members.hashCode;

  factory V3ListMessageReactionsData.fromJson(Map<String, dynamic> json) =>
      _$V3ListMessageReactionsDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListMessageReactionsDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
