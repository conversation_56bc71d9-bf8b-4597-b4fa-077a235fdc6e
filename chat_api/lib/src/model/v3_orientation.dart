//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

///  - ORIENTATION_DEFAULT: The default orientation.  - ORIENTATION_PORTRAIT: Portrait orientation.  - ORIENTATION_LANDSCAPE: Landscape orientation.
enum V3Orientation {
  ///  - ORIENTATION_DEFAULT: The default orientation.  - ORIENTATION_PORTRAIT: Portrait orientation.  - ORIENTATION_LANDSCAPE: Landscape orientation.
  @JsonValue(0)
  ORIENTATION_DEFAULT('0'),

  ///  - ORIENTATION_DEFAULT: The default orientation.  - ORIENTATION_PORTRAIT: Portrait orientation.  - ORIENTATION_LANDSCAPE: Landscape orientation.
  @JsonValue(1)
  ORIENTATION_PORTRAIT('1'),

  ///  - ORIENTATION_DEFAULT: The default orientation.  - ORIENTATION_PORTRAIT: Portrait orientation.  - ORIENTATION_LANDSCAPE: Landscape orientation.
  @JsonValue(2)
  ORIENTATION_LANDSCAPE('2');

  const V3Orientation(this.value);

  final String value;

  @override
  String toString() => value;
}
