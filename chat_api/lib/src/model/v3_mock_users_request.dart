//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_mock_users_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MockUsersRequest {
  /// Returns a new [V3MockUsersRequest] instance.
  V3MockUsersRequest({
    this.quantity,
    this.prefix,
    this.badge,
  });

  /// The number of accounts you want to create.
  @JsonKey(
    name: r'quantity',
    required: false,
    includeIfNull: false,
  )
  final int? quantity;

  @JsonKey(
    name: r'prefix',
    required: false,
    includeIfNull: false,
  )
  final String? prefix;

  @JsonKey(
    name: r'badge',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? badge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MockUsersRequest &&
          other.quantity == quantity &&
          other.prefix == prefix &&
          other.badge == badge;

  @override
  int get hashCode => quantity.hashCode + prefix.hashCode + badge.hashCode;

  factory V3MockUsersRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MockUsersRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MockUsersRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
