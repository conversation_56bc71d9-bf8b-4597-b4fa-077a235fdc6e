//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_connect_params.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_open_connection_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3OpenConnectionResponse {
  /// Returns a new [V3OpenConnectionResponse] instance.
  V3OpenConnectionResponse({
    this.ok,
    this.error,
    this.connectParams,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'connectParams',
    required: false,
    includeIfNull: false,
  )
  final V3ConnectParams? connectParams;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3OpenConnectionResponse &&
          other.ok == ok &&
          other.error == error &&
          other.connectParams == connectParams;

  @override
  int get hashCode => ok.hashCode + error.hashCode + connectParams.hashCode;

  factory V3OpenConnectionResponse.fromJson(Map<String, dynamic> json) =>
      _$V3OpenConnectionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3OpenConnectionResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
