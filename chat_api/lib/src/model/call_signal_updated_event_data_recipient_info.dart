//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'call_signal_updated_event_data_recipient_info.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CallSignalUpdatedEventDataRecipientInfo {
  /// Returns a new [CallSignalUpdatedEventDataRecipientInfo] instance.
  CallSignalUpdatedEventDataRecipientInfo({
    this.userId,
    this.deviceId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallSignalUpdatedEventDataRecipientInfo &&
          other.userId == userId &&
          other.deviceId == deviceId;

  @override
  int get hashCode => userId.hashCode + deviceId.hashCode;

  factory CallSignalUpdatedEventDataRecipientInfo.fromJson(
          Map<String, dynamic> json) =>
      _$CallSignalUpdatedEventDataRecipientInfoFromJson(json);

  Map<String, dynamic> toJson() =>
      _$CallSignalUpdatedEventDataRecipientInfoToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
