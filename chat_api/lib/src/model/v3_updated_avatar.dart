//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_updated_avatar.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdatedAvatar {
  /// Returns a new [V3UpdatedAvatar] instance.
  V3UpdatedAvatar({
    this.avatar,
  });

  @JsonKey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdatedAvatar && other.avatar == avatar;

  @override
  int get hashCode => avatar.hashCode;

  factory V3UpdatedAvatar.fromJson(Map<String, dynamic> json) =>
      _$V3UpdatedAvatarFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdatedAvatarToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
