//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_contact.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sync_contacts_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SyncContactsRequest {
  /// Returns a new [V3SyncContactsRequest] instance.
  V3SyncContactsRequest({
    this.contacts,
  });

  @JsonKey(
    name: r'contacts',
    required: false,
    includeIfNull: false,
  )
  final List<V3Contact>? contacts;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SyncContactsRequest && other.contacts == contacts;

  @override
  int get hashCode => contacts.hashCode;

  factory V3SyncContactsRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SyncContactsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SyncContactsRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
