//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_call_type_enum.dart';
import 'package:chat_api/src/model/v3_call_state_enum.dart';
import 'package:chat_api/src/model/v3_participant.dart';
import 'package:chat_api/src/model/v3_call_ended_reason_enum.dart';
import 'package:chat_api/src/model/v3_ice_connection_state_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_call_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CallData {
  /// Returns a new [V3CallData] instance.
  V3CallData({
    this.callId,
    this.state,
    this.type,
    this.endedReason,
    this.createTime,
    this.deadline,
    this.caller,
    this.callee,
    this.participants,
    this.iceConnectionState,
    this.ringbackToneUrl,
  });

  @JsonKey(
    name: r'callId',
    required: false,
    includeIfNull: false,
  )
  final String? callId;

  @JsonKey(
    name: r'state',
    required: false,
    includeIfNull: false,
  )
  final V3CallStateEnum? state;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final V3CallTypeEnum? type;

  @JsonKey(
    name: r'endedReason',
    required: false,
    includeIfNull: false,
  )
  final V3CallEndedReasonEnum? endedReason;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'deadline',
    required: false,
    includeIfNull: false,
  )
  final String? deadline;

  @JsonKey(
    name: r'caller',
    required: false,
    includeIfNull: false,
  )
  final V3Participant? caller;

  @JsonKey(
    name: r'callee',
    required: false,
    includeIfNull: false,
  )
  final V3Participant? callee;

  @JsonKey(
    name: r'participants',
    required: false,
    includeIfNull: false,
  )
  final List<V3Participant>? participants;

  @JsonKey(
    name: r'iceConnectionState',
    required: false,
    includeIfNull: false,
  )
  final V3ICEConnectionStateEnum? iceConnectionState;

  @JsonKey(
    name: r'ringbackToneUrl',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneUrl;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CallData &&
          other.callId == callId &&
          other.state == state &&
          other.type == type &&
          other.endedReason == endedReason &&
          other.createTime == createTime &&
          other.deadline == deadline &&
          other.caller == caller &&
          other.callee == callee &&
          other.participants == participants &&
          other.iceConnectionState == iceConnectionState &&
          other.ringbackToneUrl == ringbackToneUrl;

  @override
  int get hashCode =>
      callId.hashCode +
      state.hashCode +
      type.hashCode +
      endedReason.hashCode +
      createTime.hashCode +
      deadline.hashCode +
      caller.hashCode +
      callee.hashCode +
      participants.hashCode +
      iceConnectionState.hashCode +
      ringbackToneUrl.hashCode;

  factory V3CallData.fromJson(Map<String, dynamic> json) =>
      _$V3CallDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CallDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
