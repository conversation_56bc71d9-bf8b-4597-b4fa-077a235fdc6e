//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ice_candidate.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3IceCandidate {
  /// Returns a new [V3IceCandidate] instance.
  V3IceCandidate({
    this.deviceId,
    this.rtcIceCandidate,
    this.deviceType,
  });

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @Json<PERSON>ey(
    name: r'rtcIceCandidate',
    required: false,
    includeIfNull: false,
  )
  final String? rtcIceCandidate;

  @Json<PERSON>ey(
    name: r'deviceType',
    required: false,
    includeIfNull: false,
  )
  final String? deviceType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3IceCandidate &&
          other.deviceId == deviceId &&
          other.rtcIceCandidate == rtcIceCandidate &&
          other.deviceType == deviceType;

  @override
  int get hashCode =>
      deviceId.hashCode + rtcIceCandidate.hashCode + deviceType.hashCode;

  factory V3IceCandidate.fromJson(Map<String, dynamic> json) =>
      _$V3IceCandidateFromJson(json);

  Map<String, dynamic> toJson() => _$V3IceCandidateToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
