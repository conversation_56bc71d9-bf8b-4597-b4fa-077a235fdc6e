//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_verify_qr_auth_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3VerifyQRAuthRequest {
  /// Returns a new [V3VerifyQRAuthRequest] instance.
  V3VerifyQRAuthRequest({
    this.reqId,
    this.qrAuthCode,
    this.reqChallenge,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @Json<PERSON>ey(
    name: r'qrAuthCode',
    required: false,
    includeIfNull: false,
  )
  final String? qrAuthCode;

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3VerifyQRAuthRequest &&
          other.reqId == reqId &&
          other.qrAuthCode == qrAuthCode &&
          other.reqChallenge == reqChallenge;

  @override
  int get hashCode =>
      reqId.hashCode + qrAuthCode.hashCode + reqChallenge.hashCode;

  factory V3VerifyQRAuthRequest.fromJson(Map<String, dynamic> json) =>
      _$V3VerifyQRAuthRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3VerifyQRAuthRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
