//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - MESSAGE_TYPE_ENUM_DEFAULT: Default is message of user  - MESSAGE_TYPE_ENUM_AUDIT_LOG: Message from system
enum V3MessageTypeEnum {
  /// - MESSAGE_TYPE_ENUM_DEFAULT: Default is message of user  - MESSAGE_TYPE_ENUM_AUDIT_LOG: Message from system
  @JsonValue(0)
  MESSAGE_TYPE_ENUM_DEFAULT('0'),

  /// - MESSAGE_TYPE_ENUM_DEFAULT: Default is message of user  - MESSAGE_TYPE_ENUM_AUDIT_LOG: Message from system
  @JsonValue(1)
  MESSAGE_TYPE_ENUM_AUDIT_LOG('1');

  const V3MessageTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
