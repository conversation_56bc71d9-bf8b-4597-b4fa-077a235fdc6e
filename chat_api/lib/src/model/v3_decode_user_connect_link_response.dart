//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_decode_user_connect_link_response_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_decode_user_connect_link_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DecodeUserConnectLinkResponse {
  /// Returns a new [V3DecodeUserConnectLinkResponse] instance.
  V3DecodeUserConnectLinkResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3DecodeUserConnectLinkResponseData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DecodeUserConnectLinkResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3DecodeUserConnectLinkResponse.fromJson(Map<String, dynamic> json) =>
      _$V3DecodeUserConnectLinkResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3DecodeUserConnectLinkResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
