//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_audio_metadata.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AudioMetadata {
  /// Returns a new [V3AudioMetadata] instance.
  V3AudioMetadata({
    this.samples,
  });

  @JsonKey(
    name: r'samples',
    required: false,
    includeIfNull: false,
  )
  final List<int>? samples;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AudioMetadata && other.samples == samples;

  @override
  int get hashCode => samples.hashCode;

  factory V3AudioMetadata.fromJson(Map<String, dynamic> json) =>
      _$V3AudioMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$V3AudioMetadataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
