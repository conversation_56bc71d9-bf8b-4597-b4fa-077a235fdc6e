//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_audio_metadata.dart';
import 'package:chat_api/src/model/v3_file_metadata.dart';
import 'package:chat_api/src/model/v3_upload_request.dart';
import 'package:chat_api/src/model/v3_upload_metadata.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_file_upload_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3FileUploadRequest {
  /// Returns a new [V3FileUploadRequest] instance.
  V3FileUploadRequest({
    this.uploadRequest,
    this.fileBuffer,
    this.uploadMetadata,
    this.fileMetadata,
    this.audioMetadata,
  });

  @JsonKey(
    name: r'uploadRequest',
    required: false,
    includeIfNull: false,
  )
  final V3UploadRequest? uploadRequest;

  @JsonKey(
    name: r'fileBuffer',
    required: false,
    includeIfNull: false,
  )
  final String? fileBuffer;

  @JsonKey(
    name: r'uploadMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3UploadMetadata? uploadMetadata;

  @JsonKey(
    name: r'fileMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3FileMetadata? fileMetadata;

  @JsonKey(
    name: r'audioMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3AudioMetadata? audioMetadata;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3FileUploadRequest &&
          other.uploadRequest == uploadRequest &&
          other.fileBuffer == fileBuffer &&
          other.uploadMetadata == uploadMetadata &&
          other.fileMetadata == fileMetadata &&
          other.audioMetadata == audioMetadata;

  @override
  int get hashCode =>
      uploadRequest.hashCode +
      fileBuffer.hashCode +
      uploadMetadata.hashCode +
      fileMetadata.hashCode +
      audioMetadata.hashCode;

  factory V3FileUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$V3FileUploadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3FileUploadRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
