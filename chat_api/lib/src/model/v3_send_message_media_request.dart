//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_message_media_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendMessageMediaRequest {
  /// Returns a new [V3SendMessageMediaRequest] instance.
  V3SendMessageMediaRequest({
    this.workspaceId,
    this.channelId,
    this.attachmentType,
    this.mediaObjects,
    this.ref,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @J<PERSON><PERSON><PERSON>(
    name: r'mediaObjects',
    required: false,
    includeIfNull: false,
  )
  final List<V3MediaObject>? mediaObjects;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendMessageMediaRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.attachmentType == attachmentType &&
          other.mediaObjects == mediaObjects &&
          other.ref == ref;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      attachmentType.hashCode +
      mediaObjects.hashCode +
      ref.hashCode;

  factory V3SendMessageMediaRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendMessageMediaRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendMessageMediaRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
