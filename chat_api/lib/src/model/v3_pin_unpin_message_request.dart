//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_pin_unpin_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PinUnpinMessageRequest {
  /// Returns a new [V3PinUnpinMessageRequest] instance.
  V3PinUnpinMessageRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.status,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @J<PERSON><PERSON><PERSON>(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final bool? status;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PinUnpinMessageRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.status == status;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      status.hashCode;

  factory V3PinUnpinMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3PinUnpinMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3PinUnpinMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
