//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_delete_user_visited_profile_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeleteUserVisitedProfileEventData {
  /// Returns a new [V3DeleteUserVisitedProfileEventData] instance.
  V3DeleteUserVisitedProfileEventData({
    this.actorId,
    this.userId,
    this.createTime,
    this.updateTime,
  });

  @Json<PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON>ey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeleteUserVisitedProfileEventData &&
          other.actorId == actorId &&
          other.userId == userId &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      actorId.hashCode +
      userId.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3DeleteUserVisitedProfileEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3DeleteUserVisitedProfileEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3DeleteUserVisitedProfileEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
