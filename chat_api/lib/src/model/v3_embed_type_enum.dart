//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3EmbedTypeEnum {
  @JsonValue(0)
  EMBED_TYPE_ENUM_UNSPECIFIED('0'),
  @JsonValue(1)
  EMBED_TYPE_ENUM_PHOTO('1'),
  @JsonValue(2)
  EMBED_TYPE_ENUM_VIDEO('2'),
  @JsonValue(3)
  EMBED_TYPE_ENUM_LINK('3'),
  @JsonValue(4)
  EMBED_TYPE_ENUM_INVITATION('4'),
  @JsonValue(5)
  EMBED_TYPE_ENUM_OTHER('5'),
  @JsonValue(6)
  EMBED_TYPE_ENUM_LOCATION('6');

  const V3EmbedTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
