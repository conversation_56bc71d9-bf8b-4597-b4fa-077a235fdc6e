//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_connect_params.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ConnectParams {
  /// Returns a new [V3ConnectParams] instance.
  V3ConnectParams({
    this.url,
  });

  @JsonKey(
    name: r'url',
    required: false,
    includeIfNull: false,
  )
  final String? url;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is V3ConnectParams && other.url == url;

  @override
  int get hashCode => url.hashCode;

  factory V3ConnectParams.fromJson(Map<String, dynamic> json) =>
      _$V3ConnectParamsFromJson(json);

  Map<String, dynamic> toJson() => _$V3ConnectParamsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
