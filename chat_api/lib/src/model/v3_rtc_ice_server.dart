//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_rtc_ice_server.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RTCIceServer {
  /// Returns a new [V3RTCIceServer] instance.
  V3RTCIceServer({
    this.urls,
    this.username,
    this.credential,
    this.credentialType,
  });

  /// The list urls, each specifying a URL which can be used to connect to the server.
  @JsonKey(
    name: r'urls',
    required: false,
    includeIfNull: false,
  )
  final List<String>? urls;

  /// The username If the RTCIceServer is a TURN server, then this is the username to use during the authentication process.
  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  /// The credential to use when logging into the server. This is only used if the RTCIceServer represents a TURN server.
  @JsonKey(
    name: r'credential',
    required: false,
    includeIfNull: false,
  )
  final String? credential;

  /// This attribute specifies what kind of credential is to be used when connecting. The default is password.
  @JsonKey(
    name: r'credentialType',
    required: false,
    includeIfNull: false,
  )
  final String? credentialType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RTCIceServer &&
          other.urls == urls &&
          other.username == username &&
          other.credential == credential &&
          other.credentialType == credentialType;

  @override
  int get hashCode =>
      urls.hashCode +
      username.hashCode +
      credential.hashCode +
      credentialType.hashCode;

  factory V3RTCIceServer.fromJson(Map<String, dynamic> json) =>
      _$V3RTCIceServerFromJson(json);

  Map<String, dynamic> toJson() => _$V3RTCIceServerToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
