//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_cover_photo_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateCoverPhotoRequest {
  /// Returns a new [V3UpdateCoverPhotoRequest] instance.
  V3UpdateCoverPhotoRequest({
    this.coverPath,
  });

  @JsonKey(
    name: r'coverPath',
    required: false,
    includeIfNull: false,
  )
  final String? coverPath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateCoverPhotoRequest && other.coverPath == coverPath;

  @override
  int get hashCode => coverPath.hashCode;

  factory V3UpdateCoverPhotoRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateCoverPhotoRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateCoverPhotoRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
