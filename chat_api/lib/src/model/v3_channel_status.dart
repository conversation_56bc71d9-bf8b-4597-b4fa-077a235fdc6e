//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_status.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelStatus {
  /// Returns a new [V3ChannelStatus] instance.
  V3ChannelStatus({
    this.channelId,
    this.status,
  });

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final int? status;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelStatus &&
          other.channelId == channelId &&
          other.status == status;

  @override
  int get hashCode => channelId.hashCode + status.hashCode;

  factory V3ChannelStatus.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelStatusFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelStatusToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
