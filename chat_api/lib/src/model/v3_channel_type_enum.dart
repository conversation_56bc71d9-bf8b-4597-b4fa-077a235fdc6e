//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - CHANNEL_TYPE_ENUM_DM: DM: Direct message 1-1  - CHANNEL_TYPE_ENUM_CHANNEL: CHANNEL: A group can send message to multiple user 1-n  - CHANNEL_TYPE_ENUM_BROADCAST: BROADCAST: A broadcast channel consists of a single sender and multiple receivers
enum V3ChannelTypeEnum {
  /// - CHANNEL_TYPE_ENUM_DM: DM: Direct message 1-1  - CHANNEL_TYPE_ENUM_CHANNEL: CHANNEL: A group can send message to multiple user 1-n  - CHANNEL_TYPE_ENUM_BROADCAST: BROADCAST: A broadcast channel consists of a single sender and multiple receivers
  @JsonValue(0)
  CHANNEL_TYPE_ENUM_DM('0'),

  /// - CHANNEL_TYPE_ENUM_DM: DM: Direct message 1-1  - CHANNEL_TYPE_ENUM_CHANNEL: CHANNEL: A group can send message to multiple user 1-n  - CHANNEL_TYPE_ENUM_BROADCAST: BROADCAST: A broadcast channel consists of a single sender and multiple receivers
  @JsonValue(1)
  CHANNEL_TYPE_ENUM_CHANNEL('1'),

  /// - CHANNEL_TYPE_ENUM_DM: DM: Direct message 1-1  - CHANNEL_TYPE_ENUM_CHANNEL: CHANNEL: A group can send message to multiple user 1-n  - CHANNEL_TYPE_ENUM_BROADCAST: BROADCAST: A broadcast channel consists of a single sender and multiple receivers
  @JsonValue(2)
  CHANNEL_TYPE_ENUM_BROADCAST('2');

  const V3ChannelTypeEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
