//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_avatar_frame_created_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AvatarFrameCreatedEventData {
  /// Returns a new [V3AvatarFrameCreatedEventData] instance.
  V3AvatarFrameCreatedEventData({
    this.actorId,
    this.avatarFrame,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'avatarFrame',
    required: false,
    includeIfNull: false,
  )
  final String? avatarFrame;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AvatarFrameCreatedEventData &&
          other.actorId == actorId &&
          other.avatarFrame == avatarFrame;

  @override
  int get hashCode => actorId.hashCode + avatarFrame.hashCode;

  factory V3AvatarFrameCreatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3AvatarFrameCreatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3AvatarFrameCreatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
