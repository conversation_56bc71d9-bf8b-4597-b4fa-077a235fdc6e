//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_suggest_friend.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_suggestions.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Suggestions {
  /// Returns a new [V3Suggestions] instance.
  V3Suggestions({
    this.phones,
    this.channels,
    this.friends,
  });

  @JsonKey(
    name: r'phones',
    required: false,
    includeIfNull: false,
  )
  final List<V3SuggestFriend>? phones;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channels',
    required: false,
    includeIfNull: false,
  )
  final List<V3SuggestFriend>? channels;

  @JsonKey(
    name: r'friends',
    required: false,
    includeIfNull: false,
  )
  final List<V3SuggestFriend>? friends;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Suggestions &&
          other.phones == phones &&
          other.channels == channels &&
          other.friends == friends;

  @override
  int get hashCode => phones.hashCode + channels.hashCode + friends.hashCode;

  factory V3Suggestions.fromJson(Map<String, dynamic> json) =>
      _$V3SuggestionsFromJson(json);

  Map<String, dynamic> toJson() => _$V3SuggestionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
