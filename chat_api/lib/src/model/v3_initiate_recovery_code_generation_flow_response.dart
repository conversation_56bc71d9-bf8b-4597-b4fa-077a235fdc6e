//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_recovery_code_confirmation_payload.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_recovery_code_generation_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateRecoveryCodeGenerationFlowResponse {
  /// Returns a new [V3InitiateRecoveryCodeGenerationFlowResponse] instance.
  V3InitiateRecoveryCodeGenerationFlowResponse({
    this.ok,
    this.error,
    this.confirmationPayload,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'confirmationPayload',
    required: false,
    includeIfNull: false,
  )
  final V3RecoveryCodeConfirmationPayload? confirmationPayload;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateRecoveryCodeGenerationFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.confirmationPayload == confirmationPayload;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + confirmationPayload.hashCode;

  factory V3InitiateRecoveryCodeGenerationFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateRecoveryCodeGenerationFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateRecoveryCodeGenerationFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
