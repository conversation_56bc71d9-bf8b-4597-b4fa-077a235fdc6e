//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/privacy_settings_restrict_saving_content.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_privacy_settings.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PrivacySettings {
  /// Returns a new [V3PrivacySettings] instance.
  V3PrivacySettings({
    this.restrictSavingContent,
  });

  @JsonKey(
    name: r'restrictSavingContent',
    required: false,
    includeIfNull: false,
  )
  final PrivacySettingsRestrictSavingContent? restrictSavingContent;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PrivacySettings &&
          other.restrictSavingContent == restrictSavingContent;

  @override
  int get hashCode => restrictSavingContent.hashCode;

  factory V3PrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$V3PrivacySettingsFromJson(json);

  Map<String, dynamic> toJson() => _$V3PrivacySettingsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
