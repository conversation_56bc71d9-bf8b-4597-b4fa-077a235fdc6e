//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_translation_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TranslationResponseData {
  /// Returns a new [V3TranslationResponseData] instance.
  V3TranslationResponseData({
    this.translatedText,
    this.targetLanguage,
    this.ref,
  });

  @JsonKey(
    name: r'translatedText',
    required: false,
    includeIfNull: false,
  )
  final String? translatedText;

  @Json<PERSON>ey(
    name: r'targetLanguage',
    required: false,
    includeIfNull: false,
  )
  final String? targetLanguage;

  @Json<PERSON>ey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TranslationResponseData &&
          other.translatedText == translatedText &&
          other.targetLanguage == targetLanguage &&
          other.ref == ref;

  @override
  int get hashCode =>
      translatedText.hashCode + targetLanguage.hashCode + ref.hashCode;

  factory V3TranslationResponseData.fromJson(Map<String, dynamic> json) =>
      _$V3TranslationResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3TranslationResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
