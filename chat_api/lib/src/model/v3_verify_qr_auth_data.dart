//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_public_key_credential_request_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_verify_qr_auth_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3VerifyQRAuthData {
  /// Returns a new [V3VerifyQRAuthData] instance.
  V3VerifyQRAuthData({
    this.reqId,
    this.credentialRequestOptions,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'credentialRequestOptions',
    required: false,
    includeIfNull: false,
  )
  final CommonPublicKeyCredentialRequestOptions? credentialRequestOptions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3VerifyQRAuthData &&
          other.reqId == reqId &&
          other.credentialRequestOptions == credentialRequestOptions;

  @override
  int get hashCode => reqId.hashCode + credentialRequestOptions.hashCode;

  factory V3VerifyQRAuthData.fromJson(Map<String, dynamic> json) =>
      _$V3VerifyQRAuthDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3VerifyQRAuthDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
