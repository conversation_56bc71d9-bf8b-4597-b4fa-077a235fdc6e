//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_call_log_sync_data.dart';
import 'package:chat_api/src/model/v3_channel_sync_data.dart';
import 'package:chat_api/src/model/v3_user_sync_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_private_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PrivateData {
  /// Returns a new [V3PrivateData] instance.
  V3PrivateData({
    this.channels,
    this.users,
    this.callLogs,
    this.updateTime,
    this.createTime,
  });

  @JsonKey(
    name: r'channels',
    required: false,
    includeIfNull: false,
  )
  final List<V3ChannelSyncData>? channels;

  @JsonKey(
    name: r'users',
    required: false,
    includeIfNull: false,
  )
  final List<V3UserSyncData>? users;

  @Json<PERSON>ey(
    name: r'callLogs',
    required: false,
    includeIfNull: false,
  )
  final List<V3CallLogSyncData>? callLogs;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PrivateData &&
          other.channels == channels &&
          other.users == users &&
          other.callLogs == callLogs &&
          other.updateTime == updateTime &&
          other.createTime == createTime;

  @override
  int get hashCode =>
      channels.hashCode +
      users.hashCode +
      callLogs.hashCode +
      updateTime.hashCode +
      createTime.hashCode;

  factory V3PrivateData.fromJson(Map<String, dynamic> json) =>
      _$V3PrivateDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3PrivateDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
