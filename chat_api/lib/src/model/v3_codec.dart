//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_codec.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Codec {
  /// Returns a new [V3Codec] instance.
  V3Codec({
    this.mime,
    this.fmtpLine,
  });

  @JsonKey(
    name: r'mime',
    required: false,
    includeIfNull: false,
  )
  final String? mime;

  @JsonKey(
    name: r'fmtpLine',
    required: false,
    includeIfNull: false,
  )
  final String? fmtpLine;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Codec && other.mime == mime && other.fmtpLine == fmtpLine;

  @override
  int get hashCode => mime.hashCode + fmtpLine.hashCode;

  factory V3Codec.fromJson(Map<String, dynamic> json) =>
      _$V3CodecFromJson(json);

  Map<String, dynamic> toJson() => _$V3CodecToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
