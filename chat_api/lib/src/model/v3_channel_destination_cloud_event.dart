//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_direct_message_status_enum.dart';
import 'package:chat_api/src/model/v3_channel_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_destination_cloud_event.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelDestinationCloudEvent {
  /// Returns a new [V3ChannelDestinationCloudEvent] instance.
  V3ChannelDestinationCloudEvent({
    this.workspaceId,
    this.channelId,
    this.channelType,
    this.recipientId,
    this.dmId,
    this.dmStatus,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelType',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelTypeEnum? channelType;

  @JsonKey(
    name: r'recipientId',
    required: false,
    includeIfNull: false,
  )
  final String? recipientId;

  @JsonKey(
    name: r'dmId',
    required: false,
    includeIfNull: false,
  )
  final String? dmId;

  @JsonKey(
    name: r'dmStatus',
    required: false,
    includeIfNull: false,
  )
  final V3DirectMessageStatusEnum? dmStatus;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelDestinationCloudEvent &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.channelType == channelType &&
          other.recipientId == recipientId &&
          other.dmId == dmId &&
          other.dmStatus == dmStatus;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      channelType.hashCode +
      recipientId.hashCode +
      dmId.hashCode +
      dmStatus.hashCode;

  factory V3ChannelDestinationCloudEvent.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelDestinationCloudEventFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelDestinationCloudEventToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
