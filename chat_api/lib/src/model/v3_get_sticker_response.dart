//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_sticker.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_sticker_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetStickerResponse {
  /// Returns a new [V3GetStickerResponse] instance.
  V3GetStickerResponse({
    this.ok,
    this.data,
    this.error,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3Sticker? data;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetStickerResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3GetStickerResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetStickerResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetStickerResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
