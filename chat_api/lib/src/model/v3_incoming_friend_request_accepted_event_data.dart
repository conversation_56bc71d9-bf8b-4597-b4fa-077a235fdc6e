//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_friend.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_incoming_friend_request_accepted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3IncomingFriendRequestAcceptedEventData {
  /// Returns a new [V3IncomingFriendRequestAcceptedEventData] instance.
  V3IncomingFriendRequestAcceptedEventData({
    this.friendRequest,
    this.includes,
  });

  @JsonKey(
    name: r'friendRequest',
    required: false,
    includeIfNull: false,
  )
  final V3Friend? friendRequest;

  @J<PERSON><PERSON><PERSON>(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3IncomingFriendRequestAcceptedEventData &&
          other.friendRequest == friendRequest &&
          other.includes == includes;

  @override
  int get hashCode => friendRequest.hashCode + includes.hashCode;

  factory V3IncomingFriendRequestAcceptedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3IncomingFriendRequestAcceptedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3IncomingFriendRequestAcceptedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
