//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_accept_invitation_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AcceptInvitationRequest {
  /// Returns a new [V3AcceptInvitationRequest] instance.
  V3AcceptInvitationRequest({
    this.invitationLink,
  });

  @JsonKey(
    name: r'invitationLink',
    required: false,
    includeIfNull: false,
  )
  final String? invitationLink;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AcceptInvitationRequest &&
          other.invitationLink == invitationLink;

  @override
  int get hashCode => invitationLink.hashCode;

  factory V3AcceptInvitationRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AcceptInvitationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AcceptInvitationRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
