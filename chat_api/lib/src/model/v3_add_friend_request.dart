//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_add_friend_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AddFriendRequest {
  /// Returns a new [V3AddFriendRequest] instance.
  V3AddFriendRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AddFriendRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3AddFriendRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AddFriendRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AddFriendRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
