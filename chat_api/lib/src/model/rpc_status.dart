//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/protobuf_any.dart';
import 'package:json_annotation/json_annotation.dart';

part 'rpc_status.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class RpcStatus {
  /// Returns a new [RpcStatus] instance.
  RpcStatus({
    this.code,
    this.message,
    this.details,
  });

  @JsonKey(
    name: r'code',
    required: false,
    includeIfNull: false,
  )
  final int? code;

  @JsonKey(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final String? message;

  @JsonKey(
    name: r'details',
    required: false,
    includeIfNull: false,
  )
  final List<ProtobufAny>? details;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RpcStatus &&
          other.code == code &&
          other.message == message &&
          other.details == details;

  @override
  int get hashCode => code.hashCode + message.hashCode + details.hashCode;

  factory RpcStatus.fromJson(Map<String, dynamic> json) =>
      _$RpcStatusFromJson(json);

  Map<String, dynamic> toJson() => _$RpcStatusToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
