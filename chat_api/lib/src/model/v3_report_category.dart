//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
enum V3ReportCategory {
  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(0)
  REPORT_CATEGORY_UNSPECIFIED('0'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(1)
  REPORT_CATEGORY_HARASSMENT('1'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(2)
  REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY('2'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(3)
  REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE('3'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(4)
  REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT('4'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(5)
  REPORT_CATEGORY_HATE_SPEECH('5'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(6)
  REPORT_CATEGORY_UNAUTHORIZED_SALES('6'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(7)
  REPORT_CATEGORY_SCAMS('7'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(8)
  REPORT_CATEGORY_SPAM('8'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(9)
  REPORT_CATEGORY_COPYRIGHT('9'),

  /// PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum OTHERS: The user need to enter the reason for the report   - REPORT_CATEGORY_UNSPECIFIED: the user report for reasonable unspecified  - REPORT_CATEGORY_HARASSMENT: the user report for reasonable harassment  - REPORT_CATEGORY_SUICIDE_OR_SELF_INJURY: the user report for reasonable suicide or self injury  - REPORT_CATEGORY_PRETENDING_TO_BE_SOMEONE: The user must choose who is being pretended from PretendingToEnum  - REPORT_CATEGORY_SHARING_INAPPROPRIATE_CONTENT: the user report for reasonable sharing inappropriate content  - REPORT_CATEGORY_HATE_SPEECH: the user report for reasonable hate speech  - REPORT_CATEGORY_UNAUTHORIZED_SALES: the user report for reasonable unauthorized sales  - REPORT_CATEGORY_SCAMS: the user report for reasonable scams  - REPORT_CATEGORY_SPAM: the user report for reasonable spam  - REPORT_CATEGORY_COPYRIGHT: the user report for reasonable copyright  - REPORT_CATEGORY_OTHER: the user report for reasonable others reasons
  @JsonValue(20)
  REPORT_CATEGORY_OTHER('20');

  const V3ReportCategory(this.value);

  final String value;

  @override
  String toString() => value;
}
