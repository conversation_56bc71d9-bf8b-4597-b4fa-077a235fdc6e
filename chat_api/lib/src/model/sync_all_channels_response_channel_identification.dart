//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/sync_all_channels_response_channel_deleted_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sync_all_channels_response_channel_identification.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class SyncAllChannelsResponseChannelIdentification {
  /// Returns a new [SyncAllChannelsResponseChannelIdentification] instance.
  SyncAllChannelsResponseChannelIdentification({
    this.workspaceId,
    this.channelId,
    this.type,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final SyncAllChannelsResponseChannelDeletedTypeEnum? type;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncAllChannelsResponseChannelIdentification &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.type == type;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode + type.hashCode;

  factory SyncAllChannelsResponseChannelIdentification.fromJson(
          Map<String, dynamic> json) =>
      _$SyncAllChannelsResponseChannelIdentificationFromJson(json);

  Map<String, dynamic> toJson() =>
      _$SyncAllChannelsResponseChannelIdentificationToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
