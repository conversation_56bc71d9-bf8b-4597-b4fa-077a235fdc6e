//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_permissions_enum.dart';
import 'package:chat_api/src/model/v3_media_permission_setting_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_metadata.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelMetadata {
  /// Returns a new [V3ChannelMetadata] instance.
  V3ChannelMetadata({
    this.unreadCount,
    this.lastMessageId,
    this.notificationStatus,
    this.mediaPermissionSetting,
    this.permissions,
    this.workspaceId,
    this.channelId,
    this.dmId,
  });

  @Json<PERSON>ey(
    name: r'unreadCount',
    required: false,
    includeIfNull: false,
  )
  final int? unreadCount;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'lastMessageId',
    required: false,
    includeIfNull: false,
  )
  final String? lastMessageId;

  @Json<PERSON><PERSON>(
    name: r'notificationStatus',
    required: false,
    includeIfNull: false,
  )
  final bool? notificationStatus;

  @JsonKey(
    name: r'mediaPermissionSetting',
    required: false,
    includeIfNull: false,
  )
  final V3MediaPermissionSettingEnum? mediaPermissionSetting;

  @JsonKey(
    name: r'permissions',
    required: false,
    includeIfNull: false,
  )
  final List<V3ChannelPermissionsEnum>? permissions;

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'dmId',
    required: false,
    includeIfNull: false,
  )
  final String? dmId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelMetadata &&
          other.unreadCount == unreadCount &&
          other.lastMessageId == lastMessageId &&
          other.notificationStatus == notificationStatus &&
          other.mediaPermissionSetting == mediaPermissionSetting &&
          other.permissions == permissions &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.dmId == dmId;

  @override
  int get hashCode =>
      unreadCount.hashCode +
      lastMessageId.hashCode +
      notificationStatus.hashCode +
      mediaPermissionSetting.hashCode +
      permissions.hashCode +
      workspaceId.hashCode +
      channelId.hashCode +
      dmId.hashCode;

  factory V3ChannelMetadata.fromJson(Map<String, dynamic> json) =>
      _$V3ChannelMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$V3ChannelMetadataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
