//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_badge_value_argument.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_presence_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PresenceUpdatedEventData {
  /// Returns a new [V3PresenceUpdatedEventData] instance.
  V3PresenceUpdatedEventData({
    this.userId,
    this.deviceId,
    this.isOnline,
    this.badgeValueArgument,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'isOnline',
    required: false,
    includeIfNull: false,
  )
  final bool? isOnline;

  @Json<PERSON><PERSON>(
    name: r'badgeValueArgument',
    required: false,
    includeIfNull: false,
  )
  final V3BadgeValueArgument? badgeValueArgument;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PresenceUpdatedEventData &&
          other.userId == userId &&
          other.deviceId == deviceId &&
          other.isOnline == isOnline &&
          other.badgeValueArgument == badgeValueArgument;

  @override
  int get hashCode =>
      userId.hashCode +
      deviceId.hashCode +
      isOnline.hashCode +
      badgeValueArgument.hashCode;

  factory V3PresenceUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3PresenceUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3PresenceUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
