//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_terminate_session_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TerminateSessionRequest {
  /// Returns a new [V3TerminateSessionRequest] instance.
  V3TerminateSessionRequest({
    this.sessionId,
  });

  @JsonKey(
    name: r'sessionId',
    required: false,
    includeIfNull: false,
  )
  final String? sessionId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TerminateSessionRequest && other.sessionId == sessionId;

  @override
  int get hashCode => sessionId.hashCode;

  factory V3TerminateSessionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3TerminateSessionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3TerminateSessionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
