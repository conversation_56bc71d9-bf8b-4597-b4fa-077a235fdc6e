//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_removed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberRemovedEventData {
  /// Returns a new [V3MemberRemovedEventData] instance.
  V3MemberRemovedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.targetUserId,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberRemovedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.targetUserId == targetUserId;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      targetUserId.hashCode;

  factory V3MemberRemovedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MemberRemovedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberRemovedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
