//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cancel_friend_request_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CancelFriendRequestRequest {
  /// Returns a new [V3CancelFriendRequestRequest] instance.
  V3CancelFriendRequestRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CancelFriendRequestRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3CancelFriendRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CancelFriendRequestRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CancelFriendRequestRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
