//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'common_relay_party.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonRelayParty {
  /// Returns a new [CommonRelayParty] instance.
  CommonRelayParty({
    this.id,
    this.name,
    this.icon,
  });

  @JsonKey(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  @Json<PERSON>ey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'icon',
    required: false,
    includeIfNull: false,
  )
  final String? icon;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonRelayParty &&
          other.id == id &&
          other.name == name &&
          other.icon == icon;

  @override
  int get hashCode => id.hashCode + name.hashCode + icon.hashCode;

  factory CommonRelayParty.fromJson(Map<String, dynamic> json) =>
      _$CommonRelayPartyFromJson(json);

  Map<String, dynamic> toJson() => _$CommonRelayPartyToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
