//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_message.dart';
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:chat_api/src/model/v3_user.dart';
import 'package:chat_api/src/model/v3_member.dart';
import 'package:chat_api/src/model/v3_channel_metadata.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_data_include.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DataInclude {
  /// Returns a new [V3DataInclude] instance.
  V3DataInclude({
    this.users,
    this.messages,
    this.channels,
    this.members,
    this.channelMetadata,
  });

  @JsonKey(
    name: r'users',
    required: false,
    includeIfNull: false,
  )
  final List<V3User>? users;

  @JsonKey(
    name: r'messages',
    required: false,
    includeIfNull: false,
  )
  final List<V3Message>? messages;

  @J<PERSON><PERSON><PERSON>(
    name: r'channels',
    required: false,
    includeIfNull: false,
  )
  final List<V3Channel>? channels;

  @JsonKey(
    name: r'members',
    required: false,
    includeIfNull: false,
  )
  final List<V3Member>? members;

  @JsonKey(
    name: r'channelMetadata',
    required: false,
    includeIfNull: false,
  )
  final List<V3ChannelMetadata>? channelMetadata;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DataInclude &&
          other.users == users &&
          other.messages == messages &&
          other.channels == channels &&
          other.members == members &&
          other.channelMetadata == channelMetadata;

  @override
  int get hashCode =>
      users.hashCode +
      messages.hashCode +
      channels.hashCode +
      members.hashCode +
      channelMetadata.hashCode;

  factory V3DataInclude.fromJson(Map<String, dynamic> json) =>
      _$V3DataIncludeFromJson(json);

  Map<String, dynamic> toJson() => _$V3DataIncludeToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
