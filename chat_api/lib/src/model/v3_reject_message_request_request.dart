//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_reject_message_request_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RejectMessageRequestRequest {
  /// Returns a new [V3RejectMessageRequestRequest] instance.
  V3RejectMessageRequestRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RejectMessageRequestRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3RejectMessageRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RejectMessageRequestRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RejectMessageRequestRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
