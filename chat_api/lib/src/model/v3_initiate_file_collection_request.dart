//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:chat_api/src/model/v3_layout_metadata.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_file_collection_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateFileCollectionRequest {
  /// Returns a new [V3InitiateFileCollectionRequest] instance.
  V3InitiateFileCollectionRequest({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.ref,
    this.numberOfFiles,
    this.storageRequest,
    this.layoutMetadata,
    this.content,
    this.contentLocale,
    this.attachmentType,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  /// The number of files to be collected.
  @JsonKey(
    name: r'numberOfFiles',
    required: false,
    includeIfNull: false,
  )
  final int? numberOfFiles;

  /// The amount of storage required to store the collected files.
  @JsonKey(
    name: r'storageRequest',
    required: false,
    includeIfNull: false,
  )
  final int? storageRequest;

  /// Metadata associated with the layout of each file.
  @JsonKey(
    name: r'layoutMetadata',
    required: false,
    includeIfNull: false,
  )
  final List<V3LayoutMetadata>? layoutMetadata;

  @JsonKey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @JsonKey(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @JsonKey(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateFileCollectionRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId &&
          other.ref == ref &&
          other.numberOfFiles == numberOfFiles &&
          other.storageRequest == storageRequest &&
          other.layoutMetadata == layoutMetadata &&
          other.content == content &&
          other.contentLocale == contentLocale &&
          other.attachmentType == attachmentType;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      userId.hashCode +
      ref.hashCode +
      numberOfFiles.hashCode +
      storageRequest.hashCode +
      layoutMetadata.hashCode +
      content.hashCode +
      contentLocale.hashCode +
      attachmentType.hashCode;

  factory V3InitiateFileCollectionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3InitiateFileCollectionRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateFileCollectionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
