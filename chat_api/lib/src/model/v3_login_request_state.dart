//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3LoginRequestState {
  @JsonValue(0)
  PENDING('0'),
  @JsonValue(1)
  CLOSED('1'),
  @JsonValue(2)
  FAILED('2'),
  @JsonValue(3)
  VERIFIED('3'),
  @JsonValue(4)
  COMPLETED('4');

  const V3LoginRequestState(this.value);

  final String value;

  @override
  String toString() => value;
}
