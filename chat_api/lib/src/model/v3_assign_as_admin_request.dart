//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_assign_as_admin_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AssignAsAdminRequest {
  /// Returns a new [V3AssignAsAdminRequest] instance.
  V3AssignAsAdminRequest({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AssignAsAdminRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + userId.hashCode;

  factory V3AssignAsAdminRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AssignAsAdminRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AssignAsAdminRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
