//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_creation_failed_data_geo_location.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_creation_failed_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserCreationFailedData {
  /// Returns a new [V3UserCreationFailedData] instance.
  V3UserCreationFailedData({
    this.userId,
    this.username,
    this.geolocation,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'geolocation',
    required: false,
    includeIfNull: false,
  )
  final V3UserCreationFailedDataGeoLocation? geolocation;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserCreationFailedData &&
          other.userId == userId &&
          other.username == username &&
          other.geolocation == geolocation;

  @override
  int get hashCode =>
      userId.hashCode + username.hashCode + geolocation.hashCode;

  factory V3UserCreationFailedData.fromJson(Map<String, dynamic> json) =>
      _$V3UserCreationFailedDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserCreationFailedDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
