//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_delete_channel_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeleteChannelResponse {
  /// Returns a new [V3DeleteChannelResponse] instance.
  V3DeleteChannelResponse({
    this.ok,
    this.error,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeleteChannelResponse &&
          other.ok == ok &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + error.hashCode;

  factory V3DeleteChannelResponse.fromJson(Map<String, dynamic> json) =>
      _$V3DeleteChannelResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3DeleteChannelResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
