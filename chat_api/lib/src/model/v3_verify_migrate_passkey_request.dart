//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_attestation_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_verify_migrate_passkey_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3VerifyMigratePasskeyRequest {
  /// Returns a new [V3VerifyMigratePasskeyRequest] instance.
  V3VerifyMigratePasskeyRequest({
    this.reqId,
    this.reqVerifier,
    this.credential,
  });

  @Json<PERSON><PERSON>(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'credential',
    required: false,
    includeIfNull: false,
  )
  final CommonAttestationResult? credential;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3VerifyMigratePasskeyRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.credential == credential;

  @override
  int get hashCode =>
      reqId.hashCode + reqVerifier.hashCode + credential.hashCode;

  factory V3VerifyMigratePasskeyRequest.fromJson(Map<String, dynamic> json) =>
      _$V3VerifyMigratePasskeyRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3VerifyMigratePasskeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
