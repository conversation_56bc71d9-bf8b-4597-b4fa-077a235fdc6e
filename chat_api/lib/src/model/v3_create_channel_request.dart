//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_create_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CreateChannelRequest {
  /// Returns a new [V3CreateChannelRequest] instance.
  V3CreateChannelRequest({
    this.workspaceId,
    this.name,
    this.avatarPath,
    this.userIds,
    this.channelType,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  /// The display name of a channel.
  @JsonKey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  /// Users can update the avatar of a channel at any time.
  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'avatarPath',
    required: false,
    includeIfNull: false,
  )
  final String? avatarPath;

  /// The list of user IDs invited to join a group.  List of userId.
  @JsonKey(
    name: r'userIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? userIds;

  @JsonKey(
    name: r'channelType',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelTypeEnum? channelType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CreateChannelRequest &&
          other.workspaceId == workspaceId &&
          other.name == name &&
          other.avatarPath == avatarPath &&
          other.userIds == userIds &&
          other.channelType == channelType;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      name.hashCode +
      avatarPath.hashCode +
      userIds.hashCode +
      channelType.hashCode;

  factory V3CreateChannelRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CreateChannelRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CreateChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
