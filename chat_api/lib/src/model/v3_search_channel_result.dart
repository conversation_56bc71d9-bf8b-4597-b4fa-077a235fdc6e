//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_channel_result.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchChannelResult {
  /// Returns a new [V3SearchChannelResult] instance.
  V3SearchChannelResult({
    this.workspaceId,
    this.channelId,
    this.name,
    this.avatar,
    this.isPrivate,
    this.type,
    this.totalMembers,
    this.invitationLink,
    this.createTime,
    this.updateTime,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON>son<PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @Json<PERSON><PERSON>(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @JsonKey(
    name: r'isPrivate',
    required: false,
    includeIfNull: false,
  )
  final bool? isPrivate;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelTypeEnum? type;

  @JsonKey(
    name: r'totalMembers',
    required: false,
    includeIfNull: false,
  )
  final int? totalMembers;

  @JsonKey(
    name: r'invitationLink',
    required: false,
    includeIfNull: false,
  )
  final String? invitationLink;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchChannelResult &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.name == name &&
          other.avatar == avatar &&
          other.isPrivate == isPrivate &&
          other.type == type &&
          other.totalMembers == totalMembers &&
          other.invitationLink == invitationLink &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      name.hashCode +
      avatar.hashCode +
      isPrivate.hashCode +
      type.hashCode +
      totalMembers.hashCode +
      invitationLink.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3SearchChannelResult.fromJson(Map<String, dynamic> json) =>
      _$V3SearchChannelResultFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchChannelResultToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
