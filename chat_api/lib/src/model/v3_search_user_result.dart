//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_avatar_type_enum.dart';
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_user_result.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchUserResult {
  /// Returns a new [V3SearchUserResult] instance.
  V3SearchUserResult({
    this.userId,
    this.displayName,
    this.username,
    this.avatar,
    this.videoAvatar,
    this.decoratedAvatar,
    this.avatarType,
    this.userBadgeType,
  });

  /// The unique identifier for the user.
  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  /// The display name of the user.
  @JsonKey(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  /// The username of the user.
  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  /// The avatar image associated with the user.
  @JsonKey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  /// The avatar decorated image.
  @JsonKey(
    name: r'videoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? videoAvatar;

  @JsonKey(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @JsonKey(
    name: r'avatarType',
    required: false,
    includeIfNull: false,
  )
  final V3UserAvatarTypeEnum? avatarType;

  @JsonKey(
    name: r'userBadgeType',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? userBadgeType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchUserResult &&
          other.userId == userId &&
          other.displayName == displayName &&
          other.username == username &&
          other.avatar == avatar &&
          other.videoAvatar == videoAvatar &&
          other.decoratedAvatar == decoratedAvatar &&
          other.avatarType == avatarType &&
          other.userBadgeType == userBadgeType;

  @override
  int get hashCode =>
      userId.hashCode +
      displayName.hashCode +
      username.hashCode +
      avatar.hashCode +
      videoAvatar.hashCode +
      decoratedAvatar.hashCode +
      avatarType.hashCode +
      userBadgeType.hashCode;

  factory V3SearchUserResult.fromJson(Map<String, dynamic> json) =>
      _$V3SearchUserResultFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchUserResultToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
