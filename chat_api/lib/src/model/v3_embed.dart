//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_location_data.dart';
import 'package:chat_api/src/model/v3_embed_type_enum.dart';
import 'package:chat_api/src/model/v3_embed_data.dart';
import 'package:chat_api/src/model/v3_invitation_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_embed.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Embed {
  /// Returns a new [V3Embed] instance.
  V3Embed({
    this.meta,
    this.provider,
    this.url,
    this.type,
    this.embedData,
    this.invitationData,
    this.locationData,
  });

  @JsonKey(
    name: r'meta',
    required: false,
    includeIfNull: false,
  )
  final String? meta;

  @Json<PERSON>ey(
    name: r'provider',
    required: false,
    includeIfNull: false,
  )
  final String? provider;

  @Json<PERSON>ey(
    name: r'url',
    required: false,
    includeIfNull: false,
  )
  final String? url;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final V3EmbedTypeEnum? type;

  @JsonKey(
    name: r'embedData',
    required: false,
    includeIfNull: false,
  )
  final V3EmbedData? embedData;

  @JsonKey(
    name: r'invitationData',
    required: false,
    includeIfNull: false,
  )
  final V3InvitationData? invitationData;

  @JsonKey(
    name: r'locationData',
    required: false,
    includeIfNull: false,
  )
  final V3LocationData? locationData;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Embed &&
          other.meta == meta &&
          other.provider == provider &&
          other.url == url &&
          other.type == type &&
          other.embedData == embedData &&
          other.invitationData == invitationData &&
          other.locationData == locationData;

  @override
  int get hashCode =>
      meta.hashCode +
      provider.hashCode +
      url.hashCode +
      type.hashCode +
      embedData.hashCode +
      invitationData.hashCode +
      locationData.hashCode;

  factory V3Embed.fromJson(Map<String, dynamic> json) =>
      _$V3EmbedFromJson(json);

  Map<String, dynamic> toJson() => _$V3EmbedToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
