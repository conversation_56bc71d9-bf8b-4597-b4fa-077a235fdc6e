//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/sync_users_response_user_identification.dart';
import 'package:chat_api/src/model/v3_user_view.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sync_users_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SyncUsersResponse {
  /// Returns a new [V3SyncUsersResponse] instance.
  V3SyncUsersResponse({
    this.data,
    this.userDeleted,
    this.syncTime,
  });

  /// List of values for the user view interface have update after sync time input.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3UserView>? data;

  /// // List of userIds deleted.
  @J<PERSON><PERSON><PERSON>(
    name: r'userDeleted',
    required: false,
    includeIfNull: false,
  )
  final List<SyncUsersResponseUserIdentification>? userDeleted;

  @JsonKey(
    name: r'syncTime',
    required: false,
    includeIfNull: false,
  )
  final String? syncTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SyncUsersResponse &&
          other.data == data &&
          other.userDeleted == userDeleted &&
          other.syncTime == syncTime;

  @override
  int get hashCode => data.hashCode + userDeleted.hashCode + syncTime.hashCode;

  factory V3SyncUsersResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SyncUsersResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SyncUsersResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
