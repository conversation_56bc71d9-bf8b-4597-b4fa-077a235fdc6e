//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_permission_setting_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_dm_media_permission_setting_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateDMMediaPermissionSettingRequest {
  /// Returns a new [V3UpdateDMMediaPermissionSettingRequest] instance.
  V3UpdateDMMediaPermissionSettingRequest({
    this.userId,
    this.mediaPermissionSetting,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'mediaPermissionSetting',
    required: false,
    includeIfNull: false,
  )
  final V3MediaPermissionSettingEnum? mediaPermissionSetting;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateDMMediaPermissionSettingRequest &&
          other.userId == userId &&
          other.mediaPermissionSetting == mediaPermissionSetting;

  @override
  int get hashCode => userId.hashCode + mediaPermissionSetting.hashCode;

  factory V3UpdateDMMediaPermissionSettingRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateDMMediaPermissionSettingRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateDMMediaPermissionSettingRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
