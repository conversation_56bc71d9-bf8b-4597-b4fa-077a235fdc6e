//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_mock_messages_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MockMessagesRequest {
  /// Returns a new [V3MockMessagesRequest] instance.
  V3MockMessagesRequest({
    this.workspaceId,
    this.channelId,
    this.quantity,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'quantity',
    required: false,
    includeIfNull: false,
  )
  final int? quantity;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MockMessagesRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.quantity == quantity;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + quantity.hashCode;

  factory V3MockMessagesRequest.fromJson(Map<String, dynamic> json) =>
      _$V3MockMessagesRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3MockMessagesRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
