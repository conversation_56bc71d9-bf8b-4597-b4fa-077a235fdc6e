//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_assertion_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_generate_security_key_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GenerateSecurityKeyRequest {
  /// Returns a new [V3GenerateSecurityKeyRequest] instance.
  V3GenerateSecurityKeyRequest({
    this.reqId,
    this.reqVerifier,
    this.assertion,
  });

  @Json<PERSON>ey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  /// The randomly generated value created by the client.
  @JsonKey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @J<PERSON><PERSON><PERSON>(
    name: r'assertion',
    required: false,
    includeIfNull: false,
  )
  final CommonAssertionResult? assertion;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GenerateSecurityKeyRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.assertion == assertion;

  @override
  int get hashCode =>
      reqId.hashCode + reqVerifier.hashCode + assertion.hashCode;

  factory V3GenerateSecurityKeyRequest.fromJson(Map<String, dynamic> json) =>
      _$V3GenerateSecurityKeyRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3GenerateSecurityKeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
