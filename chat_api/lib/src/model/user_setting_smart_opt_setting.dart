//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'user_setting_smart_opt_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class UserSettingSmartOptSetting {
  /// Returns a new [UserSettingSmartOptSetting] instance.
  UserSettingSmartOptSetting({
    this.enable,
  });

  @JsonKey(
    name: r'enable',
    required: false,
    includeIfNull: false,
  )
  final bool? enable;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingSmartOptSetting && other.enable == enable;

  @override
  int get hashCode => enable.hashCode;

  factory UserSettingSmartOptSetting.fromJson(Map<String, dynamic> json) =>
      _$UserSettingSmartOptSettingFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingSmartOptSettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
