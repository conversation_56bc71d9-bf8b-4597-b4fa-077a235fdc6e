//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_member_data.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_nickname_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateNicknameResponse {
  /// Returns a new [V3UpdateNicknameResponse] instance.
  V3UpdateNicknameResponse({
    this.ok,
    this.data,
    this.error,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON>son<PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3MemberData? data;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateNicknameResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + data.hashCode + error.hashCode + includes.hashCode;

  factory V3UpdateNicknameResponse.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateNicknameResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateNicknameResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
