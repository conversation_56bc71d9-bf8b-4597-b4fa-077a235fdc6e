//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_call_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_create_call_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CreateCallRequest {
  /// Returns a new [V3CreateCallRequest] instance.
  V3CreateCallRequest({
    this.callId,
    this.userId,
    this.type,
  });

  @Json<PERSON>ey(
    name: r'callId',
    required: false,
    includeIfNull: false,
  )
  final String? callId;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final V3CallTypeEnum? type;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CreateCallRequest &&
          other.callId == callId &&
          other.userId == userId &&
          other.type == type;

  @override
  int get hashCode => callId.hashCode + userId.hashCode + type.hashCode;

  factory V3CreateCallRequest.fromJson(Map<String, dynamic> json) =>
      _$V3CreateCallRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3CreateCallRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
