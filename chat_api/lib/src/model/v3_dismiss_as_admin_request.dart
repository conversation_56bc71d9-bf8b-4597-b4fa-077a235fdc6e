//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_dismiss_as_admin_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DismissAsAdminRequest {
  /// Returns a new [V3DismissAsAdminRequest] instance.
  V3DismissAsAdminRequest({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DismissAsAdminRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + userId.hashCode;

  factory V3DismissAsAdminRequest.fromJson(Map<String, dynamic> json) =>
      _$V3DismissAsAdminRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3DismissAsAdminRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
