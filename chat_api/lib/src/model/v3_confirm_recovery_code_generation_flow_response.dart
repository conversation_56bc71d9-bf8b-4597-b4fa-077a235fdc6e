//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_confirm_recovery_code_generation_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ConfirmRecoveryCodeGenerationFlowResponse {
  /// Returns a new [V3ConfirmRecoveryCodeGenerationFlowResponse] instance.
  V3ConfirmRecoveryCodeGenerationFlowResponse({
    this.ok,
    this.error,
    this.recoveryCode,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'recoveryCode',
    required: false,
    includeIfNull: false,
  )
  final String? recoveryCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ConfirmRecoveryCodeGenerationFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.recoveryCode == recoveryCode;

  @override
  int get hashCode => ok.hashCode + error.hashCode + recoveryCode.hashCode;

  factory V3ConfirmRecoveryCodeGenerationFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ConfirmRecoveryCodeGenerationFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ConfirmRecoveryCodeGenerationFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
