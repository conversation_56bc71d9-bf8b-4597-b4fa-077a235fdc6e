//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_block_user_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3BlockUserRequest {
  /// Returns a new [V3BlockUserRequest] instance.
  V3BlockUserRequest({
    this.targetUserId,
  });

  @JsonKey(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3BlockUserRequest && other.targetUserId == targetUserId;

  @override
  int get hashCode => targetUserId.hashCode;

  factory V3BlockUserRequest.fromJson(Map<String, dynamic> json) =>
      _$V3BlockUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3BlockUserRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
