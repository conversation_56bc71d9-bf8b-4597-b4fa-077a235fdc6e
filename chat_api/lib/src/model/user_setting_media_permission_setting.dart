//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_permission_setting_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_setting_media_permission_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class UserSettingMediaPermissionSetting {
  /// Returns a new [UserSettingMediaPermissionSetting] instance.
  UserSettingMediaPermissionSetting({
    this.value,
  });

  @JsonKey(
    name: r'value',
    required: false,
    includeIfNull: false,
  )
  final V3MediaPermissionSettingEnum? value;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingMediaPermissionSetting && other.value == value;

  @override
  int get hashCode => value.hashCode;

  factory UserSettingMediaPermissionSetting.fromJson(
          Map<String, dynamic> json) =>
      _$UserSettingMediaPermissionSettingFromJson(json);

  Map<String, dynamic> toJson() =>
      _$UserSettingMediaPermissionSettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
