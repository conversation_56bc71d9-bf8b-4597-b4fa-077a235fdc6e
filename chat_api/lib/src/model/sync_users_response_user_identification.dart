//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_deleted_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sync_users_response_user_identification.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class SyncUsersResponseUserIdentification {
  /// Returns a new [SyncUsersResponseUserIdentification] instance.
  SyncUsersResponseUserIdentification({
    this.userId,
    this.username,
    this.type,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final V3UserDeletedTypeEnum? type;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncUsersResponseUserIdentification &&
          other.userId == userId &&
          other.username == username &&
          other.type == type;

  @override
  int get hashCode => userId.hashCode + username.hashCode + type.hashCode;

  factory SyncUsersResponseUserIdentification.fromJson(
          Map<String, dynamic> json) =>
      _$SyncUsersResponseUserIdentificationFromJson(json);

  Map<String, dynamic> toJson() =>
      _$SyncUsersResponseUserIdentificationToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
