//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_qr_auth_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateQRAuthFlowRequest {
  /// Returns a new [V3InitiateQRAuthFlowRequest] instance.
  V3InitiateQRAuthFlowRequest({
    this.reqId,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateQRAuthFlowRequest && other.reqId == reqId;

  @override
  int get hashCode => reqId.hashCode;

  factory V3InitiateQRAuthFlowRequest.fromJson(Map<String, dynamic> json) =>
      _$V3InitiateQRAuthFlowRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3InitiateQRAuthFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
