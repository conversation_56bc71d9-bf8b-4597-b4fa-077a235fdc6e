//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3ChannelPermissionsEnum {
  @JsonValue(0)
  OWNER('0'),
  @JsonValue(1)
  CHANNELS__VIEW_CHANNEL('1'),
  @JsonValue(2)
  CHANNELS__MANAGE('2'),
  @JsonValue(3)
  CHANNELS__MEMBERS_MANAGE('3'),
  @JsonValue(4)
  CHANNELS__STICKERS_MANAGE('4'),
  @JsonValue(5)
  CHANNELS__INVITATIONS_MANAGE('5'),
  @JsonValue(6)
  CHANNELS__INVITATIONS_CREATE('6'),
  @JsonValue(7)
  MESSAGES__MANAGE('7'),
  @JsonValue(8)
  MESSAGES__VIEW('8'),
  @JsonValue(9)
  MESSAGES__SEND_MESSAGE('9'),
  @JsonValue(10)
  MESSAGES__SEND_ATTACHMENTS('10'),
  @JsonValue(11)
  MESSAGES__EMBED_LINKS('11'),
  @JsonValue(12)
  MESSAGES__MENTION_EVERYONE('12'),
  @JsonValue(13)
  CHANNELS__VIEW_AUDIT_LOGS('13');

  const V3ChannelPermissionsEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
