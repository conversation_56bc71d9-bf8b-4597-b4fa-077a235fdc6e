//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_member_role.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_member.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Member {
  /// Returns a new [V3Member] instance.
  V3Member({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.nickname,
    this.role,
    this.roles,
    this.createTime,
    this.updateTime,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'nickname',
    required: false,
    includeIfNull: false,
  )
  final String? nickname;

  @J<PERSON><PERSON><PERSON>(
    name: r'role',
    required: false,
    includeIfNull: false,
  )
  final String? role;

  @JsonKey(
    name: r'roles',
    required: false,
    includeIfNull: false,
  )
  final List<V3MemberRole>? roles;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Member &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId &&
          other.nickname == nickname &&
          other.role == role &&
          other.roles == roles &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      userId.hashCode +
      nickname.hashCode +
      role.hashCode +
      roles.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3Member.fromJson(Map<String, dynamic> json) =>
      _$V3MemberFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
