//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/v3_sync_dm_messages_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'stream_result_of_v3_sync_dm_messages_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class StreamResultOfV3SyncDMMessagesResponse {
  /// Returns a new [StreamResultOfV3SyncDMMessagesResponse] instance.
  StreamResultOfV3SyncDMMessagesResponse({
    this.result,
    this.error,
  });

  @JsonKey(
    name: r'result',
    required: false,
    includeIfNull: false,
  )
  final V3SyncDMMessagesResponse? result;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final RpcStatus? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreamResultOfV3SyncDMMessagesResponse &&
          other.result == result &&
          other.error == error;

  @override
  int get hashCode => result.hashCode + error.hashCode;

  factory StreamResultOfV3SyncDMMessagesResponse.fromJson(
          Map<String, dynamic> json) =>
      _$StreamResultOfV3SyncDMMessagesResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$StreamResultOfV3SyncDMMessagesResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
