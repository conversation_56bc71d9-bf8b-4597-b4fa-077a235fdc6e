//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cover_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CoverData {
  /// Returns a new [V3CoverData] instance.
  V3CoverData({
    this.cover,
  });

  @JsonKey(
    name: r'cover',
    required: false,
    includeIfNull: false,
  )
  final String? cover;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is V3CoverData && other.cover == cover;

  @override
  int get hashCode => cover.hashCode;

  factory V3CoverData.fromJson(Map<String, dynamic> json) =>
      _$V3CoverDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CoverDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
