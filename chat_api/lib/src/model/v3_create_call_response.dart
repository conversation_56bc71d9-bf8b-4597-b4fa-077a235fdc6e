//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_create_call_response_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_create_call_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CreateCallResponse {
  /// Returns a new [V3CreateCallResponse] instance.
  V3CreateCallResponse({
    this.ok,
    this.data,
    this.error,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3CreateCallResponseData? data;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CreateCallResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3CreateCallResponse.fromJson(Map<String, dynamic> json) =>
      _$V3CreateCallResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3CreateCallResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
