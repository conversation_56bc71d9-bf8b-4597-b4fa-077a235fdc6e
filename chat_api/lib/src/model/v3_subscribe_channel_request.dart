//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_subscribe_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SubscribeChannelRequest {
  /// Returns a new [V3SubscribeChannelRequest] instance.
  V3SubscribeChannelRequest({
    this.workspaceId,
    this.channelId,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  /// The ID of the channel to subscribe to.
  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SubscribeChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode;

  factory V3SubscribeChannelRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SubscribeChannelRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SubscribeChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
