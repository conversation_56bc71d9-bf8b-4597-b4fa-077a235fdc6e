//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'iamv3_terminate_session.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class Iamv3TerminateSession {
  /// Returns a new [Iamv3TerminateSession] instance.
  Iamv3TerminateSession({
    this.deviceId,
    this.sessionId,
  });

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @JsonKey(
    name: r'sessionId',
    required: false,
    includeIfNull: false,
  )
  final String? sessionId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Iamv3TerminateSession &&
          other.deviceId == deviceId &&
          other.sessionId == sessionId;

  @override
  int get hashCode => deviceId.hashCode + sessionId.hashCode;

  factory Iamv3TerminateSession.fromJson(Map<String, dynamic> json) =>
      _$Iamv3TerminateSessionFromJson(json);

  Map<String, dynamic> toJson() => _$Iamv3TerminateSessionToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
