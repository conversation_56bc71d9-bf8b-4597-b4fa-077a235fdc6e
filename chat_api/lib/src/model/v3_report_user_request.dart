//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_pretending_to.dart';
import 'package:chat_api/src/model/v3_report_category.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_report_user_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ReportUserRequest {
  /// Returns a new [V3ReportUserRequest] instance.
  V3ReportUserRequest({
    this.userId,
    this.reportCategory,
    this.pretendingTo,
    this.reportReason,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'reportCategory',
    required: false,
    includeIfNull: false,
  )
  final V3ReportCategory? reportCategory;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'pretendingTo',
    required: false,
    includeIfNull: false,
  )
  final V3PretendingTo? pretendingTo;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'reportReason',
    required: false,
    includeIfNull: false,
  )
  final String? reportReason;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ReportUserRequest &&
          other.userId == userId &&
          other.reportCategory == reportCategory &&
          other.pretendingTo == pretendingTo &&
          other.reportReason == reportReason;

  @override
  int get hashCode =>
      userId.hashCode +
      reportCategory.hashCode +
      pretendingTo.hashCode +
      reportReason.hashCode;

  factory V3ReportUserRequest.fromJson(Map<String, dynamic> json) =>
      _$V3ReportUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3ReportUserRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
