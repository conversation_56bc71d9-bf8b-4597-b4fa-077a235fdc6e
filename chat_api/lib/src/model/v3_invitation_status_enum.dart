//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - INVITATION_STATUS_ENUM_ACTIVE: ACTIVE: Link invitation is still valid  - INVITATION_STATUS_ENUM_EXPIRED: EXPIRED: Link invitation has expired  - INVITATION_STATUS_ENUM_REVOKED: REVOKED: Link invitation has been revoked
enum V3InvitationStatusEnum {
  /// - INVITATION_STATUS_ENUM_ACTIVE: ACTIVE: Link invitation is still valid  - INVITATION_STATUS_ENUM_EXPIRED: EXPIRED: Link invitation has expired  - INVITATION_STATUS_ENUM_REVOKED: REVOKED: Link invitation has been revoked
  @JsonValue(0)
  INVITATION_STATUS_ENUM_ACTIVE('0'),

  /// - INVITATION_STATUS_ENUM_ACTIVE: ACTIVE: Link invitation is still valid  - INVITATION_STATUS_ENUM_EXPIRED: EXPIRED: Link invitation has expired  - INVITATION_STATUS_ENUM_REVOKED: REVOKED: Link invitation has been revoked
  @JsonValue(1)
  INVITATION_STATUS_ENUM_EXPIRED('1'),

  /// - INVITATION_STATUS_ENUM_ACTIVE: ACTIVE: Link invitation is still valid  - INVITATION_STATUS_ENUM_EXPIRED: EXPIRED: Link invitation has expired  - INVITATION_STATUS_ENUM_REVOKED: REVOKED: Link invitation has been revoked
  @JsonValue(2)
  INVITATION_STATUS_ENUM_REVOKED('2');

  const V3InvitationStatusEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
