//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_view.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_user_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetUserResponse {
  /// Returns a new [V3GetUserResponse] instance.
  V3GetUserResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3UserView? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetUserResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3GetUserResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetUserResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetUserResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
