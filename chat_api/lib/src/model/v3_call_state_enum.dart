//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3CallStateEnum {
  @JsonValue(0)
  CALL_STATE_UNSPECIFIED('0'),
  @JsonValue(1)
  CALL_STATE_DIALING('1'),
  @JsonValue(2)
  CALL_STATE_CALLING('2'),
  @JsonValue(3)
  CALL_STATE_READY_TO_CONNECT('3'),
  @JsonValue(4)
  CALL_STATE_CONNECTING('4'),
  @JsonValue(5)
  CALL_STATE_CONNECTED('5'),
  @JsonValue(6)
  CALL_STATE_RECONNECTING('6'),
  @JsonValue(10)
  CALL_STATE_ENDED('10');

  const V3CallStateEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
