//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_search_members_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_members_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchMembersResponse {
  /// Returns a new [V3SearchMembersResponse] instance.
  V3SearchMembersResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
  });

  /// Indicates whether the search operation was successful.
  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  /// An array of search result items containing member information. Only have value when OK is true.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3SearchMembersResult>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchMembersResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + paging.hashCode;

  factory V3SearchMembersResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SearchMembersResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchMembersResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
