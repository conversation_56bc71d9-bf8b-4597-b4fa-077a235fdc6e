//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_invitation_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendInvitationRequest {
  /// Returns a new [V3SendInvitationRequest] instance.
  V3SendInvitationRequest({
    this.invitationLink,
    this.userIds,
  });

  @JsonKey(
    name: r'invitationLink',
    required: false,
    includeIfNull: false,
  )
  final String? invitationLink;

  @JsonKey(
    name: r'userIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? userIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendInvitationRequest &&
          other.invitationLink == invitationLink &&
          other.userIds == userIds;

  @override
  int get hashCode => invitationLink.hashCode + userIds.hashCode;

  factory V3SendInvitationRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendInvitationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendInvitationRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
