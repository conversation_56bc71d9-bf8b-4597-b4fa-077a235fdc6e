//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_avatar_type_enum.dart';
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_profile.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Profile {
  /// Returns a new [V3Profile] instance.
  V3Profile({
    this.avatar,
    this.displayName,
    this.cover,
    this.originalAvatar,
    this.avatarType,
    this.videoAvatar,
    this.userBadgeType,
    this.decoratedAvatar,
    this.originalDecoratedAvatar,
  });

  @Json<PERSON>ey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @J<PERSON><PERSON><PERSON>(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'cover',
    required: false,
    includeIfNull: false,
  )
  final String? cover;

  @J<PERSON><PERSON><PERSON>(
    name: r'originalAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? originalAvatar;

  @JsonKey(
    name: r'avatarType',
    required: false,
    includeIfNull: false,
  )
  final V3UserAvatarTypeEnum? avatarType;

  @JsonKey(
    name: r'videoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? videoAvatar;

  @JsonKey(
    name: r'userBadgeType',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? userBadgeType;

  @JsonKey(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @JsonKey(
    name: r'originalDecoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? originalDecoratedAvatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Profile &&
          other.avatar == avatar &&
          other.displayName == displayName &&
          other.cover == cover &&
          other.originalAvatar == originalAvatar &&
          other.avatarType == avatarType &&
          other.videoAvatar == videoAvatar &&
          other.userBadgeType == userBadgeType &&
          other.decoratedAvatar == decoratedAvatar &&
          other.originalDecoratedAvatar == originalDecoratedAvatar;

  @override
  int get hashCode =>
      avatar.hashCode +
      displayName.hashCode +
      cover.hashCode +
      originalAvatar.hashCode +
      avatarType.hashCode +
      videoAvatar.hashCode +
      userBadgeType.hashCode +
      decoratedAvatar.hashCode +
      originalDecoratedAvatar.hashCode;

  factory V3Profile.fromJson(Map<String, dynamic> json) =>
      _$V3ProfileFromJson(json);

  Map<String, dynamic> toJson() => _$V3ProfileToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
