//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_qr_auth_code_request_options.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3QRAuthCodeRequestOptions {
  /// Returns a new [V3QRAuthCodeRequestOptions] instance.
  V3QRAuthCodeRequestOptions({
    this.qrAuthCode,
    this.timeout,
  });

  @Json<PERSON>ey(
    name: r'qrAuthCode',
    required: false,
    includeIfNull: false,
  )
  final String? qrAuthCode;

  @Json<PERSON>ey(
    name: r'timeout',
    required: false,
    includeIfNull: false,
  )
  final int? timeout;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3QRAuthCodeRequestOptions &&
          other.qrAuthCode == qrAuthCode &&
          other.timeout == timeout;

  @override
  int get hashCode => qrAuthCode.hashCode + timeout.hashCode;

  factory V3QRAuthCodeRequestOptions.fromJson(Map<String, dynamic> json) =>
      _$V3QRAuthCodeRequestOptionsFromJson(json);

  Map<String, dynamic> toJson() => _$V3QRAuthCodeRequestOptionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
