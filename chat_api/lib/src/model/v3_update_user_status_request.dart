//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_status_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserStatusRequest {
  /// Returns a new [V3UpdateUserStatusRequest] instance.
  V3UpdateUserStatusRequest({
    this.content,
    this.status,
  });

  @JsonKey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @JsonKey(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final String? status;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserStatusRequest &&
          other.content == content &&
          other.status == status;

  @override
  int get hashCode => content.hashCode + status.hashCode;

  factory V3UpdateUserStatusRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserStatusRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserStatusRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
