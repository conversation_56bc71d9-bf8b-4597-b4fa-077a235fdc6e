//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_device_session.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sessions_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SessionsData {
  /// Returns a new [V3SessionsData] instance.
  V3SessionsData({
    this.currentSession,
    this.activeSessions,
  });

  @JsonKey(
    name: r'currentSession',
    required: false,
    includeIfNull: false,
  )
  final V3DeviceSession? currentSession;

  @Json<PERSON>ey(
    name: r'activeSessions',
    required: false,
    includeIfNull: false,
  )
  final List<V3DeviceSession>? activeSessions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SessionsData &&
          other.currentSession == currentSession &&
          other.activeSessions == activeSessions;

  @override
  int get hashCode => currentSession.hashCode + activeSessions.hashCode;

  factory V3SessionsData.fromJson(Map<String, dynamic> json) =>
      _$V3SessionsDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3SessionsDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
