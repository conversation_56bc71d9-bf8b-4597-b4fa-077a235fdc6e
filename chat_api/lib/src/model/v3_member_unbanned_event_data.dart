//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_unbanned_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberUnbannedEventData {
  /// Returns a new [V3MemberUnbannedEventData] instance.
  V3MemberUnbannedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.unbannedUserId,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'unbannedUserId',
    required: false,
    includeIfNull: false,
  )
  final String? unbannedUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberUnbannedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.unbannedUserId == unbannedUserId;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      unbannedUserId.hashCode;

  factory V3MemberUnbannedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MemberUnbannedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberUnbannedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
