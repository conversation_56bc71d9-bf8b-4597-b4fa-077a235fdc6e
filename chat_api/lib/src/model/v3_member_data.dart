//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_member.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberData {
  /// Returns a new [V3MemberData] instance.
  V3MemberData({
    this.member,
  });

  @JsonKey(
    name: r'member',
    required: false,
    includeIfNull: false,
  )
  final V3Member? member;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is V3MemberData && other.member == member;

  @override
  int get hashCode => member.hashCode;

  factory V3MemberData.fromJson(Map<String, dynamic> json) =>
      _$V3MemberDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
