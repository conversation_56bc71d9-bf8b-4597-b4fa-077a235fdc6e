//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_tokens_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetTokensRequest {
  /// Returns a new [V3GetTokensRequest] instance.
  V3GetTokensRequest({
    this.usernames,
  });

  @JsonKey(
    name: r'usernames',
    required: false,
    includeIfNull: false,
  )
  final List<String>? usernames;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetTokensRequest && other.usernames == usernames;

  @override
  int get hashCode => usernames.hashCode;

  factory V3GetTokensRequest.fromJson(Map<String, dynamic> json) =>
      _$V3GetTokensRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetTokensRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
