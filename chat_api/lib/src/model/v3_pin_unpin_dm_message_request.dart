//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_pin_unpin_dm_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PinUnpinDMMessageRequest {
  /// Returns a new [V3PinUnpinDMMessageRequest] instance.
  V3PinUnpinDMMessageRequest({
    this.userId,
    this.messageId,
    this.status,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON><PERSON>(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @JsonKey(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final bool? status;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PinUnpinDMMessageRequest &&
          other.userId == userId &&
          other.messageId == messageId &&
          other.status == status;

  @override
  int get hashCode => userId.hashCode + messageId.hashCode + status.hashCode;

  factory V3PinUnpinDMMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3PinUnpinDMMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3PinUnpinDMMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
