//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_recovery_account_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateRecoveryAccountFlowRequest {
  /// Returns a new [V3InitiateRecoveryAccountFlowRequest] instance.
  V3InitiateRecoveryAccountFlowRequest({
    this.recoveryCode,
    this.reqChallenge,
    this.deviceId,
  });

  @JsonKey(
    name: r'recoveryCode',
    required: false,
    includeIfNull: false,
  )
  final String? recoveryCode;

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateRecoveryAccountFlowRequest &&
          other.recoveryCode == recoveryCode &&
          other.reqChallenge == reqChallenge &&
          other.deviceId == deviceId;

  @override
  int get hashCode =>
      recoveryCode.hashCode + reqChallenge.hashCode + deviceId.hashCode;

  factory V3InitiateRecoveryAccountFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateRecoveryAccountFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateRecoveryAccountFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
