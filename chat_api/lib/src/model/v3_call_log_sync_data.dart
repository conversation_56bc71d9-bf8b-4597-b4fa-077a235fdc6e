//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_call_state_enum.dart';
import 'package:chat_api/src/model/v3_call_ended_reason_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_call_log_sync_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CallLogSyncData {
  /// Returns a new [V3CallLogSyncData] instance.
  V3CallLogSyncData({
    this.id,
    this.version,
    this.source_,
    this.callerId,
    this.calleeId,
    this.callState,
    this.endedReason,
    this.callTimeInSeconds,
    this.isOutgoing,
    this.readTime,
    this.endedTime,
    this.createTime,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  @Json<PERSON><PERSON>(
    name: r'version',
    required: false,
    includeIfNull: false,
  )
  final int? version;

  @Json<PERSON><PERSON>(
    name: r'source',
    required: false,
    includeIfNull: false,
  )
  final String? source_;

  @JsonKey(
    name: r'callerId',
    required: false,
    includeIfNull: false,
  )
  final String? callerId;

  @JsonKey(
    name: r'calleeId',
    required: false,
    includeIfNull: false,
  )
  final String? calleeId;

  @JsonKey(
    name: r'callState',
    required: false,
    includeIfNull: false,
  )
  final V3CallStateEnum? callState;

  @JsonKey(
    name: r'endedReason',
    required: false,
    includeIfNull: false,
  )
  final V3CallEndedReasonEnum? endedReason;

  @JsonKey(
    name: r'callTimeInSeconds',
    required: false,
    includeIfNull: false,
  )
  final int? callTimeInSeconds;

  @JsonKey(
    name: r'isOutgoing',
    required: false,
    includeIfNull: false,
  )
  final bool? isOutgoing;

  @JsonKey(
    name: r'readTime',
    required: false,
    includeIfNull: false,
  )
  final String? readTime;

  @JsonKey(
    name: r'endedTime',
    required: false,
    includeIfNull: false,
  )
  final String? endedTime;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CallLogSyncData &&
          other.id == id &&
          other.version == version &&
          other.source_ == source_ &&
          other.callerId == callerId &&
          other.calleeId == calleeId &&
          other.callState == callState &&
          other.endedReason == endedReason &&
          other.callTimeInSeconds == callTimeInSeconds &&
          other.isOutgoing == isOutgoing &&
          other.readTime == readTime &&
          other.endedTime == endedTime &&
          other.createTime == createTime;

  @override
  int get hashCode =>
      id.hashCode +
      version.hashCode +
      source_.hashCode +
      callerId.hashCode +
      calleeId.hashCode +
      callState.hashCode +
      endedReason.hashCode +
      callTimeInSeconds.hashCode +
      isOutgoing.hashCode +
      readTime.hashCode +
      endedTime.hashCode +
      createTime.hashCode;

  factory V3CallLogSyncData.fromJson(Map<String, dynamic> json) =>
      _$V3CallLogSyncDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CallLogSyncDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
