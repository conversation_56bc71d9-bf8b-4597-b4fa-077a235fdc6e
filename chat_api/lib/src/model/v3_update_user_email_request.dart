//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_email_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserEmailRequest {
  /// Returns a new [V3UpdateUserEmailRequest] instance.
  V3UpdateUserEmailRequest({
    this.email,
  });

  @JsonKey(
    name: r'email',
    required: false,
    includeIfNull: false,
  )
  final String? email;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserEmailRequest && other.email == email;

  @override
  int get hashCode => email.hashCode;

  factory V3UpdateUserEmailRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserEmailRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserEmailRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
