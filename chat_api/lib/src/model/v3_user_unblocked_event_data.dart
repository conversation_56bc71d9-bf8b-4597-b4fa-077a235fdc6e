//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_unblocked_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserUnblockedEventData {
  /// Returns a new [V3UserUnblockedEventData] instance.
  V3UserUnblockedEventData({
    this.actorId,
    this.targetUserId,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'targetUserId',
    required: false,
    includeIfNull: false,
  )
  final String? targetUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserUnblockedEventData &&
          other.actorId == actorId &&
          other.targetUserId == targetUserId;

  @override
  int get hashCode => actorId.hashCode + targetUserId.hashCode;

  factory V3UserUnblockedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserUnblockedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserUnblockedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
