//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_search_channel_result.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_channels_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchChannelsResponse {
  /// Returns a new [V3SearchChannelsResponse] instance.
  V3SearchChannelsResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
  });

  /// Indicates whether the search operation was successful.
  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  /// An array of search result items containing channel information. Only have value when OK is true.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3SearchChannelResult>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchChannelsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + paging.hashCode;

  factory V3SearchChannelsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SearchChannelsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchChannelsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
