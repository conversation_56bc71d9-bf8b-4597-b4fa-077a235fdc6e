//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_verify_smart_otp_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3VerifySmartOtpRequest {
  /// Returns a new [V3VerifySmartOtpRequest] instance.
  V3VerifySmartOtpRequest({
    this.reqId,
    this.nonce,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @J<PERSON><PERSON>ey(
    name: r'nonce',
    required: false,
    includeIfNull: false,
  )
  final String? nonce;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3VerifySmartOtpRequest &&
          other.reqId == reqId &&
          other.nonce == nonce;

  @override
  int get hashCode => reqId.hashCode + nonce.hashCode;

  factory V3VerifySmartOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$V3VerifySmartOtpRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3VerifySmartOtpRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
