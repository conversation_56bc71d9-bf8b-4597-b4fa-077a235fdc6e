//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_avatar_upload_failed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelAvatarUploadFailedEventData {
  /// Returns a new [V3ChannelAvatarUploadFailedEventData] instance.
  V3ChannelAvatarUploadFailedEventData({
    this.workspaceId,
    this.channelId,
    this.reason,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'reason',
    required: false,
    includeIfNull: false,
  )
  final String? reason;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelAvatarUploadFailedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.reason == reason;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + reason.hashCode;

  factory V3ChannelAvatarUploadFailedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3ChannelAvatarUploadFailedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ChannelAvatarUploadFailedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
