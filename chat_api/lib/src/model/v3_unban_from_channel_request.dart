//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_unban_from_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UnbanFromChannelRequest {
  /// Returns a new [V3UnbanFromChannelRequest] instance.
  V3UnbanFromChannelRequest({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UnbanFromChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + userId.hashCode;

  factory V3UnbanFromChannelRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UnbanFromChannelRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UnbanFromChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
