//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/sharedv3_channel_data.dart';
import 'package:chat_api/src/model/sync_all_channels_response_channel_identification.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sync_all_channels_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SyncAllChannelsResponse {
  /// Returns a new [V3SyncAllChannelsResponse] instance.
  V3SyncAllChannelsResponse({
    this.data,
    this.channelDeleted,
    this.syncTime,
    this.includes,
  });

  /// List of values for the Channel interface.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<Sharedv3ChannelData>? data;

  /// List of channelIds deleted.
  @Json<PERSON>ey(
    name: r'channelDeleted',
    required: false,
    includeIfNull: false,
  )
  final List<SyncAllChannelsResponseChannelIdentification>? channelDeleted;

  @JsonKey(
    name: r'syncTime',
    required: false,
    includeIfNull: false,
  )
  final String? syncTime;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SyncAllChannelsResponse &&
          other.data == data &&
          other.channelDeleted == channelDeleted &&
          other.syncTime == syncTime &&
          other.includes == includes;

  @override
  int get hashCode =>
      data.hashCode +
      channelDeleted.hashCode +
      syncTime.hashCode +
      includes.hashCode;

  factory V3SyncAllChannelsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3SyncAllChannelsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3SyncAllChannelsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
