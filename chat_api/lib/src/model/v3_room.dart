//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_timed_version.dart';
import 'package:chat_api/src/model/v3_codec.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_room.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Room {
  /// Returns a new [V3Room] instance.
  V3Room({
    this.sid,
    this.name,
    this.emptyTimeout,
    this.departureTimeout,
    this.maxParticipants,
    this.creationTime,
    this.creationTimeMs,
    this.turnPassword,
    this.enabledCodecs,
    this.metadata,
    this.numParticipants,
    this.numPublishers,
    this.activeRecording,
    this.version,
    this.isStart,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'sid',
    required: false,
    includeIfNull: false,
  )
  final String? sid;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @Json<PERSON>ey(
    name: r'emptyTimeout',
    required: false,
    includeIfNull: false,
  )
  final int? emptyTimeout;

  @JsonKey(
    name: r'departureTimeout',
    required: false,
    includeIfNull: false,
  )
  final int? departureTimeout;

  @JsonKey(
    name: r'maxParticipants',
    required: false,
    includeIfNull: false,
  )
  final int? maxParticipants;

  @JsonKey(
    name: r'creationTime',
    required: false,
    includeIfNull: false,
  )
  final String? creationTime;

  @JsonKey(
    name: r'creationTimeMs',
    required: false,
    includeIfNull: false,
  )
  final String? creationTimeMs;

  @JsonKey(
    name: r'turnPassword',
    required: false,
    includeIfNull: false,
  )
  final String? turnPassword;

  @JsonKey(
    name: r'enabledCodecs',
    required: false,
    includeIfNull: false,
  )
  final List<V3Codec>? enabledCodecs;

  @JsonKey(
    name: r'metadata',
    required: false,
    includeIfNull: false,
  )
  final String? metadata;

  @JsonKey(
    name: r'numParticipants',
    required: false,
    includeIfNull: false,
  )
  final int? numParticipants;

  @JsonKey(
    name: r'numPublishers',
    required: false,
    includeIfNull: false,
  )
  final int? numPublishers;

  @JsonKey(
    name: r'activeRecording',
    required: false,
    includeIfNull: false,
  )
  final bool? activeRecording;

  @JsonKey(
    name: r'version',
    required: false,
    includeIfNull: false,
  )
  final V3TimedVersion? version;

  @JsonKey(
    name: r'isStart',
    required: false,
    includeIfNull: false,
  )
  final bool? isStart;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Room &&
          other.sid == sid &&
          other.name == name &&
          other.emptyTimeout == emptyTimeout &&
          other.departureTimeout == departureTimeout &&
          other.maxParticipants == maxParticipants &&
          other.creationTime == creationTime &&
          other.creationTimeMs == creationTimeMs &&
          other.turnPassword == turnPassword &&
          other.enabledCodecs == enabledCodecs &&
          other.metadata == metadata &&
          other.numParticipants == numParticipants &&
          other.numPublishers == numPublishers &&
          other.activeRecording == activeRecording &&
          other.version == version &&
          other.isStart == isStart;

  @override
  int get hashCode =>
      sid.hashCode +
      name.hashCode +
      emptyTimeout.hashCode +
      departureTimeout.hashCode +
      maxParticipants.hashCode +
      creationTime.hashCode +
      creationTimeMs.hashCode +
      turnPassword.hashCode +
      enabledCodecs.hashCode +
      metadata.hashCode +
      numParticipants.hashCode +
      numPublishers.hashCode +
      activeRecording.hashCode +
      version.hashCode +
      isStart.hashCode;

  factory V3Room.fromJson(Map<String, dynamic> json) => _$V3RoomFromJson(json);

  Map<String, dynamic> toJson() => _$V3RoomToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
