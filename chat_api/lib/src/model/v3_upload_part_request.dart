//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_upload_chunk.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_upload_part_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UploadPartRequest {
  /// Returns a new [V3UploadPartRequest] instance.
  V3UploadPartRequest({
    this.uploadId,
    this.chunk,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'uploadId',
    required: false,
    includeIfNull: false,
  )
  final String? uploadId;

  @JsonKey(
    name: r'chunk',
    required: false,
    includeIfNull: false,
  )
  final V3UploadChunk? chunk;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UploadPartRequest &&
          other.uploadId == uploadId &&
          other.chunk == chunk;

  @override
  int get hashCode => uploadId.hashCode + chunk.hashCode;

  factory V3UploadPartRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UploadPartRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UploadPartRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
