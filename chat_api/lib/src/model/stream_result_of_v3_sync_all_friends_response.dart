//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/v3_sync_all_friends_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'stream_result_of_v3_sync_all_friends_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class StreamResultOfV3SyncAllFriendsResponse {
  /// Returns a new [StreamResultOfV3SyncAllFriendsResponse] instance.
  StreamResultOfV3SyncAllFriendsResponse({
    this.result,
    this.error,
  });

  @JsonKey(
    name: r'result',
    required: false,
    includeIfNull: false,
  )
  final V3SyncAllFriendsResponse? result;

  @Json<PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final RpcStatus? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreamResultOfV3SyncAllFriendsResponse &&
          other.result == result &&
          other.error == error;

  @override
  int get hashCode => result.hashCode + error.hashCode;

  factory StreamResultOfV3SyncAllFriendsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$StreamResultOfV3SyncAllFriendsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$StreamResultOfV3SyncAllFriendsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
