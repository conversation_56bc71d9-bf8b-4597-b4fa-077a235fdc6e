//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_forward_messages_to_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ForwardMessagesToChannelRequest {
  /// Returns a new [V3ForwardMessagesToChannelRequest] instance.
  V3ForwardMessagesToChannelRequest({
    this.workspaceId,
    this.channelId,
    this.originalMessageIds,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'originalMessageIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? originalMessageIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ForwardMessagesToChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.originalMessageIds == originalMessageIds;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + originalMessageIds.hashCode;

  factory V3ForwardMessagesToChannelRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3ForwardMessagesToChannelRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ForwardMessagesToChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
