//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_profile.dart';
import 'package:chat_api/src/model/v3_user_setting.dart';
import 'package:chat_api/src/model/v3_user_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_me.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Me {
  /// Returns a new [V3Me] instance.
  V3Me({
    this.userId,
    this.username,
    this.createTime,
    this.updateTime,
    this.email,
    this.phoneNumber,
    this.userConnectLink,
    this.globalNotificationStatus,
    this.profile,
    this.setting,
    this.statusData,
    this.sipAddress,
    this.sipCredentials,
  });

  @<PERSON>son<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @J<PERSON><PERSON><PERSON>(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @JsonKey(
    name: r'email',
    required: false,
    includeIfNull: false,
  )
  final String? email;

  @JsonKey(
    name: r'phoneNumber',
    required: false,
    includeIfNull: false,
  )
  final String? phoneNumber;

  @JsonKey(
    name: r'userConnectLink',
    required: false,
    includeIfNull: false,
  )
  final String? userConnectLink;

  @JsonKey(
    name: r'globalNotificationStatus',
    required: false,
    includeIfNull: false,
  )
  final bool? globalNotificationStatus;

  @JsonKey(
    name: r'profile',
    required: false,
    includeIfNull: false,
  )
  final V3Profile? profile;

  @JsonKey(
    name: r'setting',
    required: false,
    includeIfNull: false,
  )
  final V3UserSetting? setting;

  @JsonKey(
    name: r'statusData',
    required: false,
    includeIfNull: false,
  )
  final V3UserStatus? statusData;

  @JsonKey(
    name: r'sipAddress',
    required: false,
    includeIfNull: false,
  )
  final String? sipAddress;

  @JsonKey(
    name: r'sipCredentials',
    required: false,
    includeIfNull: false,
  )
  final String? sipCredentials;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Me &&
          other.userId == userId &&
          other.username == username &&
          other.createTime == createTime &&
          other.updateTime == updateTime &&
          other.email == email &&
          other.phoneNumber == phoneNumber &&
          other.userConnectLink == userConnectLink &&
          other.globalNotificationStatus == globalNotificationStatus &&
          other.profile == profile &&
          other.setting == setting &&
          other.statusData == statusData &&
          other.sipAddress == sipAddress &&
          other.sipCredentials == sipCredentials;

  @override
  int get hashCode =>
      userId.hashCode +
      username.hashCode +
      createTime.hashCode +
      updateTime.hashCode +
      email.hashCode +
      phoneNumber.hashCode +
      userConnectLink.hashCode +
      globalNotificationStatus.hashCode +
      profile.hashCode +
      setting.hashCode +
      statusData.hashCode +
      sipAddress.hashCode +
      sipCredentials.hashCode;

  factory V3Me.fromJson(Map<String, dynamic> json) => _$V3MeFromJson(json);

  Map<String, dynamic> toJson() => _$V3MeToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
