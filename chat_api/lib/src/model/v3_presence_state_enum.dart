//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3PresenceStateEnum {
  @JsonValue(0)
  PRESENCE_STATUS_UNSPECIFIED('0'),
  @JsonValue(1)
  PRESENCE_STATUS_ONLINE('1'),
  @JsonValue(2)
  PRESENCE_STATUS_IDLE('2'),
  @JsonValue(3)
  PRESENCE_STATUS_DO_NOT_DISTURB('3'),
  @JsonValue(4)
  PRESENCE_STATUS_OFFLINE('4'),
  @JsonValue(5)
  PRESENCE_STATUS_OTHER('5');

  const V3PresenceStateEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
