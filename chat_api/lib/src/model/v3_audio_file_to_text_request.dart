//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_ssml_voice_gender_enum.dart';
import 'package:chat_api/src/model/v3_audio_text_to_speech_encoding_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_audio_file_to_text_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AudioFileToTextRequest {
  /// Returns a new [V3AudioFileToTextRequest] instance.
  V3AudioFileToTextRequest({
    this.fileBuffer,
    this.languageCode,
    this.type,
    this.sampleRateHertz,
    this.isTranslate,
    this.targetTranslateLanguage,
    this.isReturnAudio,
    this.genderAudio,
    this.audioEncoding,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'fileBuffer',
    required: false,
    includeIfNull: false,
  )
  final String? fileBuffer;

  @JsonKey(
    name: r'languageCode',
    required: false,
    includeIfNull: false,
  )
  final String? languageCode;

  @JsonKey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final String? type;

  @JsonKey(
    name: r'sampleRateHertz',
    required: false,
    includeIfNull: false,
  )
  final int? sampleRateHertz;

  @JsonKey(
    name: r'isTranslate',
    required: false,
    includeIfNull: false,
  )
  final bool? isTranslate;

  @JsonKey(
    name: r'targetTranslateLanguage',
    required: false,
    includeIfNull: false,
  )
  final String? targetTranslateLanguage;

  @JsonKey(
    name: r'isReturnAudio',
    required: false,
    includeIfNull: false,
  )
  final bool? isReturnAudio;

  @JsonKey(
    name: r'genderAudio',
    required: false,
    includeIfNull: false,
  )
  final V3SsmlVoiceGenderEnum? genderAudio;

  @JsonKey(
    name: r'audioEncoding',
    required: false,
    includeIfNull: false,
  )
  final V3AudioTextToSpeechEncodingEnum? audioEncoding;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AudioFileToTextRequest &&
          other.fileBuffer == fileBuffer &&
          other.languageCode == languageCode &&
          other.type == type &&
          other.sampleRateHertz == sampleRateHertz &&
          other.isTranslate == isTranslate &&
          other.targetTranslateLanguage == targetTranslateLanguage &&
          other.isReturnAudio == isReturnAudio &&
          other.genderAudio == genderAudio &&
          other.audioEncoding == audioEncoding;

  @override
  int get hashCode =>
      fileBuffer.hashCode +
      languageCode.hashCode +
      type.hashCode +
      sampleRateHertz.hashCode +
      isTranslate.hashCode +
      targetTranslateLanguage.hashCode +
      isReturnAudio.hashCode +
      genderAudio.hashCode +
      audioEncoding.hashCode;

  factory V3AudioFileToTextRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AudioFileToTextRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AudioFileToTextRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
