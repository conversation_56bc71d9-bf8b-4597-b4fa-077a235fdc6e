//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_room.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_meeting_room_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetMeetingRoomResponse {
  /// Returns a new [V3GetMeetingRoomResponse] instance.
  V3GetMeetingRoomResponse({
    this.ok,
    this.data,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3Room? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetMeetingRoomResponse && other.ok == ok && other.data == data;

  @override
  int get hashCode => ok.hashCode + data.hashCode;

  factory V3GetMeetingRoomResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetMeetingRoomResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetMeetingRoomResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
