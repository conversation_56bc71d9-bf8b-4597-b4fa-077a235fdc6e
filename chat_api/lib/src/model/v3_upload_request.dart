//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_upload_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UploadRequest {
  /// Returns a new [V3UploadRequest] instance.
  V3UploadRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.userId,
    this.fileId,
    this.filename,
    this.collectionId,
    this.attachmentType,
    this.fileRef,
    this.content,
    this.contentLocale,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @Json<PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'fileId',
    required: false,
    includeIfNull: false,
  )
  final String? fileId;

  @JsonKey(
    name: r'filename',
    required: false,
    includeIfNull: false,
  )
  final String? filename;

  @JsonKey(
    name: r'collectionId',
    required: false,
    includeIfNull: false,
  )
  final String? collectionId;

  @JsonKey(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @JsonKey(
    name: r'fileRef',
    required: false,
    includeIfNull: false,
  )
  final String? fileRef;

  @JsonKey(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @JsonKey(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UploadRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.userId == userId &&
          other.fileId == fileId &&
          other.filename == filename &&
          other.collectionId == collectionId &&
          other.attachmentType == attachmentType &&
          other.fileRef == fileRef &&
          other.content == content &&
          other.contentLocale == contentLocale;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      userId.hashCode +
      fileId.hashCode +
      filename.hashCode +
      collectionId.hashCode +
      attachmentType.hashCode +
      fileRef.hashCode +
      content.hashCode +
      contentLocale.hashCode;

  factory V3UploadRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UploadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UploadRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
