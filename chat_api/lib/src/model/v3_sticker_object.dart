//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_sticker_object.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3StickerObject {
  /// Returns a new [V3StickerObject] instance.
  V3StickerObject({
    this.collectionId,
    this.stickerId,
    this.attachmentType,
    this.stickerUrl,
    this.attachmentId,
    this.fileRef,
  });

  @Json<PERSON>ey(
    name: r'collectionId',
    required: false,
    includeIfNull: false,
  )
  final String? collectionId;

  @JsonKey(
    name: r'stickerId',
    required: false,
    includeIfNull: false,
  )
  final String? stickerId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @J<PERSON><PERSON><PERSON>(
    name: r'stickerUrl',
    required: false,
    includeIfNull: false,
  )
  final String? stickerUrl;

  @J<PERSON><PERSON><PERSON>(
    name: r'attachmentId',
    required: false,
    includeIfNull: false,
  )
  final String? attachmentId;

  @JsonKey(
    name: r'fileRef',
    required: false,
    includeIfNull: false,
  )
  final String? fileRef;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3StickerObject &&
          other.collectionId == collectionId &&
          other.stickerId == stickerId &&
          other.attachmentType == attachmentType &&
          other.stickerUrl == stickerUrl &&
          other.attachmentId == attachmentId &&
          other.fileRef == fileRef;

  @override
  int get hashCode =>
      collectionId.hashCode +
      stickerId.hashCode +
      attachmentType.hashCode +
      stickerUrl.hashCode +
      attachmentId.hashCode +
      fileRef.hashCode;

  factory V3StickerObject.fromJson(Map<String, dynamic> json) =>
      _$V3StickerObjectFromJson(json);

  Map<String, dynamic> toJson() => _$V3StickerObjectToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
