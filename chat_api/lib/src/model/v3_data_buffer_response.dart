//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_data_buffer_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DataBufferResponse {
  /// Returns a new [V3DataBufferResponse] instance.
  V3DataBufferResponse({
    this.originalText,
    this.translateText,
    this.bufferFile,
  });

  @JsonKey(
    name: r'originalText',
    required: false,
    includeIfNull: false,
  )
  final String? originalText;

  @Json<PERSON>ey(
    name: r'translateText',
    required: false,
    includeIfNull: false,
  )
  final String? translateText;

  @Json<PERSON>ey(
    name: r'bufferFile',
    required: false,
    includeIfNull: false,
  )
  final String? bufferFile;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DataBufferResponse &&
          other.originalText == originalText &&
          other.translateText == translateText &&
          other.bufferFile == bufferFile;

  @override
  int get hashCode =>
      originalText.hashCode + translateText.hashCode + bufferFile.hashCode;

  factory V3DataBufferResponse.fromJson(Map<String, dynamic> json) =>
      _$V3DataBufferResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3DataBufferResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
