//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_generic_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_unsubscribe_all_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UnsubscribeAllResponse {
  /// Returns a new [V3UnsubscribeAllResponse] instance.
  V3UnsubscribeAllResponse({
    this.ok,
    this.error,
    this.data,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3GenericData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UnsubscribeAllResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3UnsubscribeAllResponse.fromJson(Map<String, dynamic> json) =>
      _$V3UnsubscribeAllResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3UnsubscribeAllResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
