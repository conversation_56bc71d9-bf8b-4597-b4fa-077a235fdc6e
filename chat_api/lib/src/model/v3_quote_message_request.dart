//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_quote_message_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3QuoteMessageRequest {
  /// Returns a new [V3QuoteMessageRequest] instance.
  V3QuoteMessageRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.content,
    this.ref,
    this.contentLocale,
  });

  @<PERSON>son<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @J<PERSON><PERSON><PERSON>(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  /// Is a random value created by the client, which is used as a similar attribute to the local ID
  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @JsonKey(
    name: r'contentLocale',
    required: false,
    includeIfNull: false,
  )
  final String? contentLocale;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3QuoteMessageRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.content == content &&
          other.ref == ref &&
          other.contentLocale == contentLocale;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      content.hashCode +
      ref.hashCode +
      contentLocale.hashCode;

  factory V3QuoteMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$V3QuoteMessageRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3QuoteMessageRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
