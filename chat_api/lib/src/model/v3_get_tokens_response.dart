//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_get_tokens_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3GetTokensResponse {
  /// Returns a new [V3GetTokensResponse] instance.
  V3GetTokensResponse({
    this.ok,
    this.data,
    this.error,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<String>? data;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3GetTokensResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3GetTokensResponse.fromJson(Map<String, dynamic> json) =>
      _$V3GetTokensResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3GetTokensResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
