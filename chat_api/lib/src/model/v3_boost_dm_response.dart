//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_boost_dm_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3BoostDMResponse {
  /// Returns a new [V3BoostDMResponse] instance.
  V3BoostDMResponse({
    this.ok,
    this.error,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3BoostDMResponse && other.ok == ok && other.error == error;

  @override
  int get hashCode => ok.hashCode + error.hashCode;

  factory V3BoostDMResponse.fromJson(Map<String, dynamic> json) =>
      _$V3BoostDMResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3BoostDMResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
