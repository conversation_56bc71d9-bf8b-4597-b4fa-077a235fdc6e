//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_register_vo_ip_token_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RegisterVoIPTokenRequest {
  /// Returns a new [V3RegisterVoIPTokenRequest] instance.
  V3RegisterVoIPTokenRequest({
    this.appId,
    this.voipToken,
  });

  /// The ID of the application associated with the channel.
  @JsonKey(
    name: r'appId',
    required: false,
    includeIfNull: false,
  )
  final String? appId;

  /// A token associated with the device making the subscription request.
  @JsonKey(
    name: r'voipToken',
    required: false,
    includeIfNull: false,
  )
  final String? voipToken;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RegisterVoIPTokenRequest &&
          other.appId == appId &&
          other.voipToken == voipToken;

  @override
  int get hashCode => appId.hashCode + voipToken.hashCode;

  factory V3RegisterVoIPTokenRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RegisterVoIPTokenRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RegisterVoIPTokenRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
