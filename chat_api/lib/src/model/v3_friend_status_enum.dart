//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - FRIEND_STATUS_ENUM_UNSPECIFIED: UNSPECIFIED: default value  - FRIEND_STATUS_ENUM_NOT_FRIEND: NOT_FRIEND: Two user are not friends.  - FRIEND_STATUS_ENUM_REQUEST_SENT: SENT: At least one of two users has sent a friend request to the other user.  - FRIEND_STATUS_ENUM_REQUEST_RECEIVED: RECEIVED: At least one of two users has received a friend request sent by the other user.  - FRIEND_STATUS_ENUM_REQUEST_DELETED: DELETED: At user received delete a friend request  - FRIEND_STATUS_ENUM_FRIEND: FRIEND: Two user are friends.
enum V3FriendStatusEnum {
  /// - FRIEND_STATUS_ENUM_UNSPECIFIED: UNSPECIFIED: default value  - FRIEND_STATUS_ENUM_NOT_FRIEND: NOT_FRIEND: Two user are not friends.  - FRIEND_STATUS_ENUM_REQUEST_SENT: SENT: At least one of two users has sent a friend request to the other user.  - FRIEND_STATUS_ENUM_REQUEST_RECEIVED: RECEIVED: At least one of two users has received a friend request sent by the other user.  - FRIEND_STATUS_ENUM_REQUEST_DELETED: DELETED: At user received delete a friend request  - FRIEND_STATUS_ENUM_FRIEND: FRIEND: Two user are friends.
  @JsonValue(0)
  FRIEND_STATUS_ENUM_UNSPECIFIED('0'),

  /// - FRIEND_STATUS_ENUM_UNSPECIFIED: UNSPECIFIED: default value  - FRIEND_STATUS_ENUM_NOT_FRIEND: NOT_FRIEND: Two user are not friends.  - FRIEND_STATUS_ENUM_REQUEST_SENT: SENT: At least one of two users has sent a friend request to the other user.  - FRIEND_STATUS_ENUM_REQUEST_RECEIVED: RECEIVED: At least one of two users has received a friend request sent by the other user.  - FRIEND_STATUS_ENUM_REQUEST_DELETED: DELETED: At user received delete a friend request  - FRIEND_STATUS_ENUM_FRIEND: FRIEND: Two user are friends.
  @JsonValue(1)
  FRIEND_STATUS_ENUM_NOT_FRIEND('1'),

  /// - FRIEND_STATUS_ENUM_UNSPECIFIED: UNSPECIFIED: default value  - FRIEND_STATUS_ENUM_NOT_FRIEND: NOT_FRIEND: Two user are not friends.  - FRIEND_STATUS_ENUM_REQUEST_SENT: SENT: At least one of two users has sent a friend request to the other user.  - FRIEND_STATUS_ENUM_REQUEST_RECEIVED: RECEIVED: At least one of two users has received a friend request sent by the other user.  - FRIEND_STATUS_ENUM_REQUEST_DELETED: DELETED: At user received delete a friend request  - FRIEND_STATUS_ENUM_FRIEND: FRIEND: Two user are friends.
  @JsonValue(2)
  FRIEND_STATUS_ENUM_REQUEST_SENT('2'),

  /// - FRIEND_STATUS_ENUM_UNSPECIFIED: UNSPECIFIED: default value  - FRIEND_STATUS_ENUM_NOT_FRIEND: NOT_FRIEND: Two user are not friends.  - FRIEND_STATUS_ENUM_REQUEST_SENT: SENT: At least one of two users has sent a friend request to the other user.  - FRIEND_STATUS_ENUM_REQUEST_RECEIVED: RECEIVED: At least one of two users has received a friend request sent by the other user.  - FRIEND_STATUS_ENUM_REQUEST_DELETED: DELETED: At user received delete a friend request  - FRIEND_STATUS_ENUM_FRIEND: FRIEND: Two user are friends.
  @JsonValue(3)
  FRIEND_STATUS_ENUM_REQUEST_RECEIVED('3'),

  /// - FRIEND_STATUS_ENUM_UNSPECIFIED: UNSPECIFIED: default value  - FRIEND_STATUS_ENUM_NOT_FRIEND: NOT_FRIEND: Two user are not friends.  - FRIEND_STATUS_ENUM_REQUEST_SENT: SENT: At least one of two users has sent a friend request to the other user.  - FRIEND_STATUS_ENUM_REQUEST_RECEIVED: RECEIVED: At least one of two users has received a friend request sent by the other user.  - FRIEND_STATUS_ENUM_REQUEST_DELETED: DELETED: At user received delete a friend request  - FRIEND_STATUS_ENUM_FRIEND: FRIEND: Two user are friends.
  @JsonValue(4)
  FRIEND_STATUS_ENUM_REQUEST_DELETED('4'),

  /// - FRIEND_STATUS_ENUM_UNSPECIFIED: UNSPECIFIED: default value  - FRIEND_STATUS_ENUM_NOT_FRIEND: NOT_FRIEND: Two user are not friends.  - FRIEND_STATUS_ENUM_REQUEST_SENT: SENT: At least one of two users has sent a friend request to the other user.  - FRIEND_STATUS_ENUM_REQUEST_RECEIVED: RECEIVED: At least one of two users has received a friend request sent by the other user.  - FRIEND_STATUS_ENUM_REQUEST_DELETED: DELETED: At user received delete a friend request  - FRIEND_STATUS_ENUM_FRIEND: FRIEND: Two user are friends.
  @JsonValue(5)
  FRIEND_STATUS_ENUM_FRIEND('5');

  const V3FriendStatusEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
