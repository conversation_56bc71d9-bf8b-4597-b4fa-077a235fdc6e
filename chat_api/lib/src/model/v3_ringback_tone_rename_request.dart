//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_rename_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneRenameRequest {
  /// Returns a new [V3RingbackToneRenameRequest] instance.
  V3RingbackToneRenameRequest({
    this.ringbackToneId,
    this.name,
  });

  @JsonKey(
    name: r'ringbackToneId',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneId;

  @Json<PERSON>ey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneRenameRequest &&
          other.ringbackToneId == ringbackToneId &&
          other.name == name;

  @override
  int get hashCode => ringbackToneId.hashCode + name.hashCode;

  factory V3RingbackToneRenameRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneRenameRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RingbackToneRenameRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
