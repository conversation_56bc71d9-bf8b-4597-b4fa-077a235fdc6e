//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_visited_profile_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_user_visited_profile_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListUserVisitedProfileResponse {
  /// Returns a new [V3ListUserVisitedProfileResponse] instance.
  V3ListUserVisitedProfileResponse({
    this.ok,
    this.error,
    this.data,
    this.paging,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3VisitedProfileData>? data;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListUserVisitedProfileResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + paging.hashCode;

  factory V3ListUserVisitedProfileResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListUserVisitedProfileResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListUserVisitedProfileResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
