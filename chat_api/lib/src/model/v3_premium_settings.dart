//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/premium_settings_boosted.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_premium_settings.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3PremiumSettings {
  /// Returns a new [V3PremiumSettings] instance.
  V3PremiumSettings({
    this.boosted,
  });

  @JsonKey(
    name: r'boosted',
    required: false,
    includeIfNull: false,
  )
  final PremiumSettingsBoosted? boosted;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3PremiumSettings && other.boosted == boosted;

  @override
  int get hashCode => boosted.hashCode;

  factory V3PremiumSettings.fromJson(Map<String, dynamic> json) =>
      _$V3PremiumSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$V3PremiumSettingsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
