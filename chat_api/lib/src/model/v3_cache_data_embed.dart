//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cache_data_embed.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CacheDataEmbed {
  /// Returns a new [V3CacheDataEmbed] instance.
  V3CacheDataEmbed({
    this.url,
    this.width,
    this.height,
    this.checksum,
  });

  @Json<PERSON>ey(
    name: r'url',
    required: false,
    includeIfNull: false,
  )
  final String? url;

  @Json<PERSON>ey(
    name: r'width',
    required: false,
    includeIfNull: false,
  )
  final int? width;

  @JsonKey(
    name: r'height',
    required: false,
    includeIfNull: false,
  )
  final int? height;

  @JsonKey(
    name: r'checksum',
    required: false,
    includeIfNull: false,
  )
  final String? checksum;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CacheDataEmbed &&
          other.url == url &&
          other.width == width &&
          other.height == height &&
          other.checksum == checksum;

  @override
  int get hashCode =>
      url.hashCode + width.hashCode + height.hashCode + checksum.hashCode;

  factory V3CacheDataEmbed.fromJson(Map<String, dynamic> json) =>
      _$V3CacheDataEmbedFromJson(json);

  Map<String, dynamic> toJson() => _$V3CacheDataEmbedToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
