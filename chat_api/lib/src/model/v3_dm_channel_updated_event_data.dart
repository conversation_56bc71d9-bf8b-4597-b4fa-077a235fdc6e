//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_dm_channel_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DMChannelUpdatedEventData {
  /// Returns a new [V3DMChannelUpdatedEventData] instance.
  V3DMChannelUpdatedEventData({
    this.channel,
    this.includes,
  });

  @JsonKey(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3Channel? channel;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DMChannelUpdatedEventData &&
          other.channel == channel &&
          other.includes == includes;

  @override
  int get hashCode => channel.hashCode + includes.hashCode;

  factory V3DMChannelUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3DMChannelUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3DMChannelUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
