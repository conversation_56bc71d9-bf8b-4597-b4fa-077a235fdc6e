//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_add_message_reaction_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AddMessageReactionRequest {
  /// Returns a new [V3AddMessageReactionRequest] instance.
  V3AddMessageReactionRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.emoji,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @J<PERSON><PERSON><PERSON>(
    name: r'emoji',
    required: false,
    includeIfNull: false,
  )
  final String? emoji;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AddMessageReactionRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.emoji == emoji;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      emoji.hashCode;

  factory V3AddMessageReactionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AddMessageReactionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AddMessageReactionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
