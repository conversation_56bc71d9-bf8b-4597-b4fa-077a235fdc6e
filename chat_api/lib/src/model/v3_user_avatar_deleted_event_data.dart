//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_avatar_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_avatar_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserAvatarDeletedEventData {
  /// Returns a new [V3UserAvatarDeletedEventData] instance.
  V3UserAvatarDeletedEventData({
    this.actorId,
    this.avatarType,
  });

  @JsonKey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'avatarType',
    required: false,
    includeIfNull: false,
  )
  final V3UserAvatarTypeEnum? avatarType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserAvatarDeletedEventData &&
          other.actorId == actorId &&
          other.avatarType == avatarType;

  @override
  int get hashCode => actorId.hashCode + avatarType.hashCode;

  factory V3UserAvatarDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserAvatarDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserAvatarDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
