//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'common_auth_user.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonAuthUser {
  /// Returns a new [CommonAuthUser] instance.
  CommonAuthUser({
    this.id,
    this.name,
    this.displayName,
  });

  @JsonKey(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  @Json<PERSON>ey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @J<PERSON><PERSON><PERSON>(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonAuthUser &&
          other.id == id &&
          other.name == name &&
          other.displayName == displayName;

  @override
  int get hashCode => id.hashCode + name.hashCode + displayName.hashCode;

  factory CommonAuthUser.fromJson(Map<String, dynamic> json) =>
      _$CommonAuthUserFromJson(json);

  Map<String, dynamic> toJson() => _$CommonAuthUserToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
