//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_upload_decorated_avatar_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UploadDecoratedAvatarRequest {
  /// Returns a new [V3UploadDecoratedAvatarRequest] instance.
  V3UploadDecoratedAvatarRequest({
    this.avatarFrameId,
    this.decoratedAvatarPath,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'avatarFrameId',
    required: false,
    includeIfNull: false,
  )
  final String? avatarFrameId;

  @JsonKey(
    name: r'decoratedAvatarPath',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatarPath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UploadDecoratedAvatarRequest &&
          other.avatarFrameId == avatarFrameId &&
          other.decoratedAvatarPath == decoratedAvatarPath;

  @override
  int get hashCode => avatarFrameId.hashCode + decoratedAvatarPath.hashCode;

  factory V3UploadDecoratedAvatarRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UploadDecoratedAvatarRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UploadDecoratedAvatarRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
