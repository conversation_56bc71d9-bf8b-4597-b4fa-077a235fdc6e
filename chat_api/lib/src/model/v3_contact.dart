//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_contact.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3Contact {
  /// Returns a new [V3Contact] instance.
  V3Contact({
    this.name,
    this.phoneNumber,
    this.email,
  });

  @Json<PERSON>ey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @Json<PERSON>ey(
    name: r'phoneNumber',
    required: false,
    includeIfNull: false,
  )
  final String? phoneNumber;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'email',
    required: false,
    includeIfNull: false,
  )
  final String? email;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3Contact &&
          other.name == name &&
          other.phoneNumber == phoneNumber &&
          other.email == email;

  @override
  int get hashCode => name.hashCode + phoneNumber.hashCode + email.hashCode;

  factory V3Contact.fromJson(Map<String, dynamic> json) =>
      _$V3ContactFromJson(json);

  Map<String, dynamic> toJson() => _$V3ContactToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
