//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:chat_api/src/model/v3_channel_metadata.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sharedv3_channel_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class Sharedv3ChannelData {
  /// Returns a new [Sharedv3ChannelData] instance.
  Sharedv3ChannelData({
    this.channel,
    this.channelMetadata,
  });

  @JsonKey(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3Channel? channel;

  @JsonKey(
    name: r'channelMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelMetadata? channelMetadata;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Sharedv3ChannelData &&
          other.channel == channel &&
          other.channelMetadata == channelMetadata;

  @override
  int get hashCode => channel.hashCode + channelMetadata.hashCode;

  factory Sharedv3ChannelData.fromJson(Map<String, dynamic> json) =>
      _$Sharedv3ChannelDataFromJson(json);

  Map<String, dynamic> toJson() => _$Sharedv3ChannelDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
