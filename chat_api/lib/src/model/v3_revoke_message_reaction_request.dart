//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_revoke_message_reaction_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RevokeMessageReactionRequest {
  /// Returns a new [V3RevokeMessageReactionRequest] instance.
  V3RevokeMessageReactionRequest({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.emoji,
  });

  @Json<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @J<PERSON><PERSON><PERSON>(
    name: r'emoji',
    required: false,
    includeIfNull: false,
  )
  final String? emoji;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RevokeMessageReactionRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.emoji == emoji;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      emoji.hashCode;

  factory V3RevokeMessageReactionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RevokeMessageReactionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RevokeMessageReactionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
