//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_member_role.dart';
import 'package:chat_api/src/model/v3_user_badge_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_search_members_result.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SearchMembersResult {
  /// Returns a new [V3SearchMembersResult] instance.
  V3SearchMembersResult({
    this.channelId,
    this.userId,
    this.displayName,
    this.username,
    this.nickname,
    this.avatar,
    this.roles,
    this.workspaceId,
    this.userBadgeType,
    this.decoratedAvatar,
    this.videoAvatar,
  });

  @<PERSON>son<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  /// The unique identifier for the member.
  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  /// The display name of the member.
  @JsonKey(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  /// The username of the member.
  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @JsonKey(
    name: r'nickname',
    required: false,
    includeIfNull: false,
  )
  final String? nickname;

  /// The avatar image associated with the mention.
  @JsonKey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @JsonKey(
    name: r'roles',
    required: false,
    includeIfNull: false,
  )
  final List<V3MemberRole>? roles;

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'userBadgeType',
    required: false,
    includeIfNull: false,
  )
  final V3UserBadgeTypeEnum? userBadgeType;

  @JsonKey(
    name: r'decoratedAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? decoratedAvatar;

  @JsonKey(
    name: r'videoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? videoAvatar;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SearchMembersResult &&
          other.channelId == channelId &&
          other.userId == userId &&
          other.displayName == displayName &&
          other.username == username &&
          other.nickname == nickname &&
          other.avatar == avatar &&
          other.roles == roles &&
          other.workspaceId == workspaceId &&
          other.userBadgeType == userBadgeType &&
          other.decoratedAvatar == decoratedAvatar &&
          other.videoAvatar == videoAvatar;

  @override
  int get hashCode =>
      channelId.hashCode +
      userId.hashCode +
      displayName.hashCode +
      username.hashCode +
      nickname.hashCode +
      avatar.hashCode +
      roles.hashCode +
      workspaceId.hashCode +
      userBadgeType.hashCode +
      decoratedAvatar.hashCode +
      videoAvatar.hashCode;

  factory V3SearchMembersResult.fromJson(Map<String, dynamic> json) =>
      _$V3SearchMembersResultFromJson(json);

  Map<String, dynamic> toJson() => _$V3SearchMembersResultToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
