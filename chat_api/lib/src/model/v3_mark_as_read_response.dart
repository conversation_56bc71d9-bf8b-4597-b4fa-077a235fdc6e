//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_mark_as_read_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MarkAsReadResponse {
  /// Returns a new [V3MarkAsReadResponse] instance.
  V3MarkAsReadResponse({
    this.ok,
    this.totalNewMessages,
    this.error,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'totalNewMessages',
    required: false,
    includeIfNull: false,
  )
  final int? totalNewMessages;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MarkAsReadResponse &&
          other.ok == ok &&
          other.totalNewMessages == totalNewMessages &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + totalNewMessages.hashCode + error.hashCode;

  factory V3MarkAsReadResponse.fromJson(Map<String, dynamic> json) =>
      _$V3MarkAsReadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3MarkAsReadResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
