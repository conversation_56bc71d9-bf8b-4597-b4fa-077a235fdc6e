//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_audit_log.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AuditLog {
  /// Returns a new [V3AuditLog] instance.
  V3AuditLog({
    this.workspaceId,
    this.channelId,
    this.logId,
    this.content,
    this.contentArguments,
    this.userId,
    this.createTime,
    this.updateTime,
  });

  @<PERSON>son<PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'logId',
    required: false,
    includeIfNull: false,
  )
  final String? logId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'content',
    required: false,
    includeIfNull: false,
  )
  final String? content;

  @J<PERSON><PERSON><PERSON>(
    name: r'contentArguments',
    required: false,
    includeIfNull: false,
  )
  final List<String>? contentArguments;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AuditLog &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.logId == logId &&
          other.content == content &&
          other.contentArguments == contentArguments &&
          other.userId == userId &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      logId.hashCode +
      content.hashCode +
      contentArguments.hashCode +
      userId.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3AuditLog.fromJson(Map<String, dynamic> json) =>
      _$V3AuditLogFromJson(json);

  Map<String, dynamic> toJson() => _$V3AuditLogToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
