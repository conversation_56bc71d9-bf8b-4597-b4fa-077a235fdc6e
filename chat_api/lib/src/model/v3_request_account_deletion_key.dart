//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_request_account_deletion_key.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RequestAccountDeletionKey {
  /// Returns a new [V3RequestAccountDeletionKey] instance.
  V3RequestAccountDeletionKey({
    this.accountDeletionKey,
  });

  @JsonKey(
    name: r'accountDeletionKey',
    required: false,
    includeIfNull: false,
  )
  final String? accountDeletionKey;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RequestAccountDeletionKey &&
          other.accountDeletionKey == accountDeletionKey;

  @override
  int get hashCode => accountDeletionKey.hashCode;

  factory V3RequestAccountDeletionKey.fromJson(Map<String, dynamic> json) =>
      _$V3RequestAccountDeletionKeyFromJson(json);

  Map<String, dynamic> toJson() => _$V3RequestAccountDeletionKeyToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
