//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_member_banned_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MemberBannedEventData {
  /// Returns a new [V3MemberBannedEventData] instance.
  V3MemberBannedEventData({
    this.workspaceId,
    this.channelId,
    this.actorId,
    this.bannedUserId,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @Json<PERSON>ey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @Json<PERSON>ey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @J<PERSON><PERSON><PERSON>(
    name: r'bannedUserId',
    required: false,
    includeIfNull: false,
  )
  final String? bannedUserId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MemberBannedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.actorId == actorId &&
          other.bannedUserId == bannedUserId;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      actorId.hashCode +
      bannedUserId.hashCode;

  factory V3MemberBannedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3MemberBannedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3MemberBannedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
