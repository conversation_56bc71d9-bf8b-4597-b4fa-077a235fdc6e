//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_audio_metadata.dart';
import 'package:chat_api/src/model/v3_file_metadata.dart';
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_media_object.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MediaObject {
  /// Returns a new [V3MediaObject] instance.
  V3MediaObject({
    this.fileId,
    this.attachmentType,
    this.fileUrl,
    this.fileMetadata,
    this.thumbnailUrl,
    this.audioMetadata,
    this.fileRef,
    this.attachmentId,
    this.channelId,
    this.userId,
    this.messageId,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'fileId',
    required: false,
    includeIfNull: false,
  )
  final String? fileId;

  @Json<PERSON><PERSON>(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @JsonKey(
    name: r'fileUrl',
    required: false,
    includeIfNull: false,
  )
  final String? fileUrl;

  @JsonKey(
    name: r'fileMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3FileMetadata? fileMetadata;

  @JsonKey(
    name: r'thumbnailUrl',
    required: false,
    includeIfNull: false,
  )
  final String? thumbnailUrl;

  @JsonKey(
    name: r'audioMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3AudioMetadata? audioMetadata;

  @JsonKey(
    name: r'fileRef',
    required: false,
    includeIfNull: false,
  )
  final String? fileRef;

  @JsonKey(
    name: r'attachmentId',
    required: false,
    includeIfNull: false,
  )
  final String? attachmentId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MediaObject &&
          other.fileId == fileId &&
          other.attachmentType == attachmentType &&
          other.fileUrl == fileUrl &&
          other.fileMetadata == fileMetadata &&
          other.thumbnailUrl == thumbnailUrl &&
          other.audioMetadata == audioMetadata &&
          other.fileRef == fileRef &&
          other.attachmentId == attachmentId &&
          other.channelId == channelId &&
          other.userId == userId &&
          other.messageId == messageId;

  @override
  int get hashCode =>
      fileId.hashCode +
      attachmentType.hashCode +
      fileUrl.hashCode +
      fileMetadata.hashCode +
      thumbnailUrl.hashCode +
      audioMetadata.hashCode +
      fileRef.hashCode +
      attachmentId.hashCode +
      channelId.hashCode +
      userId.hashCode +
      messageId.hashCode;

  factory V3MediaObject.fromJson(Map<String, dynamic> json) =>
      _$V3MediaObjectFromJson(json);

  Map<String, dynamic> toJson() => _$V3MediaObjectToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
