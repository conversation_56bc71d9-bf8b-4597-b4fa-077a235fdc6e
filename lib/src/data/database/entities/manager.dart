import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';

/// Manager entity for storing manager settings
/// UID range: 5100-5106
@Entity()
class Manager {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 5100)
  String sessionKey = '';

  /// Whether all channels have been loaded
  @Property(uid: 5101)
  bool loadedAllChannels = false;

  /// Whether all friends have been loaded
  @Property(uid: 5102)
  bool loadedAllFriends = false;

  /// Whether all friend requests have been loaded
  @Property(uid: 5103)
  bool loadedAllFriendRequests = false;

  /// Whether message request warning has been closed
  @Property(uid: 5104)
  bool closedMessageRequestWarning = false;

  /// Whether list block user warning has been closed
  @Property(uid: 5105)
  bool closedListBlockUserWarning = false;

  /// User status emojis data
  @Property(uid: 5106)
  String userStatusEmojis = '';

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this manager to its parent session
  final session = ToOne<Session>();

  /// Default constructor
  Manager();

  /// Constructor with required fields
  Manager.create({
    required this.sessionKey,
    this.loadedAllChannels = false,
    this.loadedAllFriends = false,
    this.loadedAllFriendRequests = false,
    this.closedMessageRequestWarning = false,
    this.closedListBlockUserWarning = false,
    this.userStatusEmojis = '',
  });

  /// Copy constructor for updates
  Manager copyWith({
    int? id,
    String? sessionKey,
    bool? loadedAllChannels,
    bool? loadedAllFriends,
    bool? loadedAllFriendRequests,
    bool? closedMessageRequestWarning,
    bool? closedListBlockUserWarning,
    String? userStatusEmojis,
  }) {
    final newManager = Manager()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..loadedAllChannels = loadedAllChannels ?? this.loadedAllChannels
      ..loadedAllFriends = loadedAllFriends ?? this.loadedAllFriends
      ..loadedAllFriendRequests =
          loadedAllFriendRequests ?? this.loadedAllFriendRequests
      ..closedMessageRequestWarning =
          closedMessageRequestWarning ?? this.closedMessageRequestWarning
      ..closedListBlockUserWarning =
          closedListBlockUserWarning ?? this.closedListBlockUserWarning
      ..userStatusEmojis = userStatusEmojis ?? this.userStatusEmojis;

    // Copy relationship
    newManager.session.target = session.target;
    return newManager;
  }

  /// Mark all channels as loaded
  void markAllChannelsLoaded() {
    loadedAllChannels = true;
  }

  /// Mark all friends as loaded
  void markAllFriendsLoaded() {
    loadedAllFriends = true;
  }

  /// Mark all friend requests as loaded
  void markAllFriendRequestsLoaded() {
    loadedAllFriendRequests = true;
  }

  /// Close message request warning
  void closeMessageRequestWarning() {
    closedMessageRequestWarning = true;
  }

  /// Close list block user warning
  void closeListBlockUserWarning() {
    closedListBlockUserWarning = true;
  }

  /// Update user status emojis
  void updateUserStatusEmojis(String emojis) {
    userStatusEmojis = emojis;
  }

  /// Reset all loading states
  void resetLoadingStates() {
    loadedAllChannels = false;
    loadedAllFriends = false;
    loadedAllFriendRequests = false;
  }

  /// Check if all data has been loaded
  bool get isAllDataLoaded =>
      loadedAllChannels && loadedAllFriends && loadedAllFriendRequests;

  // RELATIONSHIP HELPER METHODS

  /// Get effective session key from relationship or fallback to stored value
  String get effectiveSessionKey => session.target?.sessionKey ?? sessionKey;

  /// Check if this manager has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Set session relationship by finding session with matching sessionKey
  /// This is a helper method for easier relationship management
  void setSessionByKey(String key, List<Session> availableSessions) {
    final matchingSessions =
        availableSessions.where((s) => s.sessionKey == key);

    if (matchingSessions.isNotEmpty) {
      final targetSession = matchingSessions.first;
      session.target = targetSession;
      sessionKey = key; // Keep string field in sync
    }
  }

  @override
  String toString() {
    return 'Manager{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'loadedAllChannels: $loadedAllChannels, '
        'loadedAllFriends: $loadedAllFriends, '
        'loadedAllFriendRequests: $loadedAllFriendRequests, '
        'closedMessageRequestWarning: $closedMessageRequestWarning, '
        'closedListBlockUserWarning: $closedListBlockUserWarning, '
        'userStatusEmojis: $userStatusEmojis'
        '}';
  }
}
