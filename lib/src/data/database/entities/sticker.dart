import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'collection.dart';
import 'sticker_frame_count.dart';

/// Sticker entity for storing sticker information
/// UID range: 8001-8010
@Entity()
class Sticker {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 8001)
  String sessionKey = '';

  /// Sticker ID (indexed)
  @Index()
  @Property(uid: 8002)
  String stickerId = '';

  /// Collection ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 8003)
  String collectionIdField = '';

  /// Sticker name
  @Property(uid: 8004)
  String name = '';

  /// Sticker URL
  @Property(uid: 8005)
  String url = '';

  /// Thumbnail URL
  @Property(uid: 8006)
  String thumbnailUrl = '';

  /// Sticker width
  @Property(uid: 8007)
  int width = 0;

  /// Sticker height
  @Property(uid: 8008)
  int height = 0;

  /// Whether sticker is animated
  @Property(uid: 8009)
  bool isAnimated = false;

  /// Frame count for animated stickers
  @Property(uid: 8010)
  int frameCount = 0;

  /// ToOne relationship to Collection
  /// Links to the collection this sticker belongs to
  final collection = ToOne<Collection>();

  /// ToOne relationship to StickerFrameCount
  /// Links to the frame count data for this sticker
  final frameCountData = ToOne<StickerFrameCount>();

  /// Get collectionId from relationship or field (backward compatibility)
  String get collectionId =>
      collection.target?.collectionId ?? collectionIdField;

  /// Default constructor
  Sticker();

  /// Constructor with required fields
  Sticker.create({
    required this.sessionKey,
    required this.stickerId,
    required String
        collectionId, // collectionId parameter for backward compatibility
    this.name = '',
    this.url = '',
    this.thumbnailUrl = '',
    this.width = 0,
    this.height = 0,
    this.isAnimated = false,
    this.frameCount = 0,
  }) : collectionIdField = collectionId;

  /// Copy constructor for updates
  Sticker copyWith({
    int? id,
    String? sessionKey,
    String? stickerId,
    String? collectionId, // collectionId parameter for backward compatibility
    String? name,
    String? url,
    String? thumbnailUrl,
    int? width,
    int? height,
    bool? isAnimated,
    int? frameCount,
  }) {
    final newSticker = Sticker()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..stickerId = stickerId ?? this.stickerId
      ..collectionIdField = collectionId ?? this.collectionIdField
      ..name = name ?? this.name
      ..url = url ?? this.url
      ..thumbnailUrl = thumbnailUrl ?? this.thumbnailUrl
      ..width = width ?? this.width
      ..height = height ?? this.height
      ..isAnimated = isAnimated ?? this.isAnimated
      ..frameCount = frameCount ?? this.frameCount;

    // Copy relationship
    newSticker.collection.target = collection.target;
    return newSticker;
  }

  /// Update sticker information
  void updateInfo({
    String? name,
    String? url,
    String? thumbnailUrl,
    int? width,
    int? height,
    bool? isAnimated,
    int? frameCount,
  }) {
    if (name != null) this.name = name;
    if (url != null) this.url = url;
    if (thumbnailUrl != null) this.thumbnailUrl = thumbnailUrl;
    if (width != null) this.width = width;
    if (height != null) this.height = height;
    if (isAnimated != null) this.isAnimated = isAnimated;
    if (frameCount != null) this.frameCount = frameCount;
  }

  /// Update frame count
  void updateFrameCount(int newFrameCount) {
    frameCount = newFrameCount;
    if (newFrameCount > 1) {
      isAnimated = true;
    }
  }

  /// Set dimensions
  void setDimensions(int width, int height) {
    this.width = width;
    this.height = height;
  }

  /// Check if sticker has URL
  bool get hasUrl => url.isNotEmpty;

  /// Check if sticker has thumbnail
  bool get hasThumbnail => thumbnailUrl.isNotEmpty;

  /// Check if sticker has name
  bool get hasName => name.isNotEmpty;

  /// Check if sticker has dimensions
  bool get hasDimensions => width > 0 && height > 0;

  /// Get aspect ratio
  double get aspectRatio {
    if (height == 0) return 1.0;
    return width / height;
  }

  /// Get effective URL (use thumbnail if main URL is empty)
  String get effectiveUrl => hasUrl ? url : thumbnailUrl;

  /// Check if sticker has relationship to collection
  bool get hasCollectionRelationship => collection.target != null;

  /// Get collection name from relationship
  String get collectionName => collection.target?.name ?? '';

  /// Check if sticker belongs to premium collection
  bool get isFromPremiumCollection => collection.target?.isPremium ?? false;

  /// Check if sticker has frame count data relationship
  bool get hasFrameCountData => frameCountData.target != null;

  /// Get frame count from relationship or field (backward compatibility)
  int get effectiveFrameCount =>
      frameCountData.target?.frameCount ?? frameCount;

  /// Check if frame count is synchronized with relationship
  bool get isFrameCountSynced =>
      frameCountData.target?.frameCount == frameCount;

  /// Synchronize frame count with relationship data
  void syncFrameCount() {
    if (frameCountData.target != null) {
      frameCount = frameCountData.target!.frameCount;
      if (frameCount > 1) {
        isAnimated = true;
      }
    }
  }

  @override
  String toString() {
    return 'Sticker{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'stickerId: $stickerId, '
        'collectionId: $collectionId, '
        'name: $name, '
        'url: $url, '
        'thumbnailUrl: $thumbnailUrl, '
        'width: $width, '
        'height: $height, '
        'isAnimated: $isAnimated, '
        'frameCount: $frameCount'
        '}';
  }
}
