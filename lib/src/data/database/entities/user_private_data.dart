import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'user.dart';

/// User private data entity for storing user-specific private data
/// UID range: 7100-7107
@Entity()
class UserPrivateData {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 7100)
  String sessionKey = '';

  /// User ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 7101)
  String userIdField = '';

  /// Alias name for the user
  @Property(uid: 7102)
  String aliasName = '';

  /// Version number
  @Property(uid: 7103)
  int version = 0;

  /// Source information
  @Property(uid: 7104)
  String source = '';

  /// DM ID
  @Property(uid: 7105)
  String dmId = '';

  /// Whether user is blocked
  @Property(uid: 7106)
  bool blocked = false;

  /// Notification status
  @Property(uid: 7107)
  bool notificationStatus = true;

  // RELATIONSHIPS

  /// ToOne relationship to User
  /// Links this private data to its user
  final user = ToOne<User>();

  /// Get userId from relationship or field (backward compatibility)
  String get userId => user.target?.userId ?? userIdField;

  /// Default constructor
  UserPrivateData();

  /// Constructor with required fields
  UserPrivateData.create({
    required this.sessionKey,
    required String userId, // userId parameter for backward compatibility
    this.aliasName = '',
    this.version = 0,
    this.source = '',
    this.dmId = '',
    this.blocked = false,
    this.notificationStatus = true,
  }) : userIdField = userId;

  /// Copy constructor for updates
  UserPrivateData copyWith({
    int? id,
    String? sessionKey,
    String? userId, // userId parameter for backward compatibility
    String? aliasName,
    int? version,
    String? source,
    String? dmId,
    bool? blocked,
    bool? notificationStatus,
  }) {
    final newUserPrivateData = UserPrivateData()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userIdField = userId ?? this.userIdField
      ..aliasName = aliasName ?? this.aliasName
      ..version = version ?? this.version
      ..source = source ?? this.source
      ..dmId = dmId ?? this.dmId
      ..blocked = blocked ?? this.blocked
      ..notificationStatus = notificationStatus ?? this.notificationStatus;

    // Copy relationship
    newUserPrivateData.user.target = user.target;
    return newUserPrivateData;
  }

  /// Block the user
  void blockUser() {
    blocked = true;
    version++;
  }

  /// Unblock the user
  void unblockUser() {
    blocked = false;
    version++;
  }

  /// Enable notifications
  void enableNotifications() {
    notificationStatus = true;
    version++;
  }

  /// Disable notifications
  void disableNotifications() {
    notificationStatus = false;
    version++;
  }

  /// Update alias name
  void updateAliasName(String newAliasName) {
    aliasName = newAliasName;
    version++;
  }

  /// Update DM ID
  void updateDmId(String newDmId) {
    dmId = newDmId;
    version++;
  }

  /// Check if user has alias
  bool get hasAlias => aliasName.isNotEmpty;

  /// Get effective display name (alias or user ID)
  String get effectiveDisplayName => hasAlias ? aliasName : userId;

  /// Check if private data has relationship to user
  bool get hasUserRelationship => user.target != null;

  /// Get user name from relationship
  String get userName => user.target?.username ?? '';

  /// Get user display name from relationship (from profile)
  String get userDisplayName {
    if (user.target?.profiles.isNotEmpty == true) {
      return user.target!.profiles.first.displayName;
    }
    return userName;
  }

  /// Get effective name priority: alias > user display name > username > userId
  String get effectiveName {
    if (hasAlias) return aliasName;
    if (userDisplayName.isNotEmpty) return userDisplayName;
    if (userName.isNotEmpty) return userName;
    return userId;
  }

  /// Check if user is online (from relationship)
  bool get isUserOnline {
    if (user.target?.presences.isNotEmpty == true) {
      final currentPresence = user.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Sync with user data
  void syncWithUser() {
    if (user.target != null) {
      // Update any sync logic here if needed
      version++;
    }
  }

  @override
  String toString() {
    return 'UserPrivateData{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'aliasName: $aliasName, '
        'version: $version, '
        'source: $source, '
        'dmId: $dmId, '
        'blocked: $blocked, '
        'notificationStatus: $notificationStatus'
        '}';
  }
}
