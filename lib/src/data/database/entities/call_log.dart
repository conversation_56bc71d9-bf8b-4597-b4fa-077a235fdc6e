import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';
import 'user.dart';
import 'call_log_private_data.dart';

/// Call log entity for storing call history
/// UID range: 9001-9014
@Entity()
class CallLog {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 9001)
  String sessionKeyField = '';

  /// Call ID (indexed)
  @Index()
  @Property(uid: 9002)
  String callId = '';

  /// Caller user ID (indexed, FIXED) - kept for backward compatibility
  @Index()
  @Property(uid: 9003)
  String callerIdField = '';

  /// Callee user ID (indexed, FIXED) - kept for backward compatibility
  @Index()
  @Property(uid: 9004)
  String calleeIdField = '';

  /// Call state
  @Property(uid: 9005)
  int callState = 0;

  /// Ended reason
  @Property(uid: 9006)
  int endedReason = 0;

  /// Call duration in seconds
  @Property(uid: 9007)
  int callTimeInSeconds = 0;

  /// Whether it's a missed call
  @Property(uid: 9008)
  bool isMissedCall = false;

  /// Whether it's an incoming call
  @Property(uid: 9009)
  bool isInComingCall = false;

  /// Whether it's an outgoing call
  @Property(uid: 9010)
  bool isOutgoing = false;

  /// Whether it's a video call
  @Property(uid: 9011)
  bool isVideoCall = false;

  /// Read time
  @Property(uid: 9012)
  DateTime? readTime;

  /// Call creation time
  @Property(uid: 9013)
  DateTime? createTime;

  /// Call ended time
  @Property(uid: 9014)
  DateTime? endedTime;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this call log to its parent session
  final session = ToOne<Session>();

  /// ToOne relationship to User (caller)
  /// Links this call log to the caller user
  final callerUser = ToOne<User>();

  /// ToOne relationship to User (callee)
  /// Links this call log to the callee user
  final calleeUser = ToOne<User>();

  /// Reverse relationship: CallLogPrivateData entities that reference this call log
  /// Links to all private data records for this call
  @Backlink('callLog')
  final privateDataRecords = ToMany<CallLogPrivateData>();

  /// Get sessionKey from relationship or field (backward compatibility)
  String get sessionKey => session.target?.sessionKey ?? sessionKeyField;

  /// Get callerId from relationship or field (backward compatibility)
  String get callerId => callerUser.target?.userId ?? callerIdField;

  /// Get calleeId from relationship or field (backward compatibility)
  String get calleeId => calleeUser.target?.userId ?? calleeIdField;

  /// Composite index field: callerCalleeTime
  String get callerCalleeTime =>
      '${callerId}_${calleeId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Composite index field: sessionCallerTime
  String get sessionCallerTime =>
      '${sessionKey}_${callerId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Default constructor
  CallLog();

  /// Constructor with required fields
  CallLog.create({
    required String
        sessionKey, // sessionKey parameter for backward compatibility
    required this.callId,
    required String callerId, // callerId parameter for backward compatibility
    required String calleeId, // calleeId parameter for backward compatibility
    this.callState = 0,
    this.endedReason = 0,
    this.callTimeInSeconds = 0,
    this.isMissedCall = false,
    this.isInComingCall = false,
    this.isOutgoing = false,
    this.isVideoCall = false,
    this.readTime,
    this.createTime,
    this.endedTime,
  })  : sessionKeyField = sessionKey,
        callerIdField = callerId,
        calleeIdField = calleeId {
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  CallLog copyWith({
    int? id,
    String? sessionKey, // sessionKey parameter for backward compatibility
    String? callId,
    String? callerId, // callerId parameter for backward compatibility
    String? calleeId, // calleeId parameter for backward compatibility
    int? callState,
    int? endedReason,
    int? callTimeInSeconds,
    bool? isMissedCall,
    bool? isInComingCall,
    bool? isOutgoing,
    bool? isVideoCall,
    DateTime? readTime,
    DateTime? createTime,
    DateTime? endedTime,
  }) {
    final newCallLog = CallLog()
      ..id = id ?? this.id
      ..sessionKeyField = sessionKey ?? this.sessionKeyField
      ..callId = callId ?? this.callId
      ..callerIdField = callerId ?? this.callerIdField
      ..calleeIdField = calleeId ?? this.calleeIdField
      ..callState = callState ?? this.callState
      ..endedReason = endedReason ?? this.endedReason
      ..callTimeInSeconds = callTimeInSeconds ?? this.callTimeInSeconds
      ..isMissedCall = isMissedCall ?? this.isMissedCall
      ..isInComingCall = isInComingCall ?? this.isInComingCall
      ..isOutgoing = isOutgoing ?? this.isOutgoing
      ..isVideoCall = isVideoCall ?? this.isVideoCall
      ..readTime = readTime ?? this.readTime
      ..createTime = createTime ?? this.createTime
      ..endedTime = endedTime ?? this.endedTime;

    // Copy relationships
    newCallLog.session.target = session.target;
    newCallLog.callerUser.target = callerUser.target;
    newCallLog.calleeUser.target = calleeUser.target;
    return newCallLog;
  }

  /// End the call
  void endCall({int? endedReason, int? callTimeInSeconds}) {
    this.endedReason = endedReason ?? 0;
    this.callTimeInSeconds = callTimeInSeconds ?? 0;
    endedTime = DateTime.now();
  }

  /// Mark as read
  void markAsRead() {
    readTime = DateTime.now();
  }

  /// Get call duration as formatted string
  String get formattedDuration {
    final hours = callTimeInSeconds ~/ 3600;
    final minutes = (callTimeInSeconds % 3600) ~/ 60;
    final seconds = callTimeInSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Check if call is active
  bool get isActive => callState == 1; // Assuming 1 means active

  /// Check if call is ended
  bool get isEnded => endedTime != null;

  // RELATIONSHIP HELPER METHODS

  /// Check if this call log has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Check if this call log has a valid caller relationship
  bool get hasCallerRelationship => callerUser.target != null;

  /// Check if this call log has a valid callee relationship
  bool get hasCalleeRelationship => calleeUser.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Get caller username from relationship
  String get callerUsername => callerUser.target?.username ?? '';

  /// Get callee username from relationship
  String get calleeUsername => calleeUser.target?.username ?? '';

  /// Get caller display name from relationship (from profile)
  String get callerDisplayName {
    if (callerUser.target?.profiles.isNotEmpty == true) {
      return callerUser.target!.profiles.first.displayName;
    }
    return callerUsername;
  }

  /// Get callee display name from relationship (from profile)
  String get calleeDisplayName {
    if (calleeUser.target?.profiles.isNotEmpty == true) {
      return calleeUser.target!.profiles.first.displayName;
    }
    return calleeUsername;
  }

  /// Get effective caller name
  String get effectiveCallerName {
    if (callerDisplayName.isNotEmpty) return callerDisplayName;
    if (callerUsername.isNotEmpty) return callerUsername;
    return callerId;
  }

  /// Get effective callee name
  String get effectiveCalleeName {
    if (calleeDisplayName.isNotEmpty) return calleeDisplayName;
    if (calleeUsername.isNotEmpty) return calleeUsername;
    return calleeId;
  }

  /// Check if caller is online (from relationship)
  bool get isCallerOnline {
    if (callerUser.target?.presences.isNotEmpty == true) {
      final currentPresence = callerUser.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Check if callee is online (from relationship)
  bool get isCalleeOnline {
    if (calleeUser.target?.presences.isNotEmpty == true) {
      final currentPresence = calleeUser.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Get private data records count
  int get privateDataRecordsCount => privateDataRecords.length;

  /// Check if call has private data records
  bool get hasPrivateDataRecords => privateDataRecords.isNotEmpty;

  /// Check if call has private data (for 1:1 simulation)
  bool get hasPrivateData => privateDataRecords.isNotEmpty;

  /// Get private data call state (from first record for 1:1 simulation)
  int get privateDataCallState =>
      privateDataRecords.isNotEmpty ? privateDataRecords.first.callState : 0;

  /// Get private data ended reason (from first record for 1:1 simulation)
  int get privateDataEndedReason =>
      privateDataRecords.isNotEmpty ? privateDataRecords.first.endedReason : 0;

  /// Check if private data is read (from first record for 1:1 simulation)
  bool get isPrivateDataRead =>
      privateDataRecords.isNotEmpty ? privateDataRecords.first.isRead : false;

  /// Get call type description
  String get callTypeDescription {
    if (isVideoCall) return 'Video Call';
    return 'Voice Call';
  }

  /// Get call direction description
  String get callDirectionDescription {
    if (isInComingCall) return 'Incoming';
    if (isOutgoing) return 'Outgoing';
    return 'Unknown';
  }

  /// Get call status description
  String get callStatusDescription {
    if (isMissedCall) return 'Missed';
    if (isEnded) return 'Ended';
    if (isActive) return 'Active';
    return 'Unknown';
  }

  @override
  String toString() {
    return 'CallLog{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'callId: $callId, '
        'callerId: $callerId, '
        'calleeId: $calleeId, '
        'callState: $callState, '
        'endedReason: $endedReason, '
        'callTimeInSeconds: $callTimeInSeconds, '
        'isMissedCall: $isMissedCall, '
        'isInComingCall: $isInComingCall, '
        'isOutgoing: $isOutgoing, '
        'isVideoCall: $isVideoCall, '
        'createTime: $createTime, '
        'endedTime: $endedTime'
        '}';
  }
}
