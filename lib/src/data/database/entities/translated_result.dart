import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'message.dart';

/// Translated result entity for storing translation results
/// UID range: 3400-3409
@Entity()
class TranslatedResult {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3400)
  String workspaceId = '';

  /// Channel ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3401)
  String channelId = '';

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3402)
  String sessionKey = '';

  /// Message ID field (indexed, FIXED, UID UPDATED) - kept for backward compatibility
  @Index()
  @Property(uid: 3403)
  String messageIdField = '';

  /// Original content (UID UPDATED)
  @Property(uid: 3404)
  String originalContent = '';

  /// Translated content (UID UPDATED)
  @Property(uid: 3405)
  String translatedContent = '';

  /// Original language (UID UPDATED)
  @Property(uid: 3406)
  String originalLanguage = '';

  /// Target language (UID UPDATED)
  @Property(uid: 3407)
  String targetLanguage = '';

  /// Translation status (UID UPDATED)
  @Property(uid: 3408)
  int statusRaw = 0;

  /// Whether to show translate result (UID UPDATED)
  @Property(uid: 3409)
  bool isShowTranslateResult = false;

  // RELATIONSHIPS

  /// ToOne relationship to Message
  /// Links this translation result to its message
  final message = ToOne<Message>();

  /// Get messageId from relationship or field (backward compatibility)
  String get messageId => message.target?.messageId ?? messageIdField;

  /// Composite index field: messageIdLanguage
  String get messageIdLanguage => '${messageId}_${targetLanguage}';

  /// Default constructor
  TranslatedResult();

  /// Constructor with required fields
  TranslatedResult.create({
    required this.workspaceId,
    required this.channelId,
    required this.sessionKey,
    required String messageId, // messageId parameter for backward compatibility
    this.originalContent = '',
    this.translatedContent = '',
    this.originalLanguage = '',
    this.targetLanguage = '',
    this.statusRaw = 0,
    this.isShowTranslateResult = false,
  }) : messageIdField = messageId;

  /// Copy constructor for updates
  TranslatedResult copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? sessionKey,
    String? messageId, // messageId parameter for backward compatibility
    String? originalContent,
    String? translatedContent,
    String? originalLanguage,
    String? targetLanguage,
    int? statusRaw,
    bool? isShowTranslateResult,
  }) {
    final newTranslatedResult = TranslatedResult()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelId = channelId ?? this.channelId
      ..sessionKey = sessionKey ?? this.sessionKey
      ..messageIdField = messageId ?? this.messageIdField
      ..originalContent = originalContent ?? this.originalContent
      ..translatedContent = translatedContent ?? this.translatedContent
      ..originalLanguage = originalLanguage ?? this.originalLanguage
      ..targetLanguage = targetLanguage ?? this.targetLanguage
      ..statusRaw = statusRaw ?? this.statusRaw
      ..isShowTranslateResult =
          isShowTranslateResult ?? this.isShowTranslateResult;

    // Copy relationship
    newTranslatedResult.message.target = message.target;
    return newTranslatedResult;
  }

  /// Update translation result
  void updateTranslation({
    String? translatedContent,
    int? statusRaw,
  }) {
    if (translatedContent != null) this.translatedContent = translatedContent;
    if (statusRaw != null) this.statusRaw = statusRaw;
  }

  /// Show translation result
  void showTranslateResult() {
    isShowTranslateResult = true;
  }

  /// Hide translation result
  void hideTranslateResult() {
    isShowTranslateResult = false;
  }

  /// Check if translation is completed
  bool get isCompleted => statusRaw == 1; // Assuming 1 means completed

  /// Check if translation is in progress
  bool get isInProgress => statusRaw == 0; // Assuming 0 means in progress

  /// Check if translation failed
  bool get isFailed => statusRaw == -1; // Assuming -1 means failed

  /// Check if translation has content
  bool get hasTranslation => translatedContent.isNotEmpty;

  // RELATIONSHIP HELPER METHODS

  /// Check if this translation result has a valid message relationship
  bool get hasMessageRelationship => message.target != null;

  /// Get message content from relationship
  String get relatedMessageContent => message.target?.content ?? '';

  /// Get message type from relationship
  int get relatedMessageType => message.target?.messageTypeRaw ?? 0;

  /// Get message status from relationship
  int get relatedMessageStatus => message.target?.messageStatusRaw ?? 0;

  /// Get message channel ID from relationship
  String get relatedMessageChannelId => message.target?.channelId ?? '';

  /// Get message sender ID from relationship
  String get relatedMessageSenderId => message.target?.userId ?? '';

  /// Get message session key from relationship
  String get relatedMessageSessionKey => message.target?.sessionKey ?? '';

  /// Check if related message is successful
  bool get isRelatedMessageSuccessful => message.target?.isSuccessful ?? false;

  /// Check if related message is failed
  bool get isRelatedMessageFailed => message.target?.isFailed ?? false;

  /// Check if related message is pending
  bool get isRelatedMessagePending => message.target?.isPending ?? false;

  /// Check if related message is pinned
  bool get isRelatedMessagePinned => message.target?.isPinned ?? false;

  /// Get related message create time
  DateTime? get relatedMessageCreateTime => message.target?.createTime;

  /// Get related message sender username
  String get relatedMessageSenderUsername =>
      message.target?.relatedSenderUsername ?? '';

  /// Get related message sender display name
  String get relatedMessageSenderDisplayName =>
      message.target?.relatedSenderDisplayName ?? '';

  /// Get related message channel name
  String get relatedMessageChannelName =>
      message.target?.relatedChannelName ?? '';

  /// Check if related message sender is online
  bool get isRelatedMessageSenderOnline =>
      message.target?.isSenderOnline ?? false;

  /// Get translation status description
  String get translationStatusDescription {
    switch (statusRaw) {
      case -1:
        return 'Failed';
      case 0:
        return 'In Progress';
      case 1:
        return 'Completed';
      case 2:
        return 'Cached';
      default:
        return 'Unknown';
    }
  }

  /// Check if translation is cached
  bool get isCached => statusRaw == 2;

  /// Check if translation is successful (completed or cached)
  bool get isSuccessful => isCompleted || isCached;

  /// Check if original content exists
  bool get hasOriginalContent => originalContent.isNotEmpty;

  /// Check if both original and translated content exist
  bool get hasBothContents => hasOriginalContent && hasTranslation;

  /// Check if languages are specified
  bool get hasLanguageInfo =>
      originalLanguage.isNotEmpty && targetLanguage.isNotEmpty;

  /// Check if translation is from different language
  bool get isDifferentLanguage =>
      originalLanguage != targetLanguage && hasLanguageInfo;

  /// Get language pair description
  String get languagePairDescription {
    if (!hasLanguageInfo) return 'Unknown → Unknown';
    return '$originalLanguage → $targetLanguage';
  }

  /// Check if translation should be displayed
  bool get shouldShowTranslation =>
      isShowTranslateResult && hasTranslation && isSuccessful;

  /// Get translation quality score (based on content length difference)
  double get translationQualityScore {
    if (!hasBothContents) return 0.0;
    final originalLength = originalContent.length;
    final translatedLength = translatedContent.length;
    if (originalLength == 0) return 0.0;

    // Quality score based on length similarity (closer to 1.0 is better)
    final lengthRatio = translatedLength / originalLength;
    if (lengthRatio > 2.0 || lengthRatio < 0.5)
      return 0.5; // Suspicious length difference
    return 1.0 -
        (lengthRatio - 1.0).abs(); // Closer to 1.0 ratio = higher score
  }

  /// Check if translation quality is good
  bool get hasGoodQuality => translationQualityScore > 0.7;

  /// Get content length difference
  int get contentLengthDifference {
    if (!hasBothContents) return 0;
    return (translatedContent.length - originalContent.length).abs();
  }

  /// Check if translation is significantly longer
  bool get isTranslationLonger =>
      hasTranslation &&
      hasOriginalContent &&
      translatedContent.length > originalContent.length * 1.5;

  /// Check if translation is significantly shorter
  bool get isTranslationShorter =>
      hasTranslation &&
      hasOriginalContent &&
      translatedContent.length < originalContent.length * 0.5;

  /// Get short translated content (first 100 characters)
  String get shortTranslatedContent {
    if (translatedContent.length <= 100) return translatedContent;
    return '${translatedContent.substring(0, 100)}...';
  }

  /// Get short original content (first 100 characters)
  String get shortOriginalContent {
    if (originalContent.length <= 100) return originalContent;
    return '${originalContent.substring(0, 100)}...';
  }

  @override
  String toString() {
    return 'TranslatedResult{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'sessionKey: $sessionKey, '
        'messageId: $messageId, '
        'originalLanguage: $originalLanguage, '
        'targetLanguage: $targetLanguage, '
        'statusRaw: $statusRaw, '
        'isShowTranslateResult: $isShowTranslateResult, '
        'hasTranslation: $hasTranslation'
        '}';
  }
}
