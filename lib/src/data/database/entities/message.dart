import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';
import 'channel.dart';
import 'user.dart';
import 'attachment.dart';
import 'translated_result.dart';

/// Message entity for storing chat messages
/// UID range: 3200-3230
@Entity()
class Message {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3200)
  String workspaceId = '';

  /// Channel ID (indexed, UID UPDATED) - kept for backward compatibility
  @Index()
  @Property(uid: 3201)
  String channelIdField = '';

  /// Unique message ID (indexed, UID UPDATED)
  @Unique()
  @Index()
  @Property(uid: 3202)
  String messageId = '';

  /// Session key reference (indexed, UID UPDATED) - kept for backward compatibility
  @Index()
  @Property(uid: 3203)
  String sessionKeyField = '';

  /// User ID who sent the message (indexed, UID UPDATED) - kept for backward compatibility
  @Index()
  @Property(uid: 3204)
  String userIdField = '';

  /// Message view type (UID UPDATED)
  @Property(uid: 3205)
  int messageViewTypeRaw = 0;

  /// Content arguments (UID UPDATED)
  @Property(uid: 3206)
  String contentArguments = '';

  /// Message content (UID UPDATED)
  @Property(uid: 3207)
  String content = '';

  /// Message reference (UID UPDATED)
  @Property(uid: 3208)
  String ref = '';

  /// Content locale (UID UPDATED)
  @Property(uid: 3209)
  String contentLocale = '';

  /// Message type (UID UPDATED)
  @Property(uid: 3210)
  int messageTypeRaw = 0;

  /// Message status (UID UPDATED)
  @Property(uid: 3211)
  int messageStatusRaw = 0;

  /// Message error reason (UID UPDATED)
  @Property(uid: 3212)
  int messageErrorReasonRaw = 0;

  /// Attachment type (UID UPDATED)
  @Property(uid: 3213)
  int attachmentTypeRaw = 0;

  /// Whether message is a thread (UID UPDATED)
  @Property(uid: 3214)
  bool isThread = false;

  /// Report count (UID UPDATED)
  @Property(uid: 3215)
  int reportCount = 0;

  /// Whether message is reported (UID UPDATED)
  @Property(uid: 3216)
  bool isReported = false;

  /// Attachment count (UID UPDATED)
  @Property(uid: 3217)
  int attachmentCount = 0;

  /// Message creation time (UID UPDATED)
  @Property(uid: 3218)
  DateTime? createTime;

  /// Message last update time (UID UPDATED)
  @Property(uid: 3219)
  DateTime? updateTime;

  /// Whether this is the first message in channel (UID UPDATED)
  @Property(uid: 3220)
  bool isFirstMessage = false;

  /// Original message raw data (UID UPDATED)
  @Property(uid: 3221)
  String originalMessageRaw = '';

  /// Reactions raw data (UID UPDATED)
  @Property(uid: 3222)
  String reactionsRaw = '';

  /// Mentions raw data (UID UPDATED)
  @Property(uid: 3223)
  String mentionsRaw = '';

  /// Embed raw data (UID UPDATED)
  @Property(uid: 3224)
  String embedRaw = '';

  /// Data mentions raw (UID UPDATED)
  @Property(uid: 3225)
  String dataMentionsRaw = '';

  /// Message edit time (UID UPDATED)
  @Property(uid: 3226)
  DateTime? editTime;

  /// Whether this is a partial message record (UID UPDATED)
  @Property(uid: 3227)
  bool isPartial = false;

  /// Whether this is a temporary message (UID UPDATED)
  @Property(uid: 3228)
  bool isTemp = false;

  /// Whether message is pinned (UID UPDATED)
  @Property(uid: 3229)
  bool isPinned = false;

  /// Pin time (UID UPDATED)
  @Property(uid: 3230)
  DateTime? pinTime;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this message to its session
  final session = ToOne<Session>();

  /// ToOne relationship to Channel
  /// Links this message to its channel
  final channel = ToOne<Channel>();

  /// ToOne relationship to User (sender)
  /// Links this message to its sender
  final sender = ToOne<User>();

  /// Reverse relationship: Attachment entities that reference this message
  /// Links to all attachments of this message
  @Backlink('message')
  final attachments = ToMany<Attachment>();

  /// ToOne relationship to TranslatedResult
  /// Links this message to its translation result (1:1 relationship)
  final translatedResult = ToOne<TranslatedResult>();

  /// Get sessionKey from relationship or field (backward compatibility)
  String get sessionKey => session.target?.sessionKey ?? sessionKeyField;

  /// Get channelId from relationship or field (backward compatibility)
  String get channelId => channel.target?.channelId ?? channelIdField;

  /// Get userId from relationship or field (backward compatibility)
  String get userId => sender.target?.userId ?? userIdField;

  /// Composite index field: channelIdCreateTime
  String get channelIdCreateTime =>
      '${channelId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Composite index field: userIdChannel
  String get userIdChannel => '${userId}_$channelId';

  /// Composite index field: channelIdStatus
  String get channelIdStatus => '${channelId}_$messageStatusRaw';

  /// Composite index field: sessionChannelTime
  String get sessionChannelTime =>
      '${sessionKey}_${channelId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Default constructor
  Message();

  /// Constructor with required fields
  Message.create({
    required this.workspaceId,
    required String channelId, // channelId parameter for backward compatibility
    required this.messageId,
    required String
        sessionKey, // sessionKey parameter for backward compatibility
    required String userId, // userId parameter for backward compatibility
    this.messageViewTypeRaw = 0,
    this.contentArguments = '',
    this.content = '',
    this.ref = '',
    this.contentLocale = '',
    this.messageTypeRaw = 0,
    this.messageStatusRaw = 0,
    this.messageErrorReasonRaw = 0,
    this.attachmentTypeRaw = 0,
    this.isThread = false,
    this.reportCount = 0,
    this.isReported = false,
    this.attachmentCount = 0,
    this.createTime,
    this.updateTime,
    this.isFirstMessage = false,
    this.originalMessageRaw = '',
    this.reactionsRaw = '',
    this.mentionsRaw = '',
    this.embedRaw = '',
    this.dataMentionsRaw = '',
    this.editTime,
    this.isPartial = false,
    this.isTemp = false,
    this.isPinned = false,
    this.pinTime,
  })  : channelIdField = channelId,
        sessionKeyField = sessionKey,
        userIdField = userId {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  Message copyWith({
    int? id,
    String? workspaceId,
    String? channelId, // channelId parameter for backward compatibility
    String? messageId,
    String? sessionKey, // sessionKey parameter for backward compatibility
    String? userId, // userId parameter for backward compatibility
    int? messageViewTypeRaw,
    String? contentArguments,
    String? content,
    String? ref,
    String? contentLocale,
    int? messageTypeRaw,
    int? messageStatusRaw,
    int? messageErrorReasonRaw,
    int? attachmentTypeRaw,
    bool? isThread,
    int? reportCount,
    bool? isReported,
    int? attachmentCount,
    DateTime? createTime,
    DateTime? updateTime,
    bool? isFirstMessage,
    String? originalMessageRaw,
    String? reactionsRaw,
    String? mentionsRaw,
    String? embedRaw,
    String? dataMentionsRaw,
    DateTime? editTime,
    bool? isPartial,
    bool? isTemp,
    bool? isPinned,
    DateTime? pinTime,
  }) {
    final newMessage = Message()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelIdField = channelId ?? this.channelIdField
      ..messageId = messageId ?? this.messageId
      ..sessionKeyField = sessionKey ?? this.sessionKeyField
      ..userIdField = userId ?? this.userIdField
      ..messageViewTypeRaw = messageViewTypeRaw ?? this.messageViewTypeRaw
      ..contentArguments = contentArguments ?? this.contentArguments
      ..content = content ?? this.content
      ..ref = ref ?? this.ref
      ..contentLocale = contentLocale ?? this.contentLocale
      ..messageTypeRaw = messageTypeRaw ?? this.messageTypeRaw
      ..messageStatusRaw = messageStatusRaw ?? this.messageStatusRaw
      ..messageErrorReasonRaw =
          messageErrorReasonRaw ?? this.messageErrorReasonRaw
      ..attachmentTypeRaw = attachmentTypeRaw ?? this.attachmentTypeRaw
      ..isThread = isThread ?? this.isThread
      ..reportCount = reportCount ?? this.reportCount
      ..isReported = isReported ?? this.isReported
      ..attachmentCount = attachmentCount ?? this.attachmentCount
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..isFirstMessage = isFirstMessage ?? this.isFirstMessage
      ..originalMessageRaw = originalMessageRaw ?? this.originalMessageRaw
      ..reactionsRaw = reactionsRaw ?? this.reactionsRaw
      ..mentionsRaw = mentionsRaw ?? this.mentionsRaw
      ..embedRaw = embedRaw ?? this.embedRaw
      ..dataMentionsRaw = dataMentionsRaw ?? this.dataMentionsRaw
      ..editTime = editTime ?? this.editTime
      ..isPartial = isPartial ?? this.isPartial
      ..isTemp = isTemp ?? this.isTemp
      ..isPinned = isPinned ?? this.isPinned
      ..pinTime = pinTime ?? this.pinTime;

    // Copy relationships
    newMessage.session.target = session.target;
    newMessage.channel.target = channel.target;
    newMessage.sender.target = sender.target;
    return newMessage;
  }

  /// Pin the message
  void pin() {
    isPinned = true;
    pinTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Unpin the message
  void unpin() {
    isPinned = false;
    pinTime = null;
    updateTime = DateTime.now();
  }

  /// Edit the message
  void edit(String newContent) {
    content = newContent;
    editTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Report the message
  void report() {
    isReported = true;
    reportCount++;
    updateTime = DateTime.now();
  }

  /// Mark as complete message record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Mark as temporary
  void markAsTemp() {
    isTemp = true;
    updateTime = DateTime.now();
  }

  /// Mark as permanent
  void markAsPermanent() {
    isTemp = false;
    updateTime = DateTime.now();
  }

  /// Check if message has content
  bool get hasContent => content.isNotEmpty;

  /// Check if message has attachments
  bool get hasAttachments => attachmentCount > 0;

  /// Check if message is edited
  bool get isEdited => editTime != null;

  /// Check if message has reactions
  bool get hasReactions => reactionsRaw.isNotEmpty;

  /// Check if message has mentions
  bool get hasMentions => mentionsRaw.isNotEmpty;

  // RELATIONSHIP HELPER METHODS

  /// Check if this message has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Check if this message has a valid channel relationship
  bool get hasChannelRelationship => channel.target != null;

  /// Check if this message has a valid sender relationship
  bool get hasSenderRelationship => sender.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Get channel name from relationship
  String get relatedChannelName => channel.target?.name ?? '';

  /// Get channel workspace ID from relationship
  String get relatedChannelWorkspaceId => channel.target?.workspaceId ?? '';

  /// Get channel type description from relationship
  String get relatedChannelTypeDescription =>
      channel.target?.channelTypeDescription ?? '';

  /// Check if related channel is DM
  bool get isRelatedChannelDM => channel.target?.isDM ?? false;

  /// Check if related channel is archived
  bool get isRelatedChannelArchived => channel.target?.isArchived ?? false;

  /// Get effective channel name from relationship
  String get relatedEffectiveChannelName =>
      channel.target?.effectiveChannelName ?? '';

  /// Get sender username from relationship
  String get relatedSenderUsername => sender.target?.username ?? '';

  /// Get sender display name from relationship (from profile)
  String get relatedSenderDisplayName {
    if (sender.target?.profiles.isNotEmpty == true) {
      return sender.target!.profiles.first.displayName;
    }
    return relatedSenderUsername;
  }

  /// Get effective sender name from relationship
  String get relatedEffectiveSenderName {
    if (relatedSenderDisplayName.isNotEmpty) return relatedSenderDisplayName;
    if (relatedSenderUsername.isNotEmpty) return relatedSenderUsername;
    return userId;
  }

  /// Check if sender is online (from relationship)
  bool get isSenderOnline {
    if (sender.target?.presences.isNotEmpty == true) {
      final currentPresence = sender.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Get message type description
  String get messageTypeDescription {
    switch (messageTypeRaw) {
      case 0:
        return 'Text Message';
      case 1:
        return 'Image Message';
      case 2:
        return 'File Message';
      case 3:
        return 'Audio Message';
      case 4:
        return 'Video Message';
      case 5:
        return 'System Message';
      case 6:
        return 'Call Message';
      default:
        return 'Unknown Message';
    }
  }

  /// Get message status description
  String get messageStatusDescription {
    switch (messageStatusRaw) {
      case 0:
        return 'Pending';
      case 1:
        return 'Sent';
      case 2:
        return 'Delivered';
      case 3:
        return 'Read';
      case 4:
        return 'Failed';
      default:
        return 'Unknown';
    }
  }

  /// Get attachment type description
  String get attachmentTypeDescription {
    switch (attachmentTypeRaw) {
      case 0:
        return 'No Attachment';
      case 1:
        return 'Image';
      case 2:
        return 'Document';
      case 3:
        return 'Audio';
      case 4:
        return 'Video';
      case 5:
        return 'Archive';
      default:
        return 'Unknown';
    }
  }

  /// Get message view type description
  String get messageViewTypeDescription {
    switch (messageViewTypeRaw) {
      case 0:
        return 'Normal';
      case 1:
        return 'Reply';
      case 2:
        return 'Forward';
      case 3:
        return 'Quote';
      default:
        return 'Unknown';
    }
  }

  /// Check if message is successful (sent, delivered, or read)
  bool get isSuccessful => messageStatusRaw >= 1 && messageStatusRaw <= 3;

  /// Check if message failed
  bool get isFailed => messageStatusRaw == 4;

  /// Check if message is pending
  bool get isPending => messageStatusRaw == 0;

  /// Check if message is delivered
  bool get isDelivered => messageStatusRaw >= 2;

  /// Check if message is read
  bool get isRead => messageStatusRaw == 3;

  /// Check if message has embed data
  bool get hasEmbed => embedRaw.isNotEmpty;

  /// Check if message has data mentions
  bool get hasDataMentions => dataMentionsRaw.isNotEmpty;

  /// Get message age in minutes
  int get ageInMinutes {
    if (createTime == null) return 0;
    return DateTime.now().difference(createTime!).inMinutes;
  }

  /// Get message age in hours
  int get ageInHours {
    if (createTime == null) return 0;
    return DateTime.now().difference(createTime!).inHours;
  }

  /// Check if message is recent (less than 1 hour old)
  bool get isRecent => ageInHours < 1;

  /// Check if message is today
  bool get isToday {
    if (createTime == null) return false;
    final now = DateTime.now();
    final messageDate = createTime!;
    return now.year == messageDate.year &&
        now.month == messageDate.month &&
        now.day == messageDate.day;
  }

  /// Get formatted create time
  String get formattedCreateTime {
    if (createTime == null) return '';
    return createTime!.toIso8601String();
  }

  /// Get short content (first 100 characters)
  String get shortContent {
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }

  // ATTACHMENT HELPER METHODS

  /// Get attachments count
  int get attachmentsCount => attachments.length;

  /// Check if message has attachments (from relationship)
  bool get hasAttachmentsFromRelationship => attachments.isNotEmpty;

  /// Get photo attachments
  List<Attachment> get photoAttachments {
    return attachments.where((attachment) => attachment.isPhoto).toList();
  }

  /// Get video attachments
  List<Attachment> get videoAttachments {
    return attachments.where((attachment) => attachment.isVideo).toList();
  }

  /// Get audio attachments
  List<Attachment> get audioAttachments {
    return attachments.where((attachment) => attachment.isAudio).toList();
  }

  /// Get sticker attachments
  List<Attachment> get stickerAttachments {
    return attachments.where((attachment) => attachment.isSticker).toList();
  }

  /// Get file attachments
  List<Attachment> get fileAttachments {
    return attachments.where((attachment) => attachment.isFileMessage).toList();
  }

  /// Get voice message attachments
  List<Attachment> get voiceMessageAttachments {
    return attachments
        .where((attachment) => attachment.isVoiceMessage)
        .toList();
  }

  /// Get video message attachments
  List<Attachment> get videoMessageAttachments {
    return attachments
        .where((attachment) => attachment.isVideoMessage)
        .toList();
  }

  /// Get media attachments (photo, video, audio)
  List<Attachment> get mediaAttachments {
    return attachments.where((attachment) => attachment.isMediaType).toList();
  }

  /// Get message type attachments (voice, video message)
  List<Attachment> get messageTypeAttachments {
    return attachments.where((attachment) => attachment.isMessageType).toList();
  }

  /// Get successful attachments
  List<Attachment> get successfulAttachments {
    return attachments.where((attachment) => attachment.isSuccessful).toList();
  }

  /// Get failed attachments
  List<Attachment> get failedAttachments {
    return attachments.where((attachment) => attachment.isFailed).toList();
  }

  /// Get pending attachments
  List<Attachment> get pendingAttachments {
    return attachments.where((attachment) => attachment.isPending).toList();
  }

  /// Get uploading attachments
  List<Attachment> get uploadingAttachments {
    return attachments.where((attachment) => attachment.isUploading).toList();
  }

  /// Get large attachments
  List<Attachment> get largeAttachments {
    return attachments.where((attachment) => attachment.isLarge).toList();
  }

  /// Get photo attachments count
  int get photoAttachmentsCount => photoAttachments.length;

  /// Get video attachments count
  int get videoAttachmentsCount => videoAttachments.length;

  /// Get audio attachments count
  int get audioAttachmentsCount => audioAttachments.length;

  /// Get sticker attachments count
  int get stickerAttachmentsCount => stickerAttachments.length;

  /// Get file attachments count
  int get fileAttachmentsCount => fileAttachments.length;

  /// Get media attachments count
  int get mediaAttachmentsCount => mediaAttachments.length;

  /// Get successful attachments count
  int get successfulAttachmentsCount => successfulAttachments.length;

  /// Get failed attachments count
  int get failedAttachmentsCount => failedAttachments.length;

  /// Get pending attachments count
  int get pendingAttachmentsCount => pendingAttachments.length;

  /// Check if message has photo attachments
  bool get hasPhotoAttachments => photoAttachmentsCount > 0;

  /// Check if message has video attachments
  bool get hasVideoAttachments => videoAttachmentsCount > 0;

  /// Check if message has audio attachments
  bool get hasAudioAttachments => audioAttachmentsCount > 0;

  /// Check if message has sticker attachments
  bool get hasStickerAttachments => stickerAttachmentsCount > 0;

  /// Check if message has file attachments
  bool get hasFileAttachments => fileAttachmentsCount > 0;

  /// Check if message has media attachments
  bool get hasMediaAttachments => mediaAttachmentsCount > 0;

  /// Check if message has failed attachments
  bool get hasFailedAttachments => failedAttachmentsCount > 0;

  /// Check if message has pending attachments
  bool get hasPendingAttachments => pendingAttachmentsCount > 0;

  /// Check if message has large attachments
  bool get hasLargeAttachments => largeAttachments.isNotEmpty;

  /// Get attachment by attachment ID
  Attachment? getAttachmentById(String attachmentId) {
    return attachments
        .where((attachment) => attachment.attachmentId == attachmentId)
        .firstOrNull;
  }

  /// Get attachments by type
  List<Attachment> getAttachmentsByType(String type) {
    return attachments
        .where((attachment) => attachment.attachmentType == type)
        .toList();
  }

  /// Get attachments by status
  List<Attachment> getAttachmentsByStatus(int status) {
    return attachments
        .where((attachment) => attachment.attachmentStatusRaw == status)
        .toList();
  }

  /// Get total estimated size of all attachments
  int get totalAttachmentsSize {
    return attachments.fold(
        0, (sum, attachment) => sum + attachment.estimatedSize);
  }

  /// Check if all attachments are successful
  bool get allAttachmentsSuccessful {
    if (attachments.isEmpty) return true;
    return attachments.every((attachment) => attachment.isSuccessful);
  }

  /// Check if any attachment is failed
  bool get anyAttachmentFailed {
    return attachments.any((attachment) => attachment.isFailed);
  }

  /// Check if any attachment is pending
  bool get anyAttachmentPending {
    return attachments.any((attachment) => attachment.isPending);
  }

  // TRANSLATION HELPER METHODS

  /// Check if message has translation result
  bool get hasTranslationResult => translatedResult.target != null;

  /// Check if message has completed translation
  bool get hasCompletedTranslation =>
      translatedResult.target?.isCompleted ?? false;

  /// Check if message has successful translation
  bool get hasSuccessfulTranslation =>
      translatedResult.target?.isSuccessful ?? false;

  /// Check if message translation is in progress
  bool get isTranslationInProgress =>
      translatedResult.target?.isInProgress ?? false;

  /// Check if message translation failed
  bool get hasTranslationFailed => translatedResult.target?.isFailed ?? false;

  /// Check if message translation is cached
  bool get hasTranslationCached => translatedResult.target?.isCached ?? false;

  /// Check if translation should be shown
  bool get shouldShowTranslation =>
      translatedResult.target?.shouldShowTranslation ?? false;

  /// Get translated content
  String get translatedContent =>
      translatedResult.target?.translatedContent ?? '';

  /// Get original content for translation
  String get originalContentForTranslation =>
      translatedResult.target?.originalContent ?? '';

  /// Get translation status description
  String get translationStatusDescription =>
      translatedResult.target?.translationStatusDescription ?? 'No Translation';

  /// Get translation language pair
  String get translationLanguagePair =>
      translatedResult.target?.languagePairDescription ?? '';

  /// Get translation quality score
  double get translationQualityScore =>
      translatedResult.target?.translationQualityScore ?? 0.0;

  /// Check if translation has good quality
  bool get hasGoodTranslationQuality =>
      translatedResult.target?.hasGoodQuality ?? false;

  /// Get short translated content
  String get shortTranslatedContent =>
      translatedResult.target?.shortTranslatedContent ?? '';

  /// Check if translation is from different language
  bool get isTranslationFromDifferentLanguage =>
      translatedResult.target?.isDifferentLanguage ?? false;

  /// Check if translation is significantly longer than original
  bool get isTranslationLonger =>
      translatedResult.target?.isTranslationLonger ?? false;

  /// Check if translation is significantly shorter than original
  bool get isTranslationShorter =>
      translatedResult.target?.isTranslationShorter ?? false;

  /// Get content length difference between original and translated
  int get translationContentLengthDifference =>
      translatedResult.target?.contentLengthDifference ?? 0;

  /// Check if message needs translation (has content but no translation result)
  bool get needsTranslation => hasContent && !hasTranslationResult;

  /// Check if message can be translated (has content and is successful)
  bool get canBeTranslated =>
      hasContent && isSuccessful && !hasTranslationResult;

  /// Check if translation is available and ready to show
  bool get isTranslationAvailable =>
      hasTranslationResult &&
      hasSuccessfulTranslation &&
      translatedResult.target!.hasTranslation;

  @override
  String toString() {
    return 'Message{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'messageId: $messageId, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'content: ${content.length > 50 ? content.substring(0, 50) + '...' : content}, '
        'messageTypeRaw: $messageTypeRaw, '
        'messageStatusRaw: $messageStatusRaw, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'isPartial: $isPartial, '
        'isTemp: $isTemp, '
        'isPinned: $isPinned'
        '}';
  }
}
