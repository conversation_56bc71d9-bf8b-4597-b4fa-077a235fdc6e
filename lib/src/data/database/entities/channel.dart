import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';
import 'user.dart';
import 'channel_private_data.dart';
import 'member.dart';
import 'message.dart';

/// Channel entity for storing communication channel information
/// UID range: 3001-3016
@Entity()
class Channel {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed)
  @Index()
  @Property(uid: 3001)
  String workspaceId = '';

  /// Unique channel ID (indexed)
  @Unique()
  @Index()
  @Property(uid: 3002)
  String channelId = '';

  /// Session key reference (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 3003)
  String sessionKeyField = '';

  /// Channel owner user ID (indexed, FIXED) - kept for backward compatibility
  @Index()
  @Property(uid: 3004)
  String channelOwnerUserIdField = '';

  /// DM channel ID
  @Property(uid: 3005)
  String dmChannelId = '';

  /// Recipient ID (indexed, FIXED) - kept for backward compatibility
  @Index()
  @Property(uid: 3006)
  String recipientIdField = '';

  /// Channel name
  @Property(uid: 3007)
  String name = '';

  /// Channel avatar URL
  @Property(uid: 3008)
  String avatar = '';

  /// Original avatar URL
  @Property(uid: 3009)
  String originalAvatar = '';

  /// Channel type (raw integer)
  @Property(uid: 3010)
  int channelTypeRaw = 0;

  /// Channel topic
  @Property(uid: 3011)
  String topic = '';

  /// Channel description
  @Property(uid: 3012)
  String description = '';

  /// Channel creation time
  @Property(uid: 3013)
  DateTime? createTime;

  /// Channel last update time
  @Property(uid: 3014)
  DateTime? updateTime;

  /// Whether channel is archived
  @Property(uid: 3015)
  bool isArchived = false;

  /// Whether this is a partial channel record
  @Property(uid: 3016)
  bool isPartial = false;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this channel to its parent session
  final session = ToOne<Session>();

  /// ToOne relationship to User (owner)
  /// Links this channel to its owner user
  final ownerUser = ToOne<User>();

  /// ToOne relationship to User (recipient)
  /// Links this channel to its recipient user (for DM channels)
  final recipientUser = ToOne<User>();

  /// Reverse relationship: ChannelPrivateData entities that reference this channel
  /// Links to all private data records for this channel
  @Backlink('channel')
  final privateDataRecords = ToMany<ChannelPrivateData>();

  /// Reverse relationship: Member entities that reference this channel
  /// Links to all members of this channel
  @Backlink('channel')
  final members = ToMany<Member>();

  /// Reverse relationship: Message entities that reference this channel
  /// Links to all messages in this channel
  @Backlink('channel')
  final messages = ToMany<Message>();

  /// Get sessionKey from relationship or field (backward compatibility)
  String get sessionKey => session.target?.sessionKey ?? sessionKeyField;

  /// Get channelOwnerUserId from relationship or field (backward compatibility)
  String get channelOwnerUserId =>
      ownerUser.target?.userId ?? channelOwnerUserIdField;

  /// Get recipientId from relationship or field (backward compatibility)
  String get recipientId => recipientUser.target?.userId ?? recipientIdField;

  /// Composite index field: sessionChannelId
  String get sessionChannelId => '${sessionKey}_$channelId';

  /// Composite index field: workspaceSessionChannel
  String get workspaceSessionChannel =>
      '${workspaceId}_${sessionKey}_$channelId';

  /// Default constructor
  Channel();

  /// Constructor with required fields
  Channel.create({
    required this.workspaceId,
    required this.channelId,
    required String
        sessionKey, // sessionKey parameter for backward compatibility
    required String
        channelOwnerUserId, // channelOwnerUserId parameter for backward compatibility
    required String
        recipientId, // recipientId parameter for backward compatibility
    this.dmChannelId = '',
    this.name = '',
    this.avatar = '',
    this.originalAvatar = '',
    this.channelTypeRaw = 0,
    this.topic = '',
    this.description = '',
    this.createTime,
    this.updateTime,
    this.isArchived = false,
    this.isPartial = false,
  })  : sessionKeyField = sessionKey,
        channelOwnerUserIdField = channelOwnerUserId,
        recipientIdField = recipientId {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  Channel copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? sessionKey, // sessionKey parameter for backward compatibility
    String?
        channelOwnerUserId, // channelOwnerUserId parameter for backward compatibility
    String? dmChannelId,
    String? recipientId, // recipientId parameter for backward compatibility
    String? name,
    String? avatar,
    String? originalAvatar,
    int? channelTypeRaw,
    String? topic,
    String? description,
    DateTime? createTime,
    DateTime? updateTime,
    bool? isArchived,
    bool? isPartial,
  }) {
    final newChannel = Channel()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelId = channelId ?? this.channelId
      ..sessionKeyField = sessionKey ?? this.sessionKeyField
      ..channelOwnerUserIdField =
          channelOwnerUserId ?? this.channelOwnerUserIdField
      ..dmChannelId = dmChannelId ?? this.dmChannelId
      ..recipientIdField = recipientId ?? this.recipientIdField
      ..name = name ?? this.name
      ..avatar = avatar ?? this.avatar
      ..originalAvatar = originalAvatar ?? this.originalAvatar
      ..channelTypeRaw = channelTypeRaw ?? this.channelTypeRaw
      ..topic = topic ?? this.topic
      ..description = description ?? this.description
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..isArchived = isArchived ?? this.isArchived
      ..isPartial = isPartial ?? this.isPartial;

    // Copy relationships
    newChannel.session.target = session.target;
    newChannel.ownerUser.target = ownerUser.target;
    newChannel.recipientUser.target = recipientUser.target;
    return newChannel;
  }

  /// Archive the channel
  void archive() {
    isArchived = true;
    updateTime = DateTime.now();
  }

  /// Unarchive the channel
  void unarchive() {
    isArchived = false;
    updateTime = DateTime.now();
  }

  /// Update channel information
  void updateInfo({
    String? name,
    String? avatar,
    String? originalAvatar,
    String? topic,
    String? description,
    int? channelTypeRaw,
  }) {
    if (name != null) this.name = name;
    if (avatar != null) this.avatar = avatar;
    if (originalAvatar != null) this.originalAvatar = originalAvatar;
    if (topic != null) this.topic = topic;
    if (description != null) this.description = description;
    if (channelTypeRaw != null) this.channelTypeRaw = channelTypeRaw;
    updateTime = DateTime.now();
  }

  /// Mark as complete channel record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Check if channel has avatar
  bool get hasAvatar => avatar.isNotEmpty;

  /// Check if channel is DM
  bool get isDM => dmChannelId.isNotEmpty;

  // RELATIONSHIP HELPER METHODS

  /// Check if this channel has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Check if this channel has a valid owner relationship
  bool get hasOwnerRelationship => ownerUser.target != null;

  /// Check if this channel has a valid recipient relationship
  bool get hasRecipientRelationship => recipientUser.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Get owner username from relationship
  String get ownerUsername => ownerUser.target?.username ?? '';

  /// Get recipient username from relationship
  String get recipientUsername => recipientUser.target?.username ?? '';

  /// Get owner display name from relationship (from profile)
  String get ownerDisplayName {
    if (ownerUser.target?.profiles.isNotEmpty == true) {
      return ownerUser.target!.profiles.first.displayName;
    }
    return ownerUsername;
  }

  /// Get recipient display name from relationship (from profile)
  String get recipientDisplayName {
    if (recipientUser.target?.profiles.isNotEmpty == true) {
      return recipientUser.target!.profiles.first.displayName;
    }
    return recipientUsername;
  }

  /// Get effective owner name
  String get effectiveOwnerName {
    if (ownerDisplayName.isNotEmpty) return ownerDisplayName;
    if (ownerUsername.isNotEmpty) return ownerUsername;
    return channelOwnerUserId;
  }

  /// Get effective recipient name
  String get effectiveRecipientName {
    if (recipientDisplayName.isNotEmpty) return recipientDisplayName;
    if (recipientUsername.isNotEmpty) return recipientUsername;
    return recipientId;
  }

  /// Check if owner is online (from relationship)
  bool get isOwnerOnline {
    if (ownerUser.target?.presences.isNotEmpty == true) {
      final currentPresence = ownerUser.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Check if recipient is online (from relationship)
  bool get isRecipientOnline {
    if (recipientUser.target?.presences.isNotEmpty == true) {
      final currentPresence = recipientUser.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Get private data records count
  int get privateDataRecordsCount => privateDataRecords.length;

  /// Check if channel has private data records
  bool get hasPrivateDataRecords => privateDataRecords.isNotEmpty;

  /// Check if channel has private data (for 1:1 simulation)
  bool get hasPrivateData => privateDataRecords.isNotEmpty;

  /// Get private data version (from first record for 1:1 simulation)
  int get privateDataVersion =>
      privateDataRecords.isNotEmpty ? privateDataRecords.first.version : 0;

  /// Get private data unread count (from first record for 1:1 simulation)
  int get privateDataUnreadCount =>
      privateDataRecords.isNotEmpty ? privateDataRecords.first.unreadCount : 0;

  /// Check if channel is pinned (from first record for 1:1 simulation)
  bool get isPinned =>
      privateDataRecords.isNotEmpty ? privateDataRecords.first.pinned : false;

  /// Get members count
  int get membersCount => members.length;

  /// Check if channel has members
  bool get hasMembers => members.isNotEmpty;

  /// Get channel owner member (if exists)
  Member? get ownerMember {
    return members
        .where((member) => member.role.toLowerCase() == 'owner')
        .firstOrNull;
  }

  /// Get admin members
  List<Member> get adminMembers {
    return members
        .where((member) => member.role.toLowerCase() == 'admin')
        .toList();
  }

  /// Get moderator members
  List<Member> get moderatorMembers {
    return members
        .where((member) => member.role.toLowerCase() == 'moderator')
        .toList();
  }

  /// Get regular members (excluding admins, moderators, owner)
  List<Member> get regularMembers {
    return members.where((member) {
      final role = member.role.toLowerCase();
      return role != 'owner' && role != 'admin' && role != 'moderator';
    }).toList();
  }

  /// Get online members
  List<Member> get onlineMembers {
    return members.where((member) => member.isRelatedUserOnline).toList();
  }

  /// Get offline members
  List<Member> get offlineMembers {
    return members.where((member) => !member.isRelatedUserOnline).toList();
  }

  /// Get members with nicknames
  List<Member> get membersWithNicknames {
    return members.where((member) => member.hasNickname).toList();
  }

  /// Get members with special permissions (admin, moderator, owner)
  List<Member> get membersWithSpecialPermissions {
    return members.where((member) => member.hasSpecialPermissions).toList();
  }

  /// Get online members count
  int get onlineMembersCount => onlineMembers.length;

  /// Get admin members count
  int get adminMembersCount => adminMembers.length;

  /// Check if channel has admin members
  bool get hasAdminMembers => adminMembersCount > 0;

  /// Check if channel has online members
  bool get hasOnlineMembers => onlineMembersCount > 0;

  /// Get member by user ID
  Member? getMemberByUserId(String userId) {
    return members.where((member) => member.userId == userId).firstOrNull;
  }

  /// Check if user is member of this channel
  bool isUserMember(String userId) {
    return getMemberByUserId(userId) != null;
  }

  /// Check if user is admin or owner of this channel
  bool isUserAdminOrOwner(String userId) {
    final member = getMemberByUserId(userId);
    return member?.isAdminOrOwner ?? false;
  }

  // MESSAGE HELPER METHODS

  /// Get messages count
  int get messagesCount => messages.length;

  /// Check if channel has messages
  bool get hasMessages => messages.isNotEmpty;

  /// Get latest message
  Message? get latestMessage {
    if (messages.isEmpty) return null;
    return messages.reduce((a, b) =>
        (a.createTime?.millisecondsSinceEpoch ?? 0) >
                (b.createTime?.millisecondsSinceEpoch ?? 0)
            ? a
            : b);
  }

  /// Get first message
  Message? get firstMessage {
    if (messages.isEmpty) return null;
    return messages.reduce((a, b) =>
        (a.createTime?.millisecondsSinceEpoch ?? 0) <
                (b.createTime?.millisecondsSinceEpoch ?? 0)
            ? a
            : b);
  }

  /// Get pinned messages
  List<Message> get pinnedMessages {
    return messages.where((message) => message.isPinned).toList();
  }

  /// Get unread messages (pending or failed)
  List<Message> get unreadMessages {
    return messages
        .where((message) => message.isPending || message.isFailed)
        .toList();
  }

  /// Get successful messages
  List<Message> get successfulMessages {
    return messages.where((message) => message.isSuccessful).toList();
  }

  /// Get failed messages
  List<Message> get failedMessages {
    return messages.where((message) => message.isFailed).toList();
  }

  /// Get messages with attachments
  List<Message> get messagesWithAttachments {
    return messages.where((message) => message.hasAttachments).toList();
  }

  /// Get messages with reactions
  List<Message> get messagesWithReactions {
    return messages.where((message) => message.hasReactions).toList();
  }

  /// Get messages with mentions
  List<Message> get messagesWithMentions {
    return messages.where((message) => message.hasMentions).toList();
  }

  /// Get recent messages (last 50, sorted by create time)
  List<Message> get recentMessages {
    final sortedMessages = messages.toList()
      ..sort((a, b) =>
          (b.createTime ?? DateTime(0)).compareTo(a.createTime ?? DateTime(0)));
    return sortedMessages.take(50).toList();
  }

  /// Get today's messages
  List<Message> get todayMessages {
    return messages.where((message) => message.isToday).toList();
  }

  /// Get pinned messages count
  int get pinnedMessagesCount => pinnedMessages.length;

  /// Get unread messages count
  int get unreadMessagesCount => unreadMessages.length;

  /// Get failed messages count
  int get failedMessagesCount => failedMessages.length;

  /// Get messages with attachments count
  int get messagesWithAttachmentsCount => messagesWithAttachments.length;

  /// Check if channel has pinned messages
  bool get hasPinnedMessages => pinnedMessagesCount > 0;

  /// Check if channel has unread messages
  bool get hasUnreadMessages => unreadMessagesCount > 0;

  /// Check if channel has failed messages
  bool get hasFailedMessages => failedMessagesCount > 0;

  /// Get message by message ID
  Message? getMessageById(String messageId) {
    return messages
        .where((message) => message.messageId == messageId)
        .firstOrNull;
  }

  /// Get messages by sender
  List<Message> getMessagesBySender(String userId) {
    return messages.where((message) => message.userId == userId).toList();
  }

  /// Get messages by type
  List<Message> getMessagesByType(int messageType) {
    return messages
        .where((message) => message.messageTypeRaw == messageType)
        .toList();
  }

  /// Get messages by status
  List<Message> getMessagesByStatus(int messageStatus) {
    return messages
        .where((message) => message.messageStatusRaw == messageStatus)
        .toList();
  }

  /// Get messages in date range
  List<Message> getMessagesInDateRange(DateTime start, DateTime end) {
    return messages.where((message) {
      final createTime = message.createTime;
      if (createTime == null) return false;
      return createTime.isAfter(start) && createTime.isBefore(end);
    }).toList();
  }

  /// Get channel type description
  String get channelTypeDescription {
    switch (channelTypeRaw) {
      case 0:
        return 'Text Channel';
      case 1:
        return 'Voice Channel';
      case 2:
        return 'DM Channel';
      case 3:
        return 'Group DM';
      case 4:
        return 'Category';
      case 5:
        return 'News Channel';
      case 6:
        return 'Store Channel';
      default:
        return 'Unknown';
    }
  }

  /// Get effective channel name (fallback to recipient name for DM)
  String get effectiveChannelName {
    if (name.isNotEmpty) return name;
    if (isDM && effectiveRecipientName.isNotEmpty)
      return effectiveRecipientName;
    return 'Unnamed Channel';
  }

  /// Get channel status description
  String get channelStatusDescription {
    if (isArchived) return 'Archived';
    if (isPartial) return 'Partial';
    return 'Active';
  }

  /// Archive this channel
  void archiveChannel() {
    isArchived = true;
    updateTime = DateTime.now();
  }

  /// Unarchive this channel
  void unarchiveChannel() {
    isArchived = false;
    updateTime = DateTime.now();
  }

  @override
  String toString() {
    return 'Channel{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'sessionKey: $sessionKey, '
        'channelOwnerUserId: $channelOwnerUserId, '
        'dmChannelId: $dmChannelId, '
        'recipientId: $recipientId, '
        'name: $name, '
        'avatar: $avatar, '
        'originalAvatar: $originalAvatar, '
        'channelTypeRaw: $channelTypeRaw, '
        'topic: $topic, '
        'description: $description, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'isArchived: $isArchived, '
        'isPartial: $isPartial'
        '}';
  }
}
