import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'sticker.dart';

/// Sticker frame count entity for storing sticker frame count information
/// UID range: 8200-8203
@Entity()
class StickerFrameCount {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 8200)
  String sessionKey = '';

  /// Sticker ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 8201)
  String stickerIdField = '';

  /// Frame count
  @Property(uid: 8202)
  int frameCount = 0;

  /// Update time
  @Property(uid: 8203)
  DateTime? updateTime;

  /// ToOne relationship to Sticker
  /// Links to the sticker this frame count belongs to
  final sticker = ToOne<Sticker>();

  /// Get stickerId from relationship or field (backward compatibility)
  String get stickerId => sticker.target?.stickerId ?? stickerIdField;

  /// Default constructor
  StickerFrameCount();

  /// Constructor with required fields
  StickerFrameCount.create({
    required this.sessionKey,
    required String stickerId, // stickerId parameter for backward compatibility
    this.frameCount = 0,
    this.updateTime,
  }) : stickerIdField = stickerId {
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  StickerFrameCount copyWith({
    int? id,
    String? sessionKey,
    String? stickerId, // stickerId parameter for backward compatibility
    int? frameCount,
    DateTime? updateTime,
  }) {
    final newFrameCount = StickerFrameCount()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..stickerIdField = stickerId ?? this.stickerIdField
      ..frameCount = frameCount ?? this.frameCount
      ..updateTime = updateTime ?? this.updateTime;

    // Copy relationship
    newFrameCount.sticker.target = sticker.target;
    return newFrameCount;
  }

  /// Update frame count
  void updateFrameCount(int newFrameCount) {
    frameCount = newFrameCount;
    updateTime = DateTime.now();
  }

  /// Increment frame count
  void incrementFrameCount() {
    frameCount++;
    updateTime = DateTime.now();
  }

  /// Decrement frame count
  void decrementFrameCount() {
    if (frameCount > 0) {
      frameCount--;
      updateTime = DateTime.now();
    }
  }

  /// Check if sticker is animated
  bool get isAnimated => frameCount > 1;

  /// Check if sticker is static
  bool get isStatic => frameCount <= 1;

  /// Check if frame count has relationship to sticker
  bool get hasStickerRelationship => sticker.target != null;

  /// Get sticker name from relationship
  String get stickerName => sticker.target?.name ?? '';

  /// Check if related sticker is animated (from relationship)
  bool get relatedStickerIsAnimated => sticker.target?.isAnimated ?? false;

  /// Synchronize with related sticker frame count
  void syncWithSticker() {
    if (sticker.target != null) {
      sticker.target!.frameCount = frameCount;
      if (frameCount > 1) {
        sticker.target!.isAnimated = true;
      }
    }
  }

  @override
  String toString() {
    return 'StickerFrameCount{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'stickerId: $stickerId, '
        'frameCount: $frameCount, '
        'updateTime: $updateTime'
        '}';
  }
}
