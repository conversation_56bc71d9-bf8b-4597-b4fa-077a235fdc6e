import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session_local_metadata.dart';
import 'history.dart';
import 'manager.dart';
import 'private_data.dart';
import 'collection.dart';

// Import generated ObjectBox classes
import '../generated/objectbox.g.dart';

/// Session entity for storing user session information
/// UID range: 1001-1009
@Entity()
class Session {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Unique session key for identification
  @Unique()
  @Property(uid: 1001)
  String sessionKey = '';

  /// Session ID from server
  @Property(uid: 1002)
  String sessionId = '';

  /// When the user logged out (null if still active)
  @Property(uid: 1003)
  DateTime? logoutTime;

  /// When the user logged in
  @Property(uid: 1004)
  DateTime loginTime = DateTime.now();

  /// Session token for authentication
  @Property(uid: 1005)
  String sessionToken = '';

  /// Whether this session is currently active
  @Property(uid: 1006)
  bool active = true;

  /// Whether user is logged in
  @Property(uid: 1007)
  bool isLogin = false;

  /// Whether user logged in via QR code
  @Property(uid: 1008)
  bool isLoginQR = false;

  /// Whether passkey has been migrated
  @Property(uid: 1009)
  bool passkeyMigrated = false;

  // RELATIONSHIPS

  /// Reverse relationship: History entities that reference this session via session ToOne
  /// Links to all history records for this session
  @Backlink('session')
  final histories = ToMany<History>();

  /// Reverse relationship: PrivateData entities that reference this session
  /// Links to all private data records for this session
  @Backlink('session')
  final privateDataRecords = ToMany<PrivateData>();

  /// Reverse relationship: Collection entities that reference this session
  /// Links to all collection records for this session
  @Backlink('session')
  final collections = ToMany<Collection>();

  // HELPER METHODS FOR 1:1 RELATIONSHIPS
  // Note: These will be implemented after ObjectBox code generation

  /// Check if this session has local metadata (1:1 relationship)
  bool get hasLocalMetadata =>
      true; // Placeholder - will be implemented with proper query

  /// Check if this session has manager (1:1 relationship)
  bool get hasManager =>
      true; // Placeholder - will be implemented with proper query

  /// Default constructor
  Session();

  /// Constructor with required fields
  Session.create({
    required this.sessionKey,
    required this.sessionId,
    required this.sessionToken,
    DateTime? loginTime,
    DateTime? logoutTime,
  }) {
    this.loginTime = loginTime ?? DateTime.now();
    this.logoutTime = logoutTime;
  }

  /// Copy constructor for updates
  Session copyWith({
    int? id,
    String? sessionKey,
    String? sessionId,
    DateTime? logoutTime,
    DateTime? loginTime,
    String? sessionToken,
    bool? active,
    bool? isLogin,
    bool? isLoginQR,
    bool? passkeyMigrated,
  }) {
    return Session()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..sessionId = sessionId ?? this.sessionId
      ..logoutTime = logoutTime ?? this.logoutTime
      ..loginTime = loginTime ?? this.loginTime
      ..sessionToken = sessionToken ?? this.sessionToken
      ..active = active ?? this.active
      ..isLogin = isLogin ?? this.isLogin
      ..isLoginQR = isLoginQR ?? this.isLoginQR
      ..passkeyMigrated = passkeyMigrated ?? this.passkeyMigrated;
  }

  /// Mark session as logged out
  void logout() {
    active = false;
    isLogin = false;
    logoutTime = DateTime.now();
  }

  /// Mark session as logged in
  void login({bool isQR = false}) {
    active = true;
    isLogin = true;
    isLoginQR = isQR;
    loginTime = DateTime.now();
    logoutTime = null;
  }

  @override
  String toString() {
    return 'Session{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'sessionId: $sessionId, '
        'logoutTime: $logoutTime, '
        'loginTime: $loginTime, '
        'sessionToken: $sessionToken, '
        'active: $active, '
        'isLogin: $isLogin, '
        'isLoginQR: $isLoginQR, '
        'passkeyMigrated: $passkeyMigrated'
        '}';
  }
}
