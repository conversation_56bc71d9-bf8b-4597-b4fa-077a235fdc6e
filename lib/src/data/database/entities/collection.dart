import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';
import 'sticker.dart';

/// Collection entity for storing sticker collections
/// UID range: 8100-8106
@Entity()
class Collection {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 8100)
  String sessionKey = '';

  /// Collection ID (indexed)
  @Index()
  @Property(uid: 8101)
  String collectionId = '';

  /// Collection name
  @Property(uid: 8102)
  String name = '';

  /// Collection description
  @Property(uid: 8103)
  String description = '';

  /// Thumbnail URL
  @Property(uid: 8104)
  String thumbnailUrl = '';

  /// Whether collection is premium
  @Property(uid: 8105)
  bool isPremium = false;

  /// Number of stickers in collection
  @Property(uid: 8106)
  int stickerCount = 0;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this collection to its parent session
  final session = ToOne<Session>();

  /// Reverse relationship: Sticker entities that belong to this collection
  /// Links to all stickers in this collection
  @Backlink('collection')
  final stickers = ToMany<Sticker>();

  /// Default constructor
  Collection();

  /// Constructor with required fields
  Collection.create({
    required this.sessionKey,
    required this.collectionId,
    this.name = '',
    this.description = '',
    this.thumbnailUrl = '',
    this.isPremium = false,
    this.stickerCount = 0,
  });

  /// Copy constructor for updates
  Collection copyWith({
    int? id,
    String? sessionKey,
    String? collectionId,
    String? name,
    String? description,
    String? thumbnailUrl,
    bool? isPremium,
    int? stickerCount,
  }) {
    return Collection()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..collectionId = collectionId ?? this.collectionId
      ..name = name ?? this.name
      ..description = description ?? this.description
      ..thumbnailUrl = thumbnailUrl ?? this.thumbnailUrl
      ..isPremium = isPremium ?? this.isPremium
      ..stickerCount = stickerCount ?? this.stickerCount;
  }

  /// Update collection information
  void updateInfo({
    String? name,
    String? description,
    String? thumbnailUrl,
    bool? isPremium,
    int? stickerCount,
  }) {
    if (name != null) this.name = name;
    if (description != null) this.description = description;
    if (thumbnailUrl != null) this.thumbnailUrl = thumbnailUrl;
    if (isPremium != null) this.isPremium = isPremium;
    if (stickerCount != null) this.stickerCount = stickerCount;
  }

  /// Increment sticker count
  void incrementStickerCount() {
    stickerCount++;
  }

  /// Decrement sticker count
  void decrementStickerCount() {
    if (stickerCount > 0) {
      stickerCount--;
    }
  }

  /// Set sticker count
  void setStickerCount(int count) {
    stickerCount = count;
  }

  /// Check if collection has thumbnail
  bool get hasThumbnail => thumbnailUrl.isNotEmpty;

  /// Check if collection has stickers
  bool get hasStickers => stickerCount > 0;

  /// Check if collection has name
  bool get hasName => name.isNotEmpty;

  /// Check if collection has description
  bool get hasDescription => description.isNotEmpty;

  /// Get actual sticker count from relationship
  int get actualStickerCount => stickers.length;

  /// Check if sticker count is synchronized with relationship
  bool get isStickerCountSynced => stickerCount == actualStickerCount;

  /// Synchronize sticker count with actual relationship count
  void syncStickerCount() {
    stickerCount = actualStickerCount;
  }

  /// Get all animated stickers in this collection
  List<Sticker> get animatedStickers =>
      stickers.where((sticker) => sticker.isAnimated).toList();

  /// Get all static stickers in this collection
  List<Sticker> get staticStickers =>
      stickers.where((sticker) => !sticker.isAnimated).toList();

  /// Get stickers by name pattern
  List<Sticker> getStickersByName(String namePattern) {
    return stickers
        .where(
          (sticker) =>
              sticker.name.toLowerCase().contains(namePattern.toLowerCase()),
        )
        .toList();
  }

  /// Check if collection has relationship to stickers
  bool get hasStickersRelationship => stickers.isNotEmpty;

  @override
  String toString() {
    return 'Collection{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'collectionId: $collectionId, '
        'name: $name, '
        'description: $description, '
        'thumbnailUrl: $thumbnailUrl, '
        'isPremium: $isPremium, '
        'stickerCount: $stickerCount'
        '}';
  }
}
