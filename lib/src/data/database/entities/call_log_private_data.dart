import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'call_log.dart';

/// Call log private data entity for storing call-specific private data
/// UID range: 7300-7310
@Entity()
class CallLogPrivateData {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 7300)
  String sessionKey = '';

  /// Call ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 7301)
  String callIdField = '';

  /// Call state
  @Property(uid: 7302)
  int callState = 0;

  /// Ended reason
  @Property(uid: 7303)
  int endedReason = 0;

  /// Call duration in seconds
  @Property(uid: 7304)
  int callTimeInSeconds = 0;

  /// Read time
  @Property(uid: 7305)
  DateTime? readTime;

  /// Ended time
  @Property(uid: 7306)
  DateTime? endedTime;

  /// Source information
  @Property(uid: 7307)
  String source = '';

  /// Version number
  @Property(uid: 7308)
  int version = 0;

  /// Creation time
  @Property(uid: 7309)
  DateTime? createTime;

  /// Call type
  @Property(uid: 7310)
  int callType = 0;

  // RELATIONSHIPS

  /// ToOne relationship to CallLog
  /// Links this private data to its parent call log
  final callLog = ToOne<CallLog>();

  /// Get callId from relationship or field (backward compatibility)
  String get callId => callLog.target?.callId ?? callIdField;

  /// Default constructor
  CallLogPrivateData();

  /// Constructor with required fields
  CallLogPrivateData.create({
    required this.sessionKey,
    required String callId, // callId parameter for backward compatibility
    this.callState = 0,
    this.endedReason = 0,
    this.callTimeInSeconds = 0,
    this.readTime,
    this.endedTime,
    this.source = '',
    this.version = 0,
    this.createTime,
    this.callType = 0,
  }) : callIdField = callId {
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  CallLogPrivateData copyWith({
    int? id,
    String? sessionKey,
    String? callId, // callId parameter for backward compatibility
    int? callState,
    int? endedReason,
    int? callTimeInSeconds,
    DateTime? readTime,
    DateTime? endedTime,
    String? source,
    int? version,
    DateTime? createTime,
    int? callType,
  }) {
    final newCallLogPrivateData = CallLogPrivateData()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..callIdField = callId ?? this.callIdField
      ..callState = callState ?? this.callState
      ..endedReason = endedReason ?? this.endedReason
      ..callTimeInSeconds = callTimeInSeconds ?? this.callTimeInSeconds
      ..readTime = readTime ?? this.readTime
      ..endedTime = endedTime ?? this.endedTime
      ..source = source ?? this.source
      ..version = version ?? this.version
      ..createTime = createTime ?? this.createTime
      ..callType = callType ?? this.callType;

    // Copy relationship
    newCallLogPrivateData.callLog.target = callLog.target;
    return newCallLogPrivateData;
  }

  /// Update call state
  void updateCallState(int newState) {
    callState = newState;
    version++;
  }

  /// End the call
  void endCall({int? endedReason, int? callTimeInSeconds}) {
    this.endedReason = endedReason ?? 0;
    this.callTimeInSeconds = callTimeInSeconds ?? 0;
    endedTime = DateTime.now();
    version++;
  }

  /// Mark as read
  void markAsRead() {
    readTime = DateTime.now();
    version++;
  }

  /// Update call type
  void updateCallType(int newType) {
    callType = newType;
    version++;
  }

  /// Get call duration as formatted string
  String get formattedDuration {
    final hours = callTimeInSeconds ~/ 3600;
    final minutes = (callTimeInSeconds % 3600) ~/ 60;
    final seconds = callTimeInSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Check if call is active
  bool get isActive => callState == 1; // Assuming 1 means active

  /// Check if call is ended
  bool get isEnded => endedTime != null;

  /// Check if call has been read
  bool get isRead => readTime != null;

  // RELATIONSHIP HELPER METHODS

  /// Check if this private data has a valid call log relationship
  bool get hasCallLogRelationship => callLog.target != null;

  /// Get call log session key from relationship
  String get relatedCallLogSessionKey => callLog.target?.sessionKey ?? '';

  /// Get call log caller ID from relationship
  String get relatedCallLogCallerId => callLog.target?.callerId ?? '';

  /// Get call log callee ID from relationship
  String get relatedCallLogCalleeId => callLog.target?.calleeId ?? '';

  /// Get caller username from relationship
  String get relatedCallerUsername => callLog.target?.callerUsername ?? '';

  /// Get callee username from relationship
  String get relatedCalleeUsername => callLog.target?.calleeUsername ?? '';

  /// Get effective caller name from relationship
  String get relatedEffectiveCallerName =>
      callLog.target?.effectiveCallerName ?? '';

  /// Get effective callee name from relationship
  String get relatedEffectiveCalleeName =>
      callLog.target?.effectiveCalleeName ?? '';

  /// Check if related call is video call
  bool get isRelatedVideoCall => callLog.target?.isVideoCall ?? false;

  /// Check if related call is incoming
  bool get isRelatedIncomingCall => callLog.target?.isInComingCall ?? false;

  /// Check if related call is outgoing
  bool get isRelatedOutgoingCall => callLog.target?.isOutgoing ?? false;

  /// Check if related call is missed
  bool get isRelatedMissedCall => callLog.target?.isMissedCall ?? false;

  /// Get related call type description
  String get relatedCallTypeDescription =>
      callLog.target?.callTypeDescription ?? '';

  /// Get related call direction description
  String get relatedCallDirectionDescription =>
      callLog.target?.callDirectionDescription ?? '';

  /// Get related call status description
  String get relatedCallStatusDescription =>
      callLog.target?.callStatusDescription ?? '';

  /// Sync call state with related call log
  void syncWithCallLog() {
    if (callLog.target != null) {
      callState = callLog.target!.callState;
      endedReason = callLog.target!.endedReason;
      callTimeInSeconds = callLog.target!.callTimeInSeconds;
      endedTime = callLog.target!.endedTime;
      version++;
    }
  }

  /// Check if call data is synchronized with call log
  bool get isSyncedWithCallLog {
    if (callLog.target == null) return false;
    return callState == callLog.target!.callState &&
        endedReason == callLog.target!.endedReason &&
        callTimeInSeconds == callLog.target!.callTimeInSeconds &&
        endedTime == callLog.target!.endedTime;
  }

  @override
  String toString() {
    return 'CallLogPrivateData{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'callId: $callId, '
        'callState: $callState, '
        'endedReason: $endedReason, '
        'callTimeInSeconds: $callTimeInSeconds, '
        'readTime: $readTime, '
        'endedTime: $endedTime, '
        'source: $source, '
        'version: $version, '
        'createTime: $createTime, '
        'callType: $callType'
        '}';
  }
}
