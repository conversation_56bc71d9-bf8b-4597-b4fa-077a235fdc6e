import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';
import 'channel.dart';
import 'user.dart';

/// Member entity for storing channel member information
/// UID range: 3100-3109
@Entity()
class Member {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed)
  @Index()
  @Property(uid: 3100)
  String workspaceId = '';

  /// Channel ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 3101)
  String channelIdField = '';

  /// User ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 3102)
  String userIdField = '';

  /// Session key reference (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 3103)
  String sessionKeyField = '';

  /// Member nickname in the channel
  @Property(uid: 3104)
  String nickname = '';

  /// Member role
  @Property(uid: 3105)
  String role = '';

  /// Raw roles data
  @Property(uid: 3106)
  String rolesRaw = '';

  /// Member creation time
  @Property(uid: 3107)
  DateTime? createTime;

  /// Member last update time
  @Property(uid: 3108)
  DateTime? updateTime;

  /// Whether this is a partial member record
  @Property(uid: 3109)
  bool isPartial = false;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this member to its session
  final session = ToOne<Session>();

  /// ToOne relationship to Channel
  /// Links this member to its channel
  final channel = ToOne<Channel>();

  /// ToOne relationship to User
  /// Links this member to its user
  final user = ToOne<User>();

  /// Get sessionKey from relationship or field (backward compatibility)
  String get sessionKey => session.target?.sessionKey ?? sessionKeyField;

  /// Get channelId from relationship or field (backward compatibility)
  String get channelId => channel.target?.channelId ?? channelIdField;

  /// Get userId from relationship or field (backward compatibility)
  String get userId => user.target?.userId ?? userIdField;

  /// Composite index field: channelUserId
  String get channelUserId => '${channelId}_$userId';

  /// Composite index field: sessionChannelUser
  String get sessionChannelUser => '${sessionKey}_${channelId}_$userId';

  /// Default constructor
  Member();

  /// Constructor with required fields
  Member.create({
    required this.workspaceId,
    required String channelId, // channelId parameter for backward compatibility
    required String userId, // userId parameter for backward compatibility
    required String
        sessionKey, // sessionKey parameter for backward compatibility
    this.nickname = '',
    this.role = '',
    this.rolesRaw = '',
    this.createTime,
    this.updateTime,
    this.isPartial = false,
  })  : channelIdField = channelId,
        userIdField = userId,
        sessionKeyField = sessionKey {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  Member copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? sessionKey, // sessionKey parameter for backward compatibility
    String? nickname,
    String? role,
    String? rolesRaw,
    DateTime? createTime,
    DateTime? updateTime,
    bool? isPartial,
  }) {
    final newMember = Member()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelIdField = channelId ?? this.channelIdField
      ..userIdField = userId ?? this.userIdField
      ..sessionKeyField = sessionKey ?? this.sessionKeyField
      ..nickname = nickname ?? this.nickname
      ..role = role ?? this.role
      ..rolesRaw = rolesRaw ?? this.rolesRaw
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..isPartial = isPartial ?? this.isPartial;

    // Copy relationships
    newMember.session.target = session.target;
    newMember.channel.target = channel.target;
    newMember.user.target = user.target;
    return newMember;
  }

  /// Update member information
  void updateInfo({
    String? nickname,
    String? role,
    String? rolesRaw,
  }) {
    if (nickname != null) this.nickname = nickname;
    if (role != null) this.role = role;
    if (rolesRaw != null) this.rolesRaw = rolesRaw;
    updateTime = DateTime.now();
  }

  /// Mark as complete member record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Check if member has nickname
  bool get hasNickname => nickname.isNotEmpty;

  /// Get effective display name (nickname or user ID)
  String get effectiveDisplayName => nickname.isNotEmpty ? nickname : userId;

  // RELATIONSHIP HELPER METHODS

  /// Check if this member has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Check if this member has a valid channel relationship
  bool get hasChannelRelationship => channel.target != null;

  /// Check if this member has a valid user relationship
  bool get hasUserRelationship => user.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Get channel name from relationship
  String get relatedChannelName => channel.target?.name ?? '';

  /// Get channel workspace ID from relationship
  String get relatedChannelWorkspaceId => channel.target?.workspaceId ?? '';

  /// Get channel type description from relationship
  String get relatedChannelTypeDescription =>
      channel.target?.channelTypeDescription ?? '';

  /// Check if related channel is DM
  bool get isRelatedChannelDM => channel.target?.isDM ?? false;

  /// Check if related channel is archived
  bool get isRelatedChannelArchived => channel.target?.isArchived ?? false;

  /// Get effective channel name from relationship
  String get relatedEffectiveChannelName =>
      channel.target?.effectiveChannelName ?? '';

  /// Get user username from relationship
  String get relatedUserUsername => user.target?.username ?? '';

  /// Get user display name from relationship (from profile)
  String get relatedUserDisplayName {
    if (user.target?.profiles.isNotEmpty == true) {
      return user.target!.profiles.first.displayName;
    }
    return relatedUserUsername;
  }

  /// Get effective user name from relationship
  String get relatedEffectiveUserName {
    if (relatedUserDisplayName.isNotEmpty) return relatedUserDisplayName;
    if (relatedUserUsername.isNotEmpty) return relatedUserUsername;
    return userId;
  }

  /// Check if related user is online
  bool get isRelatedUserOnline {
    if (user.target?.presences.isNotEmpty == true) {
      final currentPresence = user.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Get member's final display name (priority: nickname > user display name > username > userId)
  String get finalDisplayName {
    if (hasNickname) return nickname;
    if (hasUserRelationship) return relatedEffectiveUserName;
    return userId;
  }

  /// Get role description
  String get roleDescription {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'Channel Owner';
      case 'admin':
        return 'Administrator';
      case 'moderator':
        return 'Moderator';
      case 'member':
        return 'Member';
      case 'guest':
        return 'Guest';
      default:
        return role.isNotEmpty ? role : 'Member';
    }
  }

  /// Check if member is admin or owner
  bool get isAdminOrOwner =>
      role.toLowerCase() == 'owner' || role.toLowerCase() == 'admin';

  /// Check if member is moderator or higher
  bool get isModeratorOrHigher =>
      isAdminOrOwner || role.toLowerCase() == 'moderator';

  /// Get member status description
  String get memberStatusDescription {
    if (isPartial) return 'Partial';
    return 'Complete';
  }

  /// Check if member has special permissions
  bool get hasSpecialPermissions =>
      role.isNotEmpty && role.toLowerCase() != 'member';

  @override
  String toString() {
    return 'Member{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'userId: $userId, '
        'sessionKey: $sessionKey, '
        'nickname: $nickname, '
        'role: $role, '
        'rolesRaw: $rolesRaw, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'isPartial: $isPartial'
        '}';
  }
}
