import 'package:objectbox/objectbox.dart';
import 'session.dart';

/// Session local metadata entity for storing session-specific settings
/// UID range: 1100-1109
@Entity()
class SessionLocalMetadata {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 1100)
  String sessionKey = '';

  /// Whether intro channel view has been shown
  @Property(uid: 1101)
  bool introChannelView = false;

  /// Whether intro translate entire chat has been shown
  @Property(uid: 1102)
  bool introTranslateEntireChat = false;

  /// Whether intro channel list has been shown
  @Property(uid: 1103)
  bool introChannelList = false;

  /// Whether intro call log has been shown
  @Property(uid: 1104)
  bool introCallLog = false;

  /// Whether intro create channel has been shown
  @Property(uid: 1105)
  bool introCreateChannel = false;

  /// Speech language setting
  @Property(uid: 1106)
  String speechLang = '';

  /// Resume ID for session restoration
  @Property(uid: 1107)
  String resumeId = '';

  /// Whether speech to text is enabled
  @Property(uid: 1108)
  bool speechToTextEnable = false;

  /// User update time after (FIXED field)
  @Property(uid: 1109)
  DateTime? userUpdateTimeAfter;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this metadata to its parent session
  final session = ToOne<Session>();

  /// Default constructor
  SessionLocalMetadata();

  /// Constructor with required fields
  SessionLocalMetadata.create({
    required this.sessionKey,
    this.introChannelView = false,
    this.introTranslateEntireChat = false,
    this.introChannelList = false,
    this.introCallLog = false,
    this.introCreateChannel = false,
    this.speechLang = '',
    this.resumeId = '',
    this.speechToTextEnable = false,
    this.userUpdateTimeAfter,
  });

  /// Copy constructor for updates
  SessionLocalMetadata copyWith({
    int? id,
    String? sessionKey,
    bool? introChannelView,
    bool? introTranslateEntireChat,
    bool? introChannelList,
    bool? introCallLog,
    bool? introCreateChannel,
    String? speechLang,
    String? resumeId,
    bool? speechToTextEnable,
    DateTime? userUpdateTimeAfter,
  }) {
    final newMetadata = SessionLocalMetadata()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..introChannelView = introChannelView ?? this.introChannelView
      ..introTranslateEntireChat =
          introTranslateEntireChat ?? this.introTranslateEntireChat
      ..introChannelList = introChannelList ?? this.introChannelList
      ..introCallLog = introCallLog ?? this.introCallLog
      ..introCreateChannel = introCreateChannel ?? this.introCreateChannel
      ..speechLang = speechLang ?? this.speechLang
      ..resumeId = resumeId ?? this.resumeId
      ..speechToTextEnable = speechToTextEnable ?? this.speechToTextEnable
      ..userUpdateTimeAfter = userUpdateTimeAfter ?? this.userUpdateTimeAfter;

    // Copy relationship
    newMetadata.session.target = session.target;
    return newMetadata;
  }

  /// Mark all intro screens as viewed
  void markAllIntrosViewed() {
    introChannelView = true;
    introTranslateEntireChat = true;
    introChannelList = true;
    introCallLog = true;
    introCreateChannel = true;
  }

  // RELATIONSHIP HELPER METHODS

  /// Get effective session key from relationship or fallback to stored value
  String get effectiveSessionKey => session.target?.sessionKey ?? sessionKey;

  /// Check if this metadata has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Set session relationship by finding session with matching sessionKey
  /// This is a helper method for easier relationship management
  void setSessionByKey(String key, List<Session> availableSessions) {
    final matchingSessions =
        availableSessions.where((s) => s.sessionKey == key);

    if (matchingSessions.isNotEmpty) {
      final targetSession = matchingSessions.first;
      session.target = targetSession;
      sessionKey = key; // Keep string field in sync
    }
  }

  /// Reset all intro screens
  void resetIntros() {
    introChannelView = false;
    introTranslateEntireChat = false;
    introChannelList = false;
    introCallLog = false;
    introCreateChannel = false;
  }

  @override
  String toString() {
    return 'SessionLocalMetadata{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'introChannelView: $introChannelView, '
        'introTranslateEntireChat: $introTranslateEntireChat, '
        'introChannelList: $introChannelList, '
        'introCallLog: $introCallLog, '
        'introCreateChannel: $introCreateChannel, '
        'speechLang: $speechLang, '
        'resumeId: $resumeId, '
        'speechToTextEnable: $speechToTextEnable, '
        'userUpdateTimeAfter: $userUpdateTimeAfter'
        '}';
  }
}
