import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'user.dart';

/// Visited profile entity for tracking visited user profiles
/// UID range: 2200-2204
@Entity()
class VisitedProfile {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 2200)
  String sessionKey = '';

  /// Visited user ID (indexed, UID UPDATED) - kept for backward compatibility
  @Index()
  @Property(uid: 2201)
  String visitedUserIdField = '';

  /// Whether the visit has been read (UID UPDATED)
  @Property(uid: 2202)
  bool isRead = false;

  /// Visit creation time (UID UPDATED)
  @Property(uid: 2203)
  DateTime? createTime;

  /// Visit last update time (UID UPDATED)
  @Property(uid: 2204)
  DateTime? updateTime;

  // RELATIONSHIPS

  /// ToOne relationship to User (visited user)
  /// Links this visited profile to the visited user
  final visitedUser = ToOne<User>();

  /// Get visitedUserId from relationship or field (backward compatibility)
  String get visitedUserId => visitedUser.target?.userId ?? visitedUserIdField;

  /// Default constructor
  VisitedProfile();

  /// Constructor with required fields
  VisitedProfile.create({
    required this.sessionKey,
    required String
        visitedUserId, // visitedUserId parameter for backward compatibility
    this.isRead = false,
    this.createTime,
    this.updateTime,
  }) : visitedUserIdField = visitedUserId {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  VisitedProfile copyWith({
    int? id,
    String? sessionKey,
    String? visitedUserId, // visitedUserId parameter for backward compatibility
    bool? isRead,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    final newVisitedProfile = VisitedProfile()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..visitedUserIdField = visitedUserId ?? this.visitedUserIdField
      ..isRead = isRead ?? this.isRead
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;

    // Copy relationship
    newVisitedProfile.visitedUser.target = visitedUser.target;
    return newVisitedProfile;
  }

  /// Mark as read
  void markAsRead() {
    isRead = true;
    updateTime = DateTime.now();
  }

  /// Mark as unread
  void markAsUnread() {
    isRead = false;
    updateTime = DateTime.now();
  }

  /// Update visit time
  void updateVisitTime() {
    updateTime = DateTime.now();
  }

  /// Check if visited profile has relationship to user
  bool get hasVisitedUserRelationship => visitedUser.target != null;

  /// Get visited user name from relationship
  String get visitedUserName => visitedUser.target?.username ?? '';

  /// Get visited user display name from relationship (from profile)
  String get visitedUserDisplayName {
    if (visitedUser.target?.profiles.isNotEmpty == true) {
      return visitedUser.target!.profiles.first.displayName;
    }
    return visitedUserName;
  }

  /// Get effective visited user name
  String get effectiveVisitedUserName {
    if (visitedUserDisplayName.isNotEmpty) return visitedUserDisplayName;
    if (visitedUserName.isNotEmpty) return visitedUserName;
    return visitedUserId;
  }

  /// Check if visited user is online (from relationship)
  bool get isVisitedUserOnline {
    if (visitedUser.target?.presences.isNotEmpty == true) {
      final currentPresence = visitedUser.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Get visited user status from relationship
  String get visitedUserStatus {
    final currentStatus = visitedUser.target?.currentStatus;
    return currentStatus?.formattedStatus ?? '';
  }

  /// Check if visit is recent (within last 24 hours)
  bool get isRecentVisit {
    if (updateTime == null) return false;
    final now = DateTime.now();
    final difference = now.difference(updateTime!);
    return difference.inHours < 24;
  }

  /// Get time since last visit
  String get timeSinceVisit {
    if (updateTime == null) return 'Unknown';
    final now = DateTime.now();
    final difference = now.difference(updateTime!);

    if (difference.inMinutes < 1) return 'Just now';
    if (difference.inMinutes < 60) return '${difference.inMinutes}m ago';
    if (difference.inHours < 24) return '${difference.inHours}h ago';
    if (difference.inDays < 7) return '${difference.inDays}d ago';
    return '${(difference.inDays / 7).floor()}w ago';
  }

  @override
  String toString() {
    return 'VisitedProfile{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'visitedUserId: $visitedUserId, '
        'isRead: $isRead, '
        'createTime: $createTime, '
        'updateTime: $updateTime'
        '}';
  }
}
