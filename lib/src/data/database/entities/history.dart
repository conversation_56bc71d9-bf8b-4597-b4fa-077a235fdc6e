import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';

/// History entity for storing history records
/// UID range: 6100-6106 (Changed to avoid conflict with <PERSON>)
@Entity()
class History {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 6100)
  String sessionKey = '';

  /// History ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 6101)
  String historyId = '';

  /// Entity type (UID UPDATED)
  @Property(uid: 6102)
  String entityType = '';

  /// Entity ID (UID UPDATED)
  @Property(uid: 6103)
  String entityId = '';

  /// Action performed (UID UPDATED)
  @Property(uid: 6104)
  String action = '';

  /// Change data (UID UPDATED)
  @Property(uid: 6105)
  String changeData = '';

  /// Creation time (UID UPDATED)
  @Property(uid: 6106)
  DateTime? createTime;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this history record to its parent session
  final session = ToOne<Session>();

  /// Composite index field: sessionEntityType
  String get sessionEntityType => '${sessionKey}_$entityType';

  /// Composite index field: entityTypeAction
  String get entityTypeAction => '${entityType}_$action';

  /// Composite index field: sessionCreateTime
  String get sessionCreateTime =>
      '${sessionKey}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Default constructor
  History();

  /// Constructor with required fields
  History.create({
    required this.sessionKey,
    required this.historyId,
    required this.entityType,
    required this.entityId,
    required this.action,
    this.changeData = '',
    this.createTime,
  }) {
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  History copyWith({
    int? id,
    String? sessionKey,
    String? historyId,
    String? entityType,
    String? entityId,
    String? action,
    String? changeData,
    DateTime? createTime,
  }) {
    final newHistory = History()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..historyId = historyId ?? this.historyId
      ..entityType = entityType ?? this.entityType
      ..entityId = entityId ?? this.entityId
      ..action = action ?? this.action
      ..changeData = changeData ?? this.changeData
      ..createTime = createTime ?? this.createTime;

    // Copy relationship
    newHistory.session.target = session.target;
    return newHistory;
  }

  /// Check if history has change data
  bool get hasChangeData => changeData.isNotEmpty;

  /// Get action type
  String get actionType => action.toLowerCase();

  /// Check if action is create
  bool get isCreateAction => actionType == 'create';

  /// Check if action is update
  bool get isUpdateAction => actionType == 'update';

  /// Check if action is delete
  bool get isDeleteAction => actionType == 'delete';

  /// Check if action is read
  bool get isReadAction => actionType == 'read';

  // RELATIONSHIP HELPER METHODS

  /// Get effective session key from relationship or fallback to stored value
  String get effectiveSessionKey => session.target?.sessionKey ?? sessionKey;

  /// Check if this history has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Set session relationship by finding session with matching sessionKey
  /// This is a helper method for easier relationship management
  void setSessionByKey(String key, List<Session> availableSessions) {
    final matchingSessions =
        availableSessions.where((s) => s.sessionKey == key);

    if (matchingSessions.isNotEmpty) {
      final targetSession = matchingSessions.first;
      session.target = targetSession;
      sessionKey = key; // Keep string field in sync
    }
  }

  @override
  String toString() {
    return 'History{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'historyId: $historyId, '
        'entityType: $entityType, '
        'entityId: $entityId, '
        'action: $action, '
        'changeData: ${changeData.length > 50 ? changeData.substring(0, 50) + '...' : changeData}, '
        'createTime: $createTime'
        '}';
  }
}
