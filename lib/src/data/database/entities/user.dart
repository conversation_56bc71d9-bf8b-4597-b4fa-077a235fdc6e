import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';
import 'profile.dart';
import 'friend.dart';
import 'user_presence.dart';
import 'user_status.dart';
import 'user_private_data.dart';
import 'visited_profile.dart';
import 'private_data.dart';
import 'call_log.dart';
import 'channel.dart';
import 'member.dart';
import 'message.dart';

/// User entity for storing user information
/// UID range: 2001-2013
@Entity()
class User {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, NEW FIELD)
  @Index()
  @Property(uid: 2013)
  String sessionKey = '';

  /// Unique user ID (indexed)
  @Unique()
  @Index()
  @Property(uid: 2001)
  String userId = '';

  /// Username
  @Property(uid: 2002)
  String username = '';

  /// User creation time
  @Property(uid: 2003)
  DateTime? createTime;

  /// User last update time
  @Property(uid: 2004)
  DateTime? updateTime;

  /// User type (integer enum)
  @Property(uid: 2005)
  int userType = 0;

  /// User connect link
  @Property(uid: 2006)
  String userConnectLink = '';

  /// Media permission setting
  @Property(uid: 2007)
  int mediaPermissionSetting = 0;

  /// Global notification status
  @Property(uid: 2009)
  bool globalNotificationStatus = true;

  /// SIP credentials
  @Property(uid: 2010)
  String sipCredentials = '';

  /// SIP address
  @Property(uid: 2011)
  String sipAddress = '';

  /// Whether this is a partial user record
  @Property(uid: 2012)
  bool isPartial = false;

  /// Composite index field: sessionUserId
  String get sessionUserId => '${sessionKey}_$userId';

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this user to its session
  final session = ToOne<Session>();

  /// Reverse relationship: Profile entities that reference this user
  /// Links to all profiles for this user
  @Backlink('user')
  final profiles = ToMany<Profile>();

  /// Reverse relationship: Friend entities where this user is the owner
  /// Links to all friendships owned by this user
  @Backlink('ownerUser')
  final ownedFriendships = ToMany<Friend>();

  /// Reverse relationship: Friend entities where this user is the friend
  /// Links to all friendships where this user is the friend
  @Backlink('friendUser')
  final friendships = ToMany<Friend>();

  /// Reverse relationship: UserPresence entities that reference this user
  /// Links to all presence records for this user
  @Backlink('user')
  final presences = ToMany<UserPresence>();

  /// Reverse relationship: UserStatus entities that reference this user
  /// Links to all status records for this user
  @Backlink('user')
  final statuses = ToMany<UserStatus>();

  /// Reverse relationship: UserPrivateData entities that reference this user
  /// Links to all private data records for this user
  @Backlink('user')
  final privateDataRecords = ToMany<UserPrivateData>();

  /// Reverse relationship: VisitedProfile entities that reference this user as visited user
  /// Links to all visited profile records for this user
  @Backlink('visitedUser')
  final visitedProfiles = ToMany<VisitedProfile>();

  /// Reverse relationship: PrivateData entities that reference this user
  /// Links to all general private data records for this user
  @Backlink('user')
  final generalPrivateDataRecords = ToMany<PrivateData>();

  /// Reverse relationship: CallLog entities that reference this user as caller
  /// Links to all call logs where this user is the caller
  @Backlink('callerUser')
  final callerCallLogs = ToMany<CallLog>();

  /// Reverse relationship: CallLog entities that reference this user as callee
  /// Links to all call logs where this user is the callee
  @Backlink('calleeUser')
  final calleeCallLogs = ToMany<CallLog>();

  /// Reverse relationship: Channel entities that reference this user as owner
  /// Links to all channels where this user is the owner
  @Backlink('ownerUser')
  final ownedChannels = ToMany<Channel>();

  /// Reverse relationship: Channel entities that reference this user as recipient
  /// Links to all channels where this user is the recipient (DM channels)
  @Backlink('recipientUser')
  final recipientChannels = ToMany<Channel>();

  /// Reverse relationship: Member entities that reference this user
  /// Links to all channel memberships for this user
  @Backlink('user')
  final memberships = ToMany<Member>();

  /// Reverse relationship: Message entities that reference this user as sender
  /// Links to all messages sent by this user
  @Backlink('sender')
  final sentMessages = ToMany<Message>();

  /// Default constructor
  User();

  /// Constructor with required fields
  User.create({
    required this.sessionKey,
    required this.userId,
    this.username = '',
    this.createTime,
    this.updateTime,
    this.userType = 0,
    this.userConnectLink = '',
    this.mediaPermissionSetting = 0,
    this.globalNotificationStatus = true,
    this.sipCredentials = '',
    this.sipAddress = '',
    this.isPartial = false,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  User copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? username,
    DateTime? createTime,
    DateTime? updateTime,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    String? sipCredentials,
    String? sipAddress,
    bool? isPartial,
  }) {
    return User()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..username = username ?? this.username
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..userType = userType ?? this.userType
      ..userConnectLink = userConnectLink ?? this.userConnectLink
      ..mediaPermissionSetting =
          mediaPermissionSetting ?? this.mediaPermissionSetting
      ..globalNotificationStatus =
          globalNotificationStatus ?? this.globalNotificationStatus
      ..sipCredentials = sipCredentials ?? this.sipCredentials
      ..sipAddress = sipAddress ?? this.sipAddress
      ..isPartial = isPartial ?? this.isPartial;
  }

  /// Update user information
  void updateInfo({
    String? username,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    String? sipCredentials,
    String? sipAddress,
  }) {
    if (username != null) this.username = username;
    if (userType != null) this.userType = userType;
    if (userConnectLink != null) this.userConnectLink = userConnectLink;
    if (mediaPermissionSetting != null)
      this.mediaPermissionSetting = mediaPermissionSetting;
    if (globalNotificationStatus != null)
      this.globalNotificationStatus = globalNotificationStatus;
    if (sipCredentials != null) this.sipCredentials = sipCredentials;
    if (sipAddress != null) this.sipAddress = sipAddress;
    updateTime = DateTime.now();
  }

  /// Mark as complete user record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  // RELATIONSHIP HELPER METHODS

  /// Get effective session key from relationship or field
  String get effectiveSessionKey {
    return session.target?.sessionKey ?? sessionKey;
  }

  /// Check if user has session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Get related session ID
  String get relatedSessionId {
    return session.target?.sessionId ?? '';
  }

  /// Get related session token
  String get relatedSessionToken {
    return session.target?.sessionToken ?? '';
  }

  /// Get all friend relationships (both owned and received)
  List<Friend> get allFriendships {
    final allFriends = <Friend>[];
    allFriends.addAll(ownedFriendships);
    allFriends.addAll(friendships);
    return allFriends;
  }

  /// Get active friendships only
  List<Friend> get activeFriendships {
    return allFriendships.where((f) => f.isActive).toList();
  }

  /// Get pending friendship requests
  List<Friend> get pendingFriendships {
    return allFriendships.where((f) => f.isPending).toList();
  }

  /// Get current user status
  UserStatus? get currentStatus {
    if (statuses.isEmpty) return null;
    // Return the most recent status
    final sortedStatuses = statuses.toList()
      ..sort(
        (a, b) => (b.updateTime ?? DateTime.now())
            .compareTo(a.updateTime ?? DateTime.now()),
      );
    return sortedStatuses.first;
  }

  /// Get current user presence
  UserPresence? get currentPresence {
    if (presences.isEmpty) return null;
    // Return the most recent presence
    final sortedPresences = presences.toList()
      ..sort(
        (a, b) => (b.updateTime ?? DateTime.now())
            .compareTo(a.updateTime ?? DateTime.now()),
      );
    return sortedPresences.first;
  }

  /// Get private data records count
  int get privateDataRecordsCount => privateDataRecords.length;

  /// Check if user has private data records
  bool get hasPrivateDataRecords => privateDataRecords.isNotEmpty;

  /// Get private data by source
  UserPrivateData? getPrivateDataBySource(String source) {
    try {
      return privateDataRecords.firstWhere((data) => data.source == source);
    } catch (e) {
      return null;
    }
  }

  /// Get visited profiles count
  int get visitedProfilesCount => visitedProfiles.length;

  /// Check if user has visited profiles
  bool get hasVisitedProfiles => visitedProfiles.isNotEmpty;

  /// Get recent visited profiles (within last 24 hours)
  List<VisitedProfile> get recentVisitedProfiles {
    return visitedProfiles.where((profile) => profile.isRecentVisit).toList();
  }

  /// Get unread visited profiles
  List<VisitedProfile> get unreadVisitedProfiles {
    return visitedProfiles.where((profile) => !profile.isRead).toList();
  }

  // CALL LOG HELPER METHODS

  /// Get all call logs (both caller and callee)
  List<CallLog> get allCallLogs {
    final allLogs = <CallLog>[];
    allLogs.addAll(callerCallLogs);
    allLogs.addAll(calleeCallLogs);
    return allLogs;
  }

  /// Get caller call logs count
  int get callerCallLogsCount => callerCallLogs.length;

  /// Get callee call logs count
  int get calleeCallLogsCount => calleeCallLogs.length;

  /// Get total call logs count
  int get totalCallLogsCount => callerCallLogsCount + calleeCallLogsCount;

  /// Check if user has call logs
  bool get hasCallLogs => totalCallLogsCount > 0;

  /// Get recent call logs (last 20)
  List<CallLog> get recentCallLogs {
    final sortedLogs = allCallLogs
      ..sort((a, b) =>
          (b.createTime ?? DateTime(0)).compareTo(a.createTime ?? DateTime(0)));
    return sortedLogs.take(20).toList();
  }

  /// Get missed call logs
  List<CallLog> get missedCallLogs {
    return allCallLogs.where((log) => log.isMissedCall).toList();
  }

  /// Get video call logs
  List<CallLog> get videoCallLogs {
    return allCallLogs.where((log) => log.isVideoCall).toList();
  }

  /// Get voice call logs
  List<CallLog> get voiceCallLogs {
    return allCallLogs.where((log) => !log.isVideoCall).toList();
  }

  /// Get incoming call logs
  List<CallLog> get incomingCallLogs {
    return allCallLogs.where((log) => log.isInComingCall).toList();
  }

  /// Get outgoing call logs
  List<CallLog> get outgoingCallLogs {
    return allCallLogs.where((log) => log.isOutgoing).toList();
  }

  /// Get missed calls count
  int get missedCallsCount => missedCallLogs.length;

  /// Check if user has missed calls
  bool get hasMissedCalls => missedCallsCount > 0;

  // CHANNEL HELPER METHODS

  /// Get all channels (both owned and recipient)
  List<Channel> get allChannels {
    final allChannelsList = <Channel>[];
    allChannelsList.addAll(ownedChannels);
    allChannelsList.addAll(recipientChannels);
    return allChannelsList;
  }

  /// Get owned channels count
  int get ownedChannelsCount => ownedChannels.length;

  /// Get recipient channels count
  int get recipientChannelsCount => recipientChannels.length;

  /// Get total channels count
  int get totalChannelsCount => ownedChannelsCount + recipientChannelsCount;

  /// Check if user has channels
  bool get hasChannels => totalChannelsCount > 0;

  /// Get DM channels (where user is recipient)
  List<Channel> get dmChannels {
    return recipientChannels.where((channel) => channel.isDM).toList();
  }

  /// Get owned non-DM channels
  List<Channel> get ownedNonDMChannels {
    return ownedChannels.where((channel) => !channel.isDM).toList();
  }

  /// Get active channels (not archived)
  List<Channel> get activeChannels {
    return allChannels.where((channel) => !channel.isArchived).toList();
  }

  /// Get archived channels
  List<Channel> get archivedChannels {
    return allChannels.where((channel) => channel.isArchived).toList();
  }

  /// Get channels with avatars
  List<Channel> get channelsWithAvatars {
    return allChannels.where((channel) => channel.hasAvatar).toList();
  }

  /// Get recent channels (last 20, sorted by update time)
  List<Channel> get recentChannels {
    final sortedChannels = allChannels
      ..sort((a, b) =>
          (b.updateTime ?? DateTime(0)).compareTo(a.updateTime ?? DateTime(0)));
    return sortedChannels.take(20).toList();
  }

  /// Get DM channels count
  int get dmChannelsCount => dmChannels.length;

  /// Get active channels count
  int get activeChannelsCount => activeChannels.length;

  /// Get archived channels count
  int get archivedChannelsCount => archivedChannels.length;

  /// Check if user has DM channels
  bool get hasDMChannels => dmChannelsCount > 0;

  /// Check if user has archived channels
  bool get hasArchivedChannels => archivedChannelsCount > 0;

  /// Get channels by type
  List<Channel> getChannelsByType(int channelType) {
    return allChannels
        .where((channel) => channel.channelTypeRaw == channelType)
        .toList();
  }

  /// Get channels by workspace
  List<Channel> getChannelsByWorkspace(String workspaceId) {
    return allChannels
        .where((channel) => channel.workspaceId == workspaceId)
        .toList();
  }

  // MEMBERSHIP HELPER METHODS

  /// Get memberships count
  int get membershipsCount => memberships.length;

  /// Check if user has memberships
  bool get hasMemberships => memberships.isNotEmpty;

  /// Get channels where user is member (via memberships)
  List<Channel> get memberChannels {
    return memberships
        .where((membership) => membership.hasChannelRelationship)
        .map((membership) => membership.channel.target!)
        .toList();
  }

  /// Get memberships where user is owner
  List<Member> get ownerMemberships {
    return memberships
        .where((membership) => membership.role.toLowerCase() == 'owner')
        .toList();
  }

  /// Get memberships where user is admin
  List<Member> get adminMemberships {
    return memberships
        .where((membership) => membership.role.toLowerCase() == 'admin')
        .toList();
  }

  /// Get memberships where user is moderator
  List<Member> get moderatorMemberships {
    return memberships
        .where((membership) => membership.role.toLowerCase() == 'moderator')
        .toList();
  }

  /// Get regular memberships (no special role)
  List<Member> get regularMemberships {
    return memberships.where((membership) {
      final role = membership.role.toLowerCase();
      return role != 'owner' && role != 'admin' && role != 'moderator';
    }).toList();
  }

  /// Get memberships with nicknames
  List<Member> get membershipsWithNicknames {
    return memberships.where((membership) => membership.hasNickname).toList();
  }

  /// Get memberships in active channels
  List<Member> get activeMemberships {
    return memberships
        .where((membership) => !membership.isRelatedChannelArchived)
        .toList();
  }

  /// Get memberships in archived channels
  List<Member> get archivedMemberships {
    return memberships
        .where((membership) => membership.isRelatedChannelArchived)
        .toList();
  }

  /// Get memberships in DM channels
  List<Member> get dmMemberships {
    return memberships
        .where((membership) => membership.isRelatedChannelDM)
        .toList();
  }

  /// Get owner memberships count
  int get ownerMembershipsCount => ownerMemberships.length;

  /// Get admin memberships count
  int get adminMembershipsCount => adminMemberships.length;

  /// Get moderator memberships count
  int get moderatorMembershipsCount => moderatorMemberships.length;

  /// Get active memberships count
  int get activeMembershipsCount => activeMemberships.length;

  /// Check if user has owner memberships
  bool get hasOwnerMemberships => ownerMembershipsCount > 0;

  /// Check if user has admin memberships
  bool get hasAdminMemberships => adminMembershipsCount > 0;

  /// Check if user has special role memberships
  bool get hasSpecialRoleMemberships =>
      hasOwnerMemberships ||
      hasAdminMemberships ||
      moderatorMembershipsCount > 0;

  /// Get membership in specific channel
  Member? getMembershipInChannel(String channelId) {
    return memberships
        .where((membership) => membership.channelId == channelId)
        .firstOrNull;
  }

  /// Check if user is member of specific channel
  bool isMemberOfChannel(String channelId) {
    return getMembershipInChannel(channelId) != null;
  }

  /// Check if user is admin or owner in specific channel
  bool isAdminOrOwnerInChannel(String channelId) {
    final membership = getMembershipInChannel(channelId);
    return membership?.isAdminOrOwner ?? false;
  }

  /// Get memberships by workspace
  List<Member> getMembershipsByWorkspace(String workspaceId) {
    return memberships
        .where((membership) => membership.workspaceId == workspaceId)
        .toList();
  }

  // MESSAGE HELPER METHODS

  /// Get sent messages count
  int get sentMessagesCount => sentMessages.length;

  /// Check if user has sent messages
  bool get hasSentMessages => sentMessages.isNotEmpty;

  /// Get latest sent message
  Message? get latestSentMessage {
    if (sentMessages.isEmpty) return null;
    return sentMessages.reduce((a, b) =>
        (a.createTime?.millisecondsSinceEpoch ?? 0) >
                (b.createTime?.millisecondsSinceEpoch ?? 0)
            ? a
            : b);
  }

  /// Get first sent message
  Message? get firstSentMessage {
    if (sentMessages.isEmpty) return null;
    return sentMessages.reduce((a, b) =>
        (a.createTime?.millisecondsSinceEpoch ?? 0) <
                (b.createTime?.millisecondsSinceEpoch ?? 0)
            ? a
            : b);
  }

  /// Get successful sent messages
  List<Message> get successfulSentMessages {
    return sentMessages.where((message) => message.isSuccessful).toList();
  }

  /// Get failed sent messages
  List<Message> get failedSentMessages {
    return sentMessages.where((message) => message.isFailed).toList();
  }

  /// Get pending sent messages
  List<Message> get pendingSentMessages {
    return sentMessages.where((message) => message.isPending).toList();
  }

  /// Get sent messages with attachments
  List<Message> get sentMessagesWithAttachments {
    return sentMessages.where((message) => message.hasAttachments).toList();
  }

  /// Get sent messages with reactions
  List<Message> get sentMessagesWithReactions {
    return sentMessages.where((message) => message.hasReactions).toList();
  }

  /// Get sent messages with mentions
  List<Message> get sentMessagesWithMentions {
    return sentMessages.where((message) => message.hasMentions).toList();
  }

  /// Get pinned sent messages
  List<Message> get pinnedSentMessages {
    return sentMessages.where((message) => message.isPinned).toList();
  }

  /// Get recent sent messages (last 50, sorted by create time)
  List<Message> get recentSentMessages {
    final sortedMessages = sentMessages.toList()
      ..sort((a, b) =>
          (b.createTime ?? DateTime(0)).compareTo(a.createTime ?? DateTime(0)));
    return sortedMessages.take(50).toList();
  }

  /// Get today's sent messages
  List<Message> get todaySentMessages {
    return sentMessages.where((message) => message.isToday).toList();
  }

  /// Get successful sent messages count
  int get successfulSentMessagesCount => successfulSentMessages.length;

  /// Get failed sent messages count
  int get failedSentMessagesCount => failedSentMessages.length;

  /// Get pending sent messages count
  int get pendingSentMessagesCount => pendingSentMessages.length;

  /// Get sent messages with attachments count
  int get sentMessagesWithAttachmentsCount =>
      sentMessagesWithAttachments.length;

  /// Check if user has failed sent messages
  bool get hasFailedSentMessages => failedSentMessagesCount > 0;

  /// Check if user has pending sent messages
  bool get hasPendingSentMessages => pendingSentMessagesCount > 0;

  /// Check if user has pinned sent messages
  bool get hasPinnedSentMessages => pinnedSentMessages.isNotEmpty;

  /// Get sent messages in specific channel
  List<Message> getSentMessagesInChannel(String channelId) {
    return sentMessages
        .where((message) => message.channelId == channelId)
        .toList();
  }

  /// Get sent messages by type
  List<Message> getSentMessagesByType(int messageType) {
    return sentMessages
        .where((message) => message.messageTypeRaw == messageType)
        .toList();
  }

  /// Get sent messages by status
  List<Message> getSentMessagesByStatus(int messageStatus) {
    return sentMessages
        .where((message) => message.messageStatusRaw == messageStatus)
        .toList();
  }

  /// Get sent messages in date range
  List<Message> getSentMessagesInDateRange(DateTime start, DateTime end) {
    return sentMessages.where((message) {
      final createTime = message.createTime;
      if (createTime == null) return false;
      return createTime.isAfter(start) && createTime.isBefore(end);
    }).toList();
  }

  /// Get sent messages by workspace
  List<Message> getSentMessagesByWorkspace(String workspaceId) {
    return sentMessages
        .where((message) => message.workspaceId == workspaceId)
        .toList();
  }

  @override
  String toString() {
    return 'User{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'username: $username, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'userType: $userType, '
        'userConnectLink: $userConnectLink, '
        'mediaPermissionSetting: $mediaPermissionSetting, '
        'globalNotificationStatus: $globalNotificationStatus, '
        'sipCredentials: $sipCredentials, '
        'sipAddress: $sipAddress, '
        'isPartial: $isPartial'
        '}';
  }
}
