import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'message.dart';

/// Attachment entity for storing message attachments
/// UID range: 3300-3313
@Entity()
class Attachment {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Attachment ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3300)
  String attachmentId = '';

  /// Message ID field (indexed, for backward compatibility)
  @Index()
  @Property(uid: 3314)
  String messageIdField = '';

  /// Attachment reference (UID UPDATED)
  @Property(uid: 3301)
  String ref = '';

  /// Sticker raw data (UID UPDATED)
  @Property(uid: 3302)
  String stickerRaw = '';

  /// Photo raw data (UID UPDATED)
  @Property(uid: 3303)
  String photoRaw = '';

  /// Audio raw data (UID UPDATED)
  @Property(uid: 3304)
  String audioRaw = '';

  /// Video raw data (UID UPDATED)
  @Property(uid: 3305)
  String videoRaw = '';

  /// Voice message raw data (UID UPDATED)
  @Property(uid: 3306)
  String voiceMessageRaw = '';

  /// Video message raw data (UID UPDATED)
  @Property(uid: 3307)
  String videoMessageRaw = '';

  /// Media message raw data (UID UPDATED)
  @Property(uid: 3308)
  String mediaMessageRaw = '';

  /// File message raw data (UID UPDATED)
  @Property(uid: 3309)
  String fileMessageRaw = '';

  /// Undefined message raw data (UID UPDATED)
  @Property(uid: 3310)
  String undefinedMessageRaw = '';

  /// Whether this is a temporary attachment (UID UPDATED)
  @Property(uid: 3311)
  bool isTemp = false;

  /// Attachment status (UID UPDATED)
  @Property(uid: 3312)
  int attachmentStatusRaw = 0;

  /// Whether this is a partial attachment record (UID UPDATED)
  @Property(uid: 3313)
  bool isPartial = false;

  // RELATIONSHIPS

  /// ToOne relationship to Message
  /// Links this attachment to its message
  final message = ToOne<Message>();

  /// Get messageId from relationship or field (backward compatibility)
  String get messageId => message.target?.messageId ?? messageIdField;

  /// Default constructor
  Attachment();

  /// Constructor with required fields
  Attachment.create({
    required this.attachmentId,
    required String messageId, // messageId parameter for backward compatibility
    this.ref = '',
    this.stickerRaw = '',
    this.photoRaw = '',
    this.audioRaw = '',
    this.videoRaw = '',
    this.voiceMessageRaw = '',
    this.videoMessageRaw = '',
    this.mediaMessageRaw = '',
    this.fileMessageRaw = '',
    this.undefinedMessageRaw = '',
    this.isTemp = false,
    this.attachmentStatusRaw = 0,
    this.isPartial = false,
  }) : messageIdField = messageId;

  /// Copy constructor for updates
  Attachment copyWith({
    int? id,
    String? attachmentId,
    String? messageId, // messageId parameter for backward compatibility
    String? ref,
    String? stickerRaw,
    String? photoRaw,
    String? audioRaw,
    String? videoRaw,
    String? voiceMessageRaw,
    String? videoMessageRaw,
    String? mediaMessageRaw,
    String? fileMessageRaw,
    String? undefinedMessageRaw,
    bool? isTemp,
    int? attachmentStatusRaw,
    bool? isPartial,
  }) {
    final newAttachment = Attachment()
      ..id = id ?? this.id
      ..attachmentId = attachmentId ?? this.attachmentId
      ..messageIdField = messageId ?? this.messageIdField
      ..ref = ref ?? this.ref
      ..stickerRaw = stickerRaw ?? this.stickerRaw
      ..photoRaw = photoRaw ?? this.photoRaw
      ..audioRaw = audioRaw ?? this.audioRaw
      ..videoRaw = videoRaw ?? this.videoRaw
      ..voiceMessageRaw = voiceMessageRaw ?? this.voiceMessageRaw
      ..videoMessageRaw = videoMessageRaw ?? this.videoMessageRaw
      ..mediaMessageRaw = mediaMessageRaw ?? this.mediaMessageRaw
      ..fileMessageRaw = fileMessageRaw ?? this.fileMessageRaw
      ..undefinedMessageRaw = undefinedMessageRaw ?? this.undefinedMessageRaw
      ..isTemp = isTemp ?? this.isTemp
      ..attachmentStatusRaw = attachmentStatusRaw ?? this.attachmentStatusRaw
      ..isPartial = isPartial ?? this.isPartial;

    // Copy relationship
    newAttachment.message.target = message.target;
    return newAttachment;
  }

  /// Mark as complete attachment record
  void markAsComplete() {
    isPartial = false;
  }

  /// Mark as temporary
  void markAsTemp() {
    isTemp = true;
  }

  /// Mark as permanent
  void markAsPermanent() {
    isTemp = false;
  }

  /// Check if attachment is a sticker
  bool get isSticker => stickerRaw.isNotEmpty;

  /// Check if attachment is a photo
  bool get isPhoto => photoRaw.isNotEmpty;

  /// Check if attachment is audio
  bool get isAudio => audioRaw.isNotEmpty;

  /// Check if attachment is video
  bool get isVideo => videoRaw.isNotEmpty;

  /// Check if attachment is voice message
  bool get isVoiceMessage => voiceMessageRaw.isNotEmpty;

  /// Check if attachment is video message
  bool get isVideoMessage => videoMessageRaw.isNotEmpty;

  /// Check if attachment is media message
  bool get isMediaMessage => mediaMessageRaw.isNotEmpty;

  /// Check if attachment is file message
  bool get isFileMessage => fileMessageRaw.isNotEmpty;

  /// Get attachment type as string
  String get attachmentType {
    if (isSticker) return 'sticker';
    if (isPhoto) return 'photo';
    if (isAudio) return 'audio';
    if (isVideo) return 'video';
    if (isVoiceMessage) return 'voice';
    if (isVideoMessage) return 'video_message';
    if (isMediaMessage) return 'media';
    if (isFileMessage) return 'file';
    return 'undefined';
  }

  // RELATIONSHIP HELPER METHODS

  /// Check if this attachment has a valid message relationship
  bool get hasMessageRelationship => message.target != null;

  /// Get message content from relationship
  String get relatedMessageContent => message.target?.content ?? '';

  /// Get message type from relationship
  int get relatedMessageType => message.target?.messageTypeRaw ?? 0;

  /// Get message status from relationship
  int get relatedMessageStatus => message.target?.messageStatusRaw ?? 0;

  /// Get message channel ID from relationship
  String get relatedMessageChannelId => message.target?.channelId ?? '';

  /// Get message sender ID from relationship
  String get relatedMessageSenderId => message.target?.userId ?? '';

  /// Get message session key from relationship
  String get relatedMessageSessionKey => message.target?.sessionKey ?? '';

  /// Check if related message is successful
  bool get isRelatedMessageSuccessful => message.target?.isSuccessful ?? false;

  /// Check if related message is failed
  bool get isRelatedMessageFailed => message.target?.isFailed ?? false;

  /// Check if related message is pending
  bool get isRelatedMessagePending => message.target?.isPending ?? false;

  /// Check if related message is pinned
  bool get isRelatedMessagePinned => message.target?.isPinned ?? false;

  /// Get related message create time
  DateTime? get relatedMessageCreateTime => message.target?.createTime;

  /// Get related message sender username
  String get relatedMessageSenderUsername =>
      message.target?.relatedSenderUsername ?? '';

  /// Get related message sender display name
  String get relatedMessageSenderDisplayName =>
      message.target?.relatedSenderDisplayName ?? '';

  /// Get related message channel name
  String get relatedMessageChannelName =>
      message.target?.relatedChannelName ?? '';

  /// Check if related message sender is online
  bool get isRelatedMessageSenderOnline =>
      message.target?.isSenderOnline ?? false;

  /// Get attachment status description
  String get attachmentStatusDescription {
    switch (attachmentStatusRaw) {
      case 0:
        return 'Pending';
      case 1:
        return 'Uploading';
      case 2:
        return 'Uploaded';
      case 3:
        return 'Failed';
      case 4:
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  /// Check if attachment is pending
  bool get isPending => attachmentStatusRaw == 0;

  /// Check if attachment is uploading
  bool get isUploading => attachmentStatusRaw == 1;

  /// Check if attachment is uploaded
  bool get isUploaded => attachmentStatusRaw == 2;

  /// Check if attachment failed
  bool get isFailed => attachmentStatusRaw == 3;

  /// Check if attachment is cancelled
  bool get isCancelled => attachmentStatusRaw == 4;

  /// Check if attachment is successful (uploaded)
  bool get isSuccessful => isUploaded;

  /// Check if attachment has content
  bool get hasContent {
    return stickerRaw.isNotEmpty ||
        photoRaw.isNotEmpty ||
        audioRaw.isNotEmpty ||
        videoRaw.isNotEmpty ||
        voiceMessageRaw.isNotEmpty ||
        videoMessageRaw.isNotEmpty ||
        mediaMessageRaw.isNotEmpty ||
        fileMessageRaw.isNotEmpty ||
        undefinedMessageRaw.isNotEmpty;
  }

  /// Check if attachment is media type (photo, video, audio)
  bool get isMediaType => isPhoto || isVideo || isAudio || isMediaMessage;

  /// Check if attachment is message type (voice, video message)
  bool get isMessageType => isVoiceMessage || isVideoMessage;

  /// Get attachment type description
  String get attachmentTypeDescription {
    switch (attachmentType) {
      case 'sticker':
        return 'Sticker';
      case 'photo':
        return 'Photo';
      case 'audio':
        return 'Audio';
      case 'video':
        return 'Video';
      case 'voice':
        return 'Voice Message';
      case 'video_message':
        return 'Video Message';
      case 'media':
        return 'Media';
      case 'file':
        return 'File';
      default:
        return 'Unknown';
    }
  }

  /// Get attachment size (if available in raw data)
  int get estimatedSize {
    // This is a rough estimation based on content length
    final totalContent = stickerRaw.length +
        photoRaw.length +
        audioRaw.length +
        videoRaw.length +
        voiceMessageRaw.length +
        videoMessageRaw.length +
        mediaMessageRaw.length +
        fileMessageRaw.length +
        undefinedMessageRaw.length;
    return totalContent;
  }

  /// Check if attachment is large (estimated size > 1MB)
  bool get isLarge => estimatedSize > 1024 * 1024;

  @override
  String toString() {
    return 'Attachment{'
        'id: $id, '
        'attachmentId: $attachmentId, '
        'ref: $ref, '
        'type: $attachmentType, '
        'attachmentStatusRaw: $attachmentStatusRaw, '
        'isTemp: $isTemp, '
        'isPartial: $isPartial'
        '}';
  }
}
