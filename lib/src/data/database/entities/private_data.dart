import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'session.dart';
import 'user.dart';

/// Private data entity for storing private data
/// UID range: 7001-7004
@Entity()
class PrivateData {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 7001)
  String sessionKeyField = '';

  /// User ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 7002)
  String userIdField = '';

  /// Creation time
  @Property(uid: 7003)
  DateTime? createTime;

  /// Last update time
  @Property(uid: 7004)
  DateTime? updateTime;

  // RELATIONSHIPS

  /// ToOne relationship to Session
  /// Links this private data to its parent session
  final session = ToOne<Session>();

  /// ToOne relationship to User
  /// Links this private data to its user
  final user = ToOne<User>();

  /// Get sessionKey from relationship or field (backward compatibility)
  String get sessionKey => session.target?.sessionKey ?? sessionKey<PERSON>ield;

  /// Get userId from relationship or field (backward compatibility)
  String get userId => user.target?.userId ?? userIdField;

  /// Default constructor
  PrivateData();

  /// Constructor with required fields
  PrivateData.create({
    required String
        sessionKey, // sessionKey parameter for backward compatibility
    required String userId, // userId parameter for backward compatibility
    this.createTime,
    this.updateTime,
  })  : sessionKeyField = sessionKey,
        userIdField = userId {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  PrivateData copyWith({
    int? id,
    String? sessionKey, // sessionKey parameter for backward compatibility
    String? userId, // userId parameter for backward compatibility
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    final newPrivateData = PrivateData()
      ..id = id ?? this.id
      ..sessionKeyField = sessionKey ?? this.sessionKeyField
      ..userIdField = userId ?? this.userIdField
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;

    // Copy relationships
    newPrivateData.session.target = session.target;
    newPrivateData.user.target = user.target;
    return newPrivateData;
  }

  /// Update the record
  void update() {
    updateTime = DateTime.now();
  }

  // RELATIONSHIP HELPER METHODS

  /// Check if this private data has a valid session relationship
  bool get hasSessionRelationship => session.target != null;

  /// Check if this private data has a valid user relationship
  bool get hasUserRelationship => user.target != null;

  /// Get session ID from relationship
  String get relatedSessionId => session.target?.sessionId ?? '';

  /// Get session token from relationship
  String get relatedSessionToken => session.target?.sessionToken ?? '';

  /// Get user name from relationship
  String get relatedUserName => user.target?.username ?? '';

  /// Get user display name from relationship (from profile)
  String get relatedUserDisplayName {
    if (user.target?.profiles.isNotEmpty == true) {
      return user.target!.profiles.first.displayName;
    }
    return relatedUserName;
  }

  /// Check if related user is online (from relationship)
  bool get isRelatedUserOnline {
    if (user.target?.presences.isNotEmpty == true) {
      final currentPresence = user.target!.currentPresence;
      return currentPresence?.presenceStatus == 1; // 1 = Online
    }
    return false;
  }

  /// Set session relationship by finding session with matching sessionKey
  void setSessionByKey(String key, List<Session> availableSessions) {
    final matchingSessions =
        availableSessions.where((s) => s.sessionKey == key);

    if (matchingSessions.isNotEmpty) {
      final targetSession = matchingSessions.first;
      session.target = targetSession;
      sessionKeyField = key; // Keep string field in sync
    }
  }

  /// Set user relationship by finding user with matching userId
  void setUserByKey(String key, List<User> availableUsers) {
    final matchingUsers = availableUsers.where((u) => u.userId == key);

    if (matchingUsers.isNotEmpty) {
      final targetUser = matchingUsers.first;
      user.target = targetUser;
      userIdField = key; // Keep string field in sync
    }
  }

  @override
  String toString() {
    return 'PrivateData{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'createTime: $createTime, '
        'updateTime: $updateTime'
        '}';
  }
}
