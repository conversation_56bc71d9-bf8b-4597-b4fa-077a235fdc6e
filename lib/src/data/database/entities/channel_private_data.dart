import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'channel.dart';

/// Channel private data entity for storing channel-specific private data
/// UID range: 7200-7207
@Entity()
class ChannelPrivateData {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 7200)
  String sessionKey = '';

  /// Channel ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 7201)
  String channelIdField = '';

  /// Version number
  @Property(uid: 7202)
  int version = 0;

  /// Unread message count
  @Property(uid: 7203)
  int unreadCount = 0;

  /// Last seen message ID
  @Property(uid: 7204)
  String lastSeenMessageId = '';

  /// Whether channel is pinned
  @Property(uid: 7205)
  bool pinned = false;

  /// Sort order
  @Property(uid: 7206)
  int sort = 0;

  /// Source information
  @Property(uid: 7207)
  String source = '';

  // RELATIONSHIPS

  /// ToOne relationship to Channel
  /// Links this private data to its parent channel
  final channel = ToOne<Channel>();

  /// Get channelId from relationship or field (backward compatibility)
  String get channelId => channel.target?.channelId ?? channelIdField;

  /// Default constructor
  ChannelPrivateData();

  /// Constructor with required fields
  ChannelPrivateData.create({
    required this.sessionKey,
    required String channelId, // channelId parameter for backward compatibility
    this.version = 0,
    this.unreadCount = 0,
    this.lastSeenMessageId = '',
    this.pinned = false,
    this.sort = 0,
    this.source = '',
  }) : channelIdField = channelId;

  /// Copy constructor for updates
  ChannelPrivateData copyWith({
    int? id,
    String? sessionKey,
    String? channelId, // channelId parameter for backward compatibility
    int? version,
    int? unreadCount,
    String? lastSeenMessageId,
    bool? pinned,
    int? sort,
    String? source,
  }) {
    final newChannelPrivateData = ChannelPrivateData()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..channelIdField = channelId ?? this.channelIdField
      ..version = version ?? this.version
      ..unreadCount = unreadCount ?? this.unreadCount
      ..lastSeenMessageId = lastSeenMessageId ?? this.lastSeenMessageId
      ..pinned = pinned ?? this.pinned
      ..sort = sort ?? this.sort
      ..source = source ?? this.source;

    // Copy relationship
    newChannelPrivateData.channel.target = channel.target;
    return newChannelPrivateData;
  }

  /// Pin the channel
  void pin() {
    pinned = true;
    version++;
  }

  /// Unpin the channel
  void unpin() {
    pinned = false;
    version++;
  }

  /// Mark messages as read
  void markAsRead(String messageId) {
    lastSeenMessageId = messageId;
    unreadCount = 0;
    version++;
  }

  /// Increment unread count
  void incrementUnreadCount() {
    unreadCount++;
    version++;
  }

  /// Set unread count
  void setUnreadCount(int count) {
    unreadCount = count;
    version++;
  }

  /// Update sort order
  void updateSort(int newSort) {
    sort = newSort;
    version++;
  }

  /// Reset unread count
  void resetUnreadCount() {
    unreadCount = 0;
    version++;
  }

  /// Check if channel has unread messages
  bool get hasUnreadMessages => unreadCount > 0;

  // RELATIONSHIP HELPER METHODS

  /// Check if this private data has a valid channel relationship
  bool get hasChannelRelationship => channel.target != null;

  /// Get channel session key from relationship
  String get relatedChannelSessionKey => channel.target?.sessionKey ?? '';

  /// Get channel workspace ID from relationship
  String get relatedChannelWorkspaceId => channel.target?.workspaceId ?? '';

  /// Get channel owner ID from relationship
  String get relatedChannelOwnerId => channel.target?.channelOwnerUserId ?? '';

  /// Get channel recipient ID from relationship
  String get relatedChannelRecipientId => channel.target?.recipientId ?? '';

  /// Get channel name from relationship
  String get relatedChannelName => channel.target?.name ?? '';

  /// Get effective channel name from relationship
  String get relatedEffectiveChannelName =>
      channel.target?.effectiveChannelName ?? '';

  /// Get channel type description from relationship
  String get relatedChannelTypeDescription =>
      channel.target?.channelTypeDescription ?? '';

  /// Check if related channel is DM
  bool get isRelatedChannelDM => channel.target?.isDM ?? false;

  /// Check if related channel is archived
  bool get isRelatedChannelArchived => channel.target?.isArchived ?? false;

  /// Check if related channel has avatar
  bool get relatedChannelHasAvatar => channel.target?.hasAvatar ?? false;

  /// Get related channel avatar URL
  String get relatedChannelAvatar => channel.target?.avatar ?? '';

  /// Get related channel topic
  String get relatedChannelTopic => channel.target?.topic ?? '';

  /// Get related channel description
  String get relatedChannelDescription => channel.target?.description ?? '';

  /// Get related channel status description
  String get relatedChannelStatusDescription =>
      channel.target?.channelStatusDescription ?? '';

  /// Get owner username from relationship
  String get relatedOwnerUsername => channel.target?.ownerUsername ?? '';

  /// Get recipient username from relationship
  String get relatedRecipientUsername =>
      channel.target?.recipientUsername ?? '';

  /// Get effective owner name from relationship
  String get relatedEffectiveOwnerName =>
      channel.target?.effectiveOwnerName ?? '';

  /// Get effective recipient name from relationship
  String get relatedEffectiveRecipientName =>
      channel.target?.effectiveRecipientName ?? '';

  /// Check if owner is online from relationship
  bool get isRelatedOwnerOnline => channel.target?.isOwnerOnline ?? false;

  /// Check if recipient is online from relationship
  bool get isRelatedRecipientOnline =>
      channel.target?.isRecipientOnline ?? false;

  /// Get unread status description
  String get unreadStatusDescription {
    if (unreadCount == 0) return 'No unread messages';
    if (unreadCount == 1) return '1 unread message';
    return '$unreadCount unread messages';
  }

  /// Get pin status description
  String get pinStatusDescription => pinned ? 'Pinned' : 'Not pinned';

  /// Check if channel is prioritized (pinned or has unread messages)
  bool get isPrioritized => pinned || hasUnreadMessages;

  /// Mark all messages as read
  void markAllAsRead() {
    unreadCount = 0;
    version++;
  }

  @override
  String toString() {
    return 'ChannelPrivateData{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'channelId: $channelId, '
        'version: $version, '
        'unreadCount: $unreadCount, '
        'lastSeenMessageId: $lastSeenMessageId, '
        'pinned: $pinned, '
        'sort: $sort, '
        'source: $source'
        '}';
  }
}
