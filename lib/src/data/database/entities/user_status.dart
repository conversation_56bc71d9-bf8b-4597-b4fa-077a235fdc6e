import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'user.dart';

/// User status entity for storing user status information
/// UID range: 9300-9307
@Entity()
class UserStatus {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 9300)
  String sessionKey = '';

  /// User ID (indexed)
  @Index()
  @Property(uid: 9301)
  String userId = '';

  /// Status text
  @Property(uid: 9302)
  String statusText = '';

  /// Status emoji
  @Property(uid: 9303)
  String statusEmoji = '';

  /// Status expiration time
  @Property(uid: 9304)
  DateTime? expiresAt;

  /// Whether this is a custom status
  @Property(uid: 9305)
  bool isCustom = false;

  /// Creation time
  @Property(uid: 9306)
  DateTime? createTime;

  /// Last update time
  @Property(uid: 9307)
  DateTime? updateTime;

  // RELATIONSHIPS

  /// ToOne relationship to User
  /// Links this status to its user
  final user = ToOne<User>();

  /// Default constructor
  UserStatus();

  /// Constructor with required fields
  UserStatus.create({
    required this.sessionKey,
    required this.userId,
    this.statusText = '',
    this.statusEmoji = '',
    this.expiresAt,
    this.isCustom = false,
    this.createTime,
    this.updateTime,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  UserStatus copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? statusText,
    String? statusEmoji,
    DateTime? expiresAt,
    bool? isCustom,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return UserStatus()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..statusText = statusText ?? this.statusText
      ..statusEmoji = statusEmoji ?? this.statusEmoji
      ..expiresAt = expiresAt ?? this.expiresAt
      ..isCustom = isCustom ?? this.isCustom
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;
  }

  /// Update status
  void updateStatus({
    String? statusText,
    String? statusEmoji,
    DateTime? expiresAt,
    bool? isCustom,
  }) {
    if (statusText != null) this.statusText = statusText;
    if (statusEmoji != null) this.statusEmoji = statusEmoji;
    if (expiresAt != null) this.expiresAt = expiresAt;
    if (isCustom != null) this.isCustom = isCustom;
    updateTime = DateTime.now();
  }

  /// Clear status
  void clearStatus() {
    statusText = '';
    statusEmoji = '';
    expiresAt = null;
    isCustom = false;
    updateTime = DateTime.now();
  }

  /// Set custom status
  void setCustomStatus({
    required String text,
    required String emoji,
    DateTime? expiresAt,
  }) {
    statusText = text;
    statusEmoji = emoji;
    this.expiresAt = expiresAt;
    isCustom = true;
    updateTime = DateTime.now();
  }

  /// Check if status is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if status is active
  bool get isActive =>
      !isExpired && (statusText.isNotEmpty || statusEmoji.isNotEmpty);

  /// Check if status has text
  bool get hasText => statusText.isNotEmpty;

  /// Check if status has emoji
  bool get hasEmoji => statusEmoji.isNotEmpty;

  /// Get formatted status
  String get formattedStatus {
    if (!isActive) return '';

    final parts = <String>[];
    if (hasEmoji) parts.add(statusEmoji);
    if (hasText) parts.add(statusText);

    return parts.join(' ');
  }

  @override
  String toString() {
    return 'UserStatus{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'statusText: $statusText, '
        'statusEmoji: $statusEmoji, '
        'expiresAt: $expiresAt, '
        'isCustom: $isCustom, '
        'createTime: $createTime, '
        'updateTime: $updateTime'
        '}';
  }
}
