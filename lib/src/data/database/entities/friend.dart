import 'package:objectbox/objectbox.dart';

// Import related entities for relationships
import 'user.dart';

/// Friend entity for storing friend relationships
/// UID range: 5001-5007
@Entity()
class Friend {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 5001)
  String sessionKey = '';

  /// Owner user ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 5002)
  String ownerUserIdField = '';

  /// Friend user ID (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 5003)
  String friendUserIdField = '';

  /// Friendship status
  @Property(uid: 5004)
  int status = 0;

  /// Status update time
  @Property(uid: 5005)
  DateTime? statusUpdateTime;

  /// Friendship creation time
  @Property(uid: 5006)
  DateTime? createTime;

  /// Whether this is a partial friend record
  @Property(uid: 5007)
  bool isPartial = false;

  /// Get ownerUserId from relationship or field (backward compatibility)
  String get ownerUserId => ownerUser.target?.userId ?? ownerUserIdField;

  /// Get friendUserId from relationship or field (backward compatibility)
  String get friendUserId => friendUser.target?.userId ?? friendUserIdField;

  /// Composite index field: ownerFriendStatus
  String get ownerFriendStatus => '${ownerUserId}_${friendUserId}_$status';

  /// Composite index field: sessionOwnerStatus
  String get sessionOwnerStatus => '${sessionKey}_${ownerUserId}_$status';

  /// Composite index field: sessionFriendStatus
  String get sessionFriendStatus => '${sessionKey}_${friendUserId}_$status';

  // RELATIONSHIPS

  /// ToOne relationship to User (owner of the friendship)
  /// Links this friendship to the user who owns it
  final ownerUser = ToOne<User>();

  /// ToOne relationship to User (friend in the friendship)
  /// Links this friendship to the user who is the friend
  final friendUser = ToOne<User>();

  /// Default constructor
  Friend();

  /// Constructor with required fields
  Friend.create({
    required this.sessionKey,
    required String
        ownerUserId, // ownerUserId parameter for backward compatibility
    required String
        friendUserId, // friendUserId parameter for backward compatibility
    this.status = 0,
    this.statusUpdateTime,
    this.createTime,
    this.isPartial = false,
  })  : ownerUserIdField = ownerUserId,
        friendUserIdField = friendUserId {
    this.statusUpdateTime ??= DateTime.now();
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  Friend copyWith({
    int? id,
    String? sessionKey,
    String? ownerUserId, // ownerUserId parameter for backward compatibility
    String? friendUserId, // friendUserId parameter for backward compatibility
    int? status,
    DateTime? statusUpdateTime,
    DateTime? createTime,
    bool? isPartial,
  }) {
    final newFriend = Friend()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..ownerUserIdField = ownerUserId ?? this.ownerUserIdField
      ..friendUserIdField = friendUserId ?? this.friendUserIdField
      ..status = status ?? this.status
      ..statusUpdateTime = statusUpdateTime ?? this.statusUpdateTime
      ..createTime = createTime ?? this.createTime
      ..isPartial = isPartial ?? this.isPartial;

    // Copy relationships
    newFriend.ownerUser.target = ownerUser.target;
    newFriend.friendUser.target = friendUser.target;
    return newFriend;
  }

  /// Update friendship status
  void updateStatus(int newStatus) {
    status = newStatus;
    statusUpdateTime = DateTime.now();
  }

  /// Mark as complete friend record
  void markAsComplete() {
    isPartial = false;
  }

  /// Check if friendship is active
  bool get isActive => status == 1; // Assuming 1 means active

  /// Check if friendship is pending
  bool get isPending => status == 0; // Assuming 0 means pending

  /// Check if friendship is blocked
  bool get isBlocked => status == -1; // Assuming -1 means blocked

  @override
  String toString() {
    return 'Friend{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'ownerUserId: $ownerUserId, '
        'friendUserId: $friendUserId, '
        'status: $status, '
        'statusUpdateTime: $statusUpdateTime, '
        'createTime: $createTime, '
        'isPartial: $isPartial'
        '}';
  }
}
