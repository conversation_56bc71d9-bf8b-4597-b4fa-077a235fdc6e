import 'dart:async';

import '../../core/interfaces/data_source.dart';
import '../../core/interfaces/model_adapter.dart';

/// Stub implementation of LocalDataSource
/// TODO: Replace with actual ObjectBox integration
class ObjectBoxLocalDataSource implements LocalDataSource {
  final Map<String, StreamController<Map<String, dynamic>?>> _itemControllers = {};
  final Map<String, StreamController<List<Map<String, dynamic>>>> _listControllers = {};
  
  bool _initialized = false;

  @override
  Future<void> init() async {
    if (_initialized) return;
    
    try {
      // Stub implementation
      print('ObjectBoxLocalDataSource: Using stub implementation');
      
      _initialized = true;
      print('ObjectBoxLocalDataSource: Initialized successfully');
    } catch (e) {
      throw Exception('Failed to initialize ObjectBox database: $e');
    }
  }

  @override
  Future<T?> get<T>(String id, ModelAdapter<T> adapter) async {
    _ensureInitialized();
    
    try {
      // Stub implementation - always return null for now
      print('ObjectBoxLocalDataSource: Stub get ${adapter.tableName}[$id]');
      return null;
    } catch (e) {
      throw Exception('Failed to get ${adapter.tableName}[$id]: $e');
    }
  }

  @override
  Future<List<T>> getAll<T>(ModelAdapter<T> adapter) async {
    _ensureInitialized();
    
    try {
      // Stub implementation - always return empty list for now
      print('ObjectBoxLocalDataSource: Stub getAll ${adapter.tableName}');
      return [];
    } catch (e) {
      throw Exception('Failed to get all ${adapter.tableName}: $e');
    }
  }

  @override
  Future<void> save<T>(T item, ModelAdapter<T> adapter) async {
    _ensureInitialized();
    
    try {
      if (!adapter.validate(item)) {
        throw Exception('Validation failed for ${adapter.tableName}');
      }
      
      final id = adapter.getId(item);
      print('ObjectBoxLocalDataSource: Stub save ${adapter.tableName}[$id]');
      
      // Notify watchers
      final itemMap = adapter.toMap(item);
      _notifyItemWatchers(adapter.tableName, id, itemMap);
      _notifyListWatchers(adapter.tableName);
    } catch (e) {
      throw Exception('Failed to save ${adapter.tableName}: $e');
    }
  }

  @override
  Future<void> saveAll<T>(List<T> items, ModelAdapter<T> adapter) async {
    for (final item in items) {
      await save(item, adapter);
    }
  }

  @override
  Future<void> delete<T>(String id, ModelAdapter<T> adapter) async {
    _ensureInitialized();
    
    try {
      print('ObjectBoxLocalDataSource: Stub delete ${adapter.tableName}[$id]');
      
      // Notify watchers
      _notifyItemWatchers(adapter.tableName, id, null);
      _notifyListWatchers(adapter.tableName);
    } catch (e) {
      throw Exception('Failed to delete ${adapter.tableName}[$id]: $e');
    }
  }

  @override
  Future<void> clear<T>(ModelAdapter<T> adapter) async {
    _ensureInitialized();
    
    try {
      print('ObjectBoxLocalDataSource: Stub clear ${adapter.tableName}');
      
      // Notify watchers
      _notifyListWatchers(adapter.tableName);
    } catch (e) {
      throw Exception('Failed to clear ${adapter.tableName}: $e');
    }
  }

  @override
  Stream<T?> watch<T>(String id, ModelAdapter<T> adapter) {
    final key = '${adapter.tableName}:$id';
    
    if (!_itemControllers.containsKey(key)) {
      _itemControllers[key] = StreamController<Map<String, dynamic>?>.broadcast();
    }
    
    return _itemControllers[key]!.stream.map((data) {
      if (data == null) return null;
      try {
        return adapter.fromMap(data);
      } catch (e) {
        print('ObjectBoxLocalDataSource: Error deserializing watched ${adapter.tableName}[$id]: $e');
        return null;
      }
    });
  }

  @override
  Stream<List<T>> watchAll<T>(ModelAdapter<T> adapter) {
    final key = adapter.tableName;
    
    if (!_listControllers.containsKey(key)) {
      _listControllers[key] = StreamController<List<Map<String, dynamic>>>.broadcast();
    }
    
    return _listControllers[key]!.stream.map((dataList) {
      final items = <T>[];
      for (final data in dataList) {
        try {
          items.add(adapter.fromMap(data));
        } catch (e) {
          print('ObjectBoxLocalDataSource: Error deserializing watched ${adapter.tableName} item: $e');
        }
      }
      return items;
    });
  }

  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('ObjectBoxLocalDataSource not initialized. Call init() first.');
    }
  }

  void _notifyItemWatchers(String tableName, String id, Map<String, dynamic>? data) {
    final key = '$tableName:$id';
    final controller = _itemControllers[key];
    if (controller != null && !controller.isClosed) {
      controller.add(data);
    }
  }

  void _notifyListWatchers(String tableName) {
    final controller = _listControllers[tableName];
    if (controller != null && !controller.isClosed) {
      // For stub, just send empty list
      controller.add([]);
    }
  }

  /// Dispose all resources
  Future<void> dispose() async {
    for (final controller in _itemControllers.values) {
      await controller.close();
    }
    for (final controller in _listControllers.values) {
      await controller.close();
    }
    
    _itemControllers.clear();
    _listControllers.clear();
    
    _initialized = false;
    
    print('ObjectBoxLocalDataSource: Disposed');
  }
}
