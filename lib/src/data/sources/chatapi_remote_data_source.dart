import '../../core/interfaces/data_source.dart';
import '../../core/interfaces/model_adapter.dart';
import '../../legacy/api/client/unified_api_client.dart';
import '../../core/di/di.dart';

/// ChatAPI implementation of RemoteDataSource
/// Provides remote data access using existing ChatAPI infrastructure
class ChatApiRemoteDataSource implements RemoteDataSource {
  UnifiedApiClient? _apiClient;
  bool _initialized = false;

  @override
  Future<void> init() async {
    if (_initialized) return;

    try {
      // Get the existing API client from DI
      _apiClient = getIt<UnifiedApiClient>();

      // Ensure it's initialized
      if (!_apiClient!.isInitialized) {
        await _apiClient!.initialize();
      }

      _initialized = true;
      print('ChatApiRemoteDataSource: Initialized successfully');
    } catch (e) {
      throw Exception('Failed to initialize ChatAPI remote data source: $e');
    }
  }

  @override
  Future<T?> fetch<T>(String id, ModelAdapter<T> adapter) async {
    _ensureInitialized();

    try {
      // For now, return null as we need to implement specific API endpoints
      // TODO: Implement actual API calls based on adapter.endpoint
      print(
          'ChatApiRemoteDataSource: Fetching ${adapter.tableName}[$id] from ${adapter.endpoint}');

      // Example implementation for User:
      // if (adapter.tableName == 'users') {
      //   final response = await _apiClient!.chatApi.userApi.getUser(id);
      //   if (response.data != null) {
      //     return adapter.fromMap(response.data!);
      //   }
      // }

      // For now, simulate API response
      await Future.delayed(const Duration(milliseconds: 500));

      // Return null to indicate not found
      return null;
    } catch (e) {
      throw Exception('Failed to fetch ${adapter.tableName}[$id]: $e');
    }
  }

  @override
  Future<List<T>> fetchAll<T>(ModelAdapter<T> adapter) async {
    _ensureInitialized();

    try {
      print(
          'ChatApiRemoteDataSource: Fetching all ${adapter.tableName} from ${adapter.endpoint}');

      // TODO: Implement actual API calls based on adapter.endpoint
      // Example implementation for User:
      // if (adapter.tableName == 'users') {
      //   final response = await _apiClient!.chatApi.userApi.getUsers();
      //   if (response.data != null) {
      //     return response.data!.map((userData) => adapter.fromMap(userData)).toList();
      //   }
      // }

      // For now, simulate API response
      await Future.delayed(const Duration(milliseconds: 800));

      // Return empty list
      return [];
    } catch (e) {
      throw Exception('Failed to fetch all ${adapter.tableName}: $e');
    }
  }

  @override
  Future<T> create<T>(T item, ModelAdapter<T> adapter) async {
    _ensureInitialized();

    try {
      if (!adapter.validate(item)) {
        throw Exception('Validation failed for ${adapter.tableName}');
      }

      final id = adapter.getId(item);
      print(
          'ChatApiRemoteDataSource: Creating ${adapter.tableName}[$id] at ${adapter.endpoint}');

      // TODO: Implement actual API calls based on adapter.endpoint
      // Example implementation for User:
      // if (adapter.tableName == 'users') {
      //   final userData = adapter.toMap(item);
      //   final response = await _apiClient!.chatApi.userApi.createUser(userData);
      //   if (response.data != null) {
      //     return adapter.fromMap(response.data!);
      //   }
      // }

      // For now, simulate API response
      await Future.delayed(const Duration(milliseconds: 600));

      // Return the item with updated timestamp
      final itemMap = adapter.toMap(item);
      itemMap['updateTime'] = DateTime.now().toIso8601String();
      return adapter.fromMap(itemMap);
    } catch (e) {
      throw Exception('Failed to create ${adapter.tableName}: $e');
    }
  }

  @override
  Future<T> update<T>(T item, ModelAdapter<T> adapter) async {
    _ensureInitialized();

    try {
      if (!adapter.validate(item)) {
        throw Exception('Validation failed for ${adapter.tableName}');
      }

      final id = adapter.getId(item);
      print(
          'ChatApiRemoteDataSource: Updating ${adapter.tableName}[$id] at ${adapter.endpoint}');

      // TODO: Implement actual API calls based on adapter.endpoint
      // Example implementation for User:
      // if (adapter.tableName == 'users') {
      //   final userData = adapter.toMap(item);
      //   final response = await _apiClient!.chatApi.userApi.updateUser(id, userData);
      //   if (response.data != null) {
      //     return adapter.fromMap(response.data!);
      //   }
      // }

      // For now, simulate API response
      await Future.delayed(const Duration(milliseconds: 600));

      // Return the item with updated timestamp
      final itemMap = adapter.toMap(item);
      itemMap['updateTime'] = DateTime.now().toIso8601String();
      return adapter.fromMap(itemMap);
    } catch (e) {
      throw Exception('Failed to update ${adapter.tableName}: $e');
    }
  }

  @override
  Future<void> delete<T>(String id, ModelAdapter<T> adapter) async {
    _ensureInitialized();

    try {
      print(
          'ChatApiRemoteDataSource: Deleting ${adapter.tableName}[$id] at ${adapter.endpoint}');

      // TODO: Implement actual API calls based on adapter.endpoint
      // Example implementation for User:
      // if (adapter.tableName == 'users') {
      //   await _apiClient!.chatApi.userApi.deleteUser(id);
      // }

      // For now, simulate API response
      await Future.delayed(const Duration(milliseconds: 400));

      print('ChatApiRemoteDataSource: Deleted ${adapter.tableName}[$id]');
    } catch (e) {
      throw Exception('Failed to delete ${adapter.tableName}[$id]: $e');
    }
  }

  @override
  Future<List<T>> search<T>(String query, ModelAdapter<T> adapter) async {
    _ensureInitialized();

    try {
      print(
          'ChatApiRemoteDataSource: Searching ${adapter.tableName} for "$query"');

      // TODO: Implement actual API calls based on adapter.endpoint
      // Example implementation for User:
      // if (adapter.tableName == 'users') {
      //   final response = await _apiClient!.chatApi.userApi.searchUsers(query);
      //   if (response.data != null) {
      //     return response.data!.map((userData) => adapter.fromMap(userData)).toList();
      //   }
      // }

      // For now, simulate API response
      await Future.delayed(const Duration(milliseconds: 700));

      // Return empty list
      return [];
    } catch (e) {
      throw Exception('Failed to search ${adapter.tableName}: $e');
    }
  }

  void _ensureInitialized() {
    if (!_initialized || _apiClient == null) {
      throw StateError(
          'ChatApiRemoteDataSource not initialized. Call init() first.');
    }
  }

  /// Dispose all resources
  Future<void> dispose() async {
    // Note: We don't dispose the API client as it's managed by DI
    _initialized = false;
    print('ChatApiRemoteDataSource: Disposed');
  }

  /// Get the underlying API client for advanced usage
  UnifiedApiClient? get apiClient => _apiClient;

  /// Check if authenticated
  bool get isAuthenticated => _apiClient?.isAuthenticated ?? false;

  /// Set authentication token
  void setAuthToken(String token) {
    _apiClient?.setAuthToken(token);
  }

  /// Clear authentication token
  void clearAuthToken() {
    _apiClient?.clearAuthToken();
  }
}
