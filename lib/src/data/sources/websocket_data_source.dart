import 'dart:async';

import '../../core/interfaces/data_source.dart' as ds;
import '../../core/interfaces/model_adapter.dart';

/// WebSocket implementation for real-time data updates
/// Provides real-time data synchronization capabilities
class WebSocketDataSource implements ds.WebSocketDataSource {
  // Subscription management
  final Map<String, StreamController<Map<String, dynamic>>> _itemSubscriptions =
      {};
  final Map<String, StreamController<List<Map<String, dynamic>>>>
      _listSubscriptions = {};

  // Connection management
  bool _isConnected = false;
  final StreamController<bool> _connectionController =
      StreamController<bool>.broadcast();

  bool _initialized = false;

  @override
  Future<void> init() async {
    if (_initialized) return;

    try {
      // TODO: Initialize actual WebSocket connection
      // For now, simulate connection
      await Future.delayed(const Duration(milliseconds: 200));

      _isConnected = true;
      _connectionController.add(true);

      _initialized = true;
      print('WebSocketDataSource: Connected successfully');
    } catch (e) {
      throw Exception('Failed to initialize WebSocket connection: $e');
    }
  }

  @override
  Stream<T> subscribe<T>(String id, ModelAdapter<T> adapter) {
    _ensureInitialized();

    final key = '${adapter.tableName}:$id';

    if (!_itemSubscriptions.containsKey(key)) {
      _itemSubscriptions[key] =
          StreamController<Map<String, dynamic>>.broadcast();
      print(
          'WebSocketDataSource: Subscribed to ${adapter.tableName}[$id] on channel ${adapter.socketChannel}');

      // TODO: Subscribe to actual WebSocket channel
      // Example:
      // _webSocket.subscribe(adapter.socketChannel, {
      //   'type': 'item',
      //   'id': id,
      // });
    }

    return _itemSubscriptions[key]!.stream.map((data) {
      try {
        return adapter.fromMap(data);
      } catch (e) {
        throw Exception(
            'WebSocketDataSource: Error deserializing ${adapter.tableName}[$id]: $e');
      }
    });
  }

  @override
  Stream<List<T>> subscribeAll<T>(ModelAdapter<T> adapter) {
    _ensureInitialized();

    final key = adapter.tableName;

    if (!_listSubscriptions.containsKey(key)) {
      _listSubscriptions[key] =
          StreamController<List<Map<String, dynamic>>>.broadcast();
      print(
          'WebSocketDataSource: Subscribed to all ${adapter.tableName} on channel ${adapter.socketChannel}');

      // TODO: Subscribe to actual WebSocket channel
      // Example:
      // _webSocket.subscribe(adapter.socketChannel, {
      //   'type': 'list',
      // });
    }

    return _listSubscriptions[key]!.stream.map((dataList) {
      final items = <T>[];
      for (final data in dataList) {
        try {
          items.add(adapter.fromMap(data));
        } catch (e) {
          print(
              'WebSocketDataSource: Error deserializing ${adapter.tableName} item: $e');
        }
      }
      return items;
    });
  }

  @override
  void unsubscribe<T>(String id, ModelAdapter<T> adapter) {
    final key = '${adapter.tableName}:$id';
    final controller = _itemSubscriptions[key];

    if (controller != null) {
      controller.close();
      _itemSubscriptions.remove(key);
      print('WebSocketDataSource: Unsubscribed from ${adapter.tableName}[$id]');

      // TODO: Unsubscribe from actual WebSocket channel
      // Example:
      // _webSocket.unsubscribe(adapter.socketChannel, {
      //   'type': 'item',
      //   'id': id,
      // });
    }
  }

  @override
  void unsubscribeAll<T>(ModelAdapter<T> adapter) {
    final key = adapter.tableName;
    final controller = _listSubscriptions[key];

    if (controller != null) {
      controller.close();
      _listSubscriptions.remove(key);
      print('WebSocketDataSource: Unsubscribed from all ${adapter.tableName}');

      // TODO: Unsubscribe from actual WebSocket channel
      // Example:
      // _webSocket.unsubscribe(adapter.socketChannel, {
      //   'type': 'list',
      // });
    }
  }

  @override
  bool get isConnected => _isConnected;

  @override
  Stream<bool> get connectionStatus => _connectionController.stream;

  /// Manually trigger an update (for testing or external events)
  void triggerUpdate<T>(T item, ModelAdapter<T> adapter) {
    final id = adapter.getId(item);
    final data = adapter.toMap(item);

    // Update item subscription
    final itemKey = '${adapter.tableName}:$id';
    final itemController = _itemSubscriptions[itemKey];
    if (itemController != null && !itemController.isClosed) {
      itemController.add(data);
    }

    // Update list subscription
    final listController = _listSubscriptions[adapter.tableName];
    if (listController != null && !listController.isClosed) {
      listController.add([data]);
    }

    print(
        'WebSocketDataSource: Manually triggered update for ${adapter.tableName}[$id]');
  }

  /// Simulate connection drop and reconnection (for testing)
  Future<void> simulateConnectionDrop() async {
    if (!_isConnected) return;

    print('WebSocketDataSource: Simulating connection drop');
    _isConnected = false;
    _connectionController.add(false);

    // Reconnect after 2 seconds
    await Future.delayed(const Duration(seconds: 2));

    _isConnected = true;
    _connectionController.add(true);
    print('WebSocketDataSource: Reconnected');
  }

  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError(
          'WebSocketDataSource not initialized. Call init() first.');
    }
  }

  /// Get subscription statistics for debugging
  Map<String, dynamic> getSubscriptionStats() {
    return {
      'itemSubscriptions': _itemSubscriptions.length,
      'listSubscriptions': _listSubscriptions.length,
      'isConnected': _isConnected,
    };
  }

  /// Dispose all resources
  Future<void> dispose() async {
    for (final controller in _itemSubscriptions.values) {
      await controller.close();
    }
    for (final controller in _listSubscriptions.values) {
      await controller.close();
    }

    await _connectionController.close();

    _itemSubscriptions.clear();
    _listSubscriptions.clear();

    _isConnected = false;
    _initialized = false;

    print('WebSocketDataSource: Disposed');
  }
}
