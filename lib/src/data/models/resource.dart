/// Represents the state of data operations
enum DataState {
  /// Data is being loaded
  loading,

  /// Data loaded successfully
  success,

  /// Error occurred during loading
  error,

  /// No data available
  empty,
}

/// Wrapper class for data with state information
/// Provides loading states, error handling, and cache information
class Resource<T> {
  /// Current state of the data
  final DataState state;

  /// The actual data (can be null)
  final T? data;

  /// Error message if state is error
  final String? error;

  /// Whether data comes from cache/local storage
  final bool isFromCache;

  /// Timestamp when resource was created
  final DateTime timestamp;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  Resource({
    required this.state,
    this.data,
    this.error,
    this.isFromCache = false,
    DateTime? timestamp,
    this.metadata,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Create loading resource with optional cached data
  factory Resource.loading([T? cachedData]) => Resource(
        state: DataState.loading,
        data: cachedData,
        isFromCache: cachedData != null,
      );

  /// Create success resource
  factory Resource.success(
    T data, {
    bool isFromCache = false,
    Map<String, dynamic>? metadata,
  }) =>
      Resource(
        state: DataState.success,
        data: data,
        isFromCache: isFromCache,
        metadata: metadata,
      );

  /// Create error resource with optional cached data
  factory Resource.error(
    String error, [
    T? cachedData,
    Map<String, dynamic>? metadata,
  ]) =>
      Resource(
        state: DataState.error,
        error: error,
        data: cachedData,
        isFromCache: cachedData != null,
        metadata: metadata,
      );

  /// Create empty resource
  factory Resource.empty() => Resource(state: DataState.empty);

  // Convenience getters
  bool get isLoading => state == DataState.loading;
  bool get isSuccess => state == DataState.success;
  bool get isError => state == DataState.error;
  bool get isEmpty => state == DataState.empty;
  bool get hasData => data != null;
  bool get hasError => error != null;

  /// Transform the data while preserving state
  Resource<R> map<R>(R Function(T) transform) {
    if (data == null) {
      return Resource<R>(
        state: state,
        error: error,
        isFromCache: isFromCache,
        timestamp: timestamp,
        metadata: metadata,
      );
    }

    try {
      final transformedData = transform(data as T);
      return Resource<R>(
        state: state,
        data: transformedData,
        error: error,
        isFromCache: isFromCache,
        timestamp: timestamp,
        metadata: metadata,
      );
    } catch (e) {
      return Resource<R>.error(e.toString(), null, metadata);
    }
  }

  /// Create a copy with updated fields
  Resource<T> copyWith({
    DataState? state,
    T? data,
    String? error,
    bool? isFromCache,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return Resource<T>(
      state: state ?? this.state,
      data: data ?? this.data,
      error: error ?? this.error,
      isFromCache: isFromCache ?? this.isFromCache,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'Resource{state: $state, hasData: $hasData, isFromCache: $isFromCache, error: $error}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Resource &&
          runtimeType == other.runtimeType &&
          state == other.state &&
          data == other.data &&
          error == other.error &&
          isFromCache == other.isFromCache;

  @override
  int get hashCode =>
      state.hashCode ^ data.hashCode ^ error.hashCode ^ isFromCache.hashCode;
}
