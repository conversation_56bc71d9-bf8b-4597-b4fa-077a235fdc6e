import '../../core/interfaces/model_adapter.dart';
import '../database/entities/user.dart';

/// Adapter for User entity from existing ObjectBox database
/// Defines how User data is stored, fetched, and synchronized
class UserAdapter implements ModelAdapter<User> {
  @override
  String get tableName => 'users';

  @override
  String get endpoint => '/api/users';

  @override
  String get socketChannel => 'user_updates';

  @override
  User fromMap(Map<String, dynamic> map) {
    return User.create(
      sessionKey: map['sessionKey'] ?? '',
      userId: map['id'] ?? '',
      username: map['username'] ?? '',
      createTime: map['createTime'] != null 
          ? DateTime.parse(map['createTime']) 
          : null,
      updateTime: map['updateTime'] != null 
          ? DateTime.parse(map['updateTime']) 
          : null,
      userType: map['userType'] ?? 0,
      userConnectLink: map['userConnectLink'] ?? '',
      mediaPermissionSetting: map['mediaPermissionSetting'] ?? 0,
      globalNotificationStatus: map['globalNotificationStatus'] ?? true,
      sipCredentials: map['sipCredentials'] ?? '',
      sipAddress: map['sipAddress'] ?? '',
      isPartial: map['isPartial'] ?? false,
    );
  }

  @override
  Map<String, dynamic> toMap(User model) {
    return {
      'id': model.userId,
      'sessionKey': model.sessionKey,
      'username': model.username,
      'createTime': model.createTime?.toIso8601String(),
      'updateTime': model.updateTime?.toIso8601String(),
      'userType': model.userType,
      'userConnectLink': model.userConnectLink,
      'mediaPermissionSetting': model.mediaPermissionSetting,
      'globalNotificationStatus': model.globalNotificationStatus,
      'sipCredentials': model.sipCredentials,
      'sipAddress': model.sipAddress,
      'isPartial': model.isPartial,
    };
  }

  @override
  String getId(User model) {
    return model.userId;
  }

  @override
  bool validate(User model) {
    // Basic validation rules for User entity
    if (model.userId.isEmpty) return false;
    if (model.sessionKey.isEmpty) return false;
    
    // Username can be empty for partial users
    if (!model.isPartial && model.username.isEmpty) return false;
    
    return true;
  }

  @override
  User? createEmpty() {
    // Return null since User requires mandatory fields
    return null;
  }

  /// Enhanced validation with detailed error messages
  Map<String, String> validateDetailed(User model) {
    final errors = <String, String>{};

    if (model.userId.isEmpty) {
      errors['userId'] = 'User ID is required';
    }

    if (model.sessionKey.isEmpty) {
      errors['sessionKey'] = 'Session key is required';
    }

    if (!model.isPartial && model.username.isEmpty) {
      errors['username'] = 'Username is required for complete user records';
    }

    if (model.userType < 0) {
      errors['userType'] = 'User type must be non-negative';
    }

    return errors;
  }

  /// Check if validation passes
  bool get isValidationEnabled => true;

  /// Get validation error message
  String? getValidationError(User model) {
    final errors = validateDetailed(model);
    if (errors.isEmpty) return null;
    
    return errors.values.first;
  }

  /// Create a User from minimal data (for API responses)
  User createFromMinimalData({
    required String userId,
    required String sessionKey,
    String? username,
    bool isPartial = false,
  }) {
    return User.create(
      sessionKey: sessionKey,
      userId: userId,
      username: username ?? '',
      isPartial: isPartial,
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
    );
  }

  /// Update User with new data while preserving existing relationships
  User updateUser(User existing, Map<String, dynamic> updates) {
    return existing.copyWith(
      username: updates['username'],
      userType: updates['userType'],
      userConnectLink: updates['userConnectLink'],
      mediaPermissionSetting: updates['mediaPermissionSetting'],
      globalNotificationStatus: updates['globalNotificationStatus'],
      sipCredentials: updates['sipCredentials'],
      sipAddress: updates['sipAddress'],
      isPartial: updates['isPartial'],
      updateTime: DateTime.now(),
    );
  }

  /// Check if User is a complete record (not partial)
  bool isCompleteUser(User user) {
    return !user.isPartial && user.username.isNotEmpty;
  }

  /// Check if User has SIP configuration
  bool hasSipConfiguration(User user) {
    return user.sipCredentials.isNotEmpty && user.sipAddress.isNotEmpty;
  }

  /// Get display name for User
  String getDisplayName(User user) {
    if (user.username.isNotEmpty) {
      return user.username;
    }
    return user.userId;
  }

  /// Check if User allows media permissions
  bool allowsMedia(User user) {
    return user.mediaPermissionSetting > 0;
  }

  /// Check if User has global notifications enabled
  bool hasNotificationsEnabled(User user) {
    return user.globalNotificationStatus;
  }

  /// Create a partial User record (for caching incomplete data)
  User createPartialUser({
    required String userId,
    required String sessionKey,
    String? username,
  }) {
    return User.create(
      sessionKey: sessionKey,
      userId: userId,
      username: username ?? '',
      isPartial: true,
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
    );
  }

  /// Convert User to a summary Map (for logging/debugging)
  Map<String, dynamic> toSummaryMap(User user) {
    return {
      'userId': user.userId,
      'username': user.username,
      'sessionKey': user.sessionKey.substring(0, 8) + '...', // Truncated for security
      'isPartial': user.isPartial,
      'userType': user.userType,
      'hasNotifications': user.globalNotificationStatus,
      'updateTime': user.updateTime?.toIso8601String(),
    };
  }
}
