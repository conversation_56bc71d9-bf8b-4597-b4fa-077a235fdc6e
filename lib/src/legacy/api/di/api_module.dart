import 'package:chat_api/chat_api.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../implementations/auth_service_impl.dart';
import '../interfaces/auth_service_interface.dart';

/// Dependency injection module for API layer
@module
abstract class ApiModule {
  /// Provide Dio instance with default configuration
  @singleton
  Dio provideDio() {
    final dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    ));

    // Add logging in debug mode
    dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        error: true,
        logPrint: (object) {
          print('[API] $object');
        },
      ),
    );

    return dio;
  }

  /// Provide AuthService implementation
  @singleton
  IAuthService provideAuthService(ChatApi chatApi) {
    return AuthServiceImpl(chatApi);
  }

  // TODO: Add other service providers as they are implemented
  // @singleton
  // IMessageService provideMessageService(ChatApi chatApi) {
  //   return MessageServiceImpl(chatApi);
  // }

  // @singleton
  // IChannelService provideChannelService(ChatApi chatApi) {
  //   return ChannelServiceImpl(chatApi);
  // }

  // @singleton
  // IUserProfileService provideUserProfileService(ChatApi chatApi) {
  //   return UserProfileServiceImpl(chatApi);
  // }

  // @singleton
  // IFriendService provideFriendService(ChatApi chatApi) {
  //   return FriendServiceImpl(chatApi);
  // }

  // @singleton
  // IMemberService provideMemberService(ChatApi chatApi) {
  //   return MemberServiceImpl(chatApi);
  // }

  // @singleton
  // ISearchService provideSearchService(ChatApi chatApi) {
  //   return SearchServiceImpl(chatApi);
  // }

  // @singleton
  // ISuggestionService provideSuggestionService(ChatApi chatApi) {
  //   return SuggestionServiceImpl(chatApi);
  // }

  // @singleton
  // ICallService provideCallService(ChatApi chatApi) {
  //   return CallServiceImpl(chatApi);
  // }

  // @singleton
  // IUserSettingService provideUserSettingService(ChatApi chatApi) {
  //   return UserSettingServiceImpl(chatApi);
  // }

  // @singleton
  // IWebSocketService provideWebSocketService(ChatApi chatApi) {
  //   return WebSocketServiceImpl(chatApi);
  // }

  // @singleton
  // IInvitationService provideInvitationService(ChatApi chatApi) {
  //   return InvitationServiceImpl(chatApi);
  // }

  /// Get base URL from environment or configuration
  String _getBaseUrl() {
    // TODO: Get from environment variables or configuration
    // For now, return a default value
    return 'http://localhost:8080';
  }
}

/// API configuration class
class ApiConfig {
  final String baseUrl;
  final Duration connectTimeout;
  final Duration receiveTimeout;
  final Duration sendTimeout;
  final Map<String, String> defaultHeaders;
  final bool enableLogging;

  const ApiConfig({
    required this.baseUrl,
    this.connectTimeout = const Duration(seconds: 30),
    this.receiveTimeout = const Duration(seconds: 30),
    this.sendTimeout = const Duration(seconds: 30),
    this.defaultHeaders = const {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
    this.enableLogging = true,
  });

  /// Development configuration
  static const ApiConfig development = ApiConfig(
    baseUrl: 'http://localhost:8080',
    enableLogging: true,
  );

  /// Staging configuration
  static const ApiConfig staging = ApiConfig(
    baseUrl: 'https://staging-api.example.com',
    enableLogging: true,
  );

  /// Production configuration
  static const ApiConfig production = ApiConfig(
    baseUrl: 'https://api.example.com',
    enableLogging: false,
  );
}

/// Environment-specific API module
@module
abstract class EnvironmentApiModule {
  /// Provide API configuration based on environment
  @singleton
  ApiConfig provideApiConfig() {
    // TODO: Determine environment from build configuration
    const environment =
        String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');

    switch (environment) {
      case 'production':
        return ApiConfig.production;
      case 'staging':
        return ApiConfig.staging;
      case 'development':
      default:
        return ApiConfig.development;
    }
  }

  // Note: Dio is provided by ApiModule to avoid conflicts
}

/// Mock API module for testing
@module
abstract class MockApiModule {
  /// Provide mock ChatApi instance
  @singleton
  @test
  ChatApi provideMockChatApi() {
    final dio = Dio(BaseOptions(
      baseUrl: 'http://mock.localhost',
      connectTimeout: const Duration(seconds: 5),
      receiveTimeout: const Duration(seconds: 5),
    ));

    // Add mock interceptor
    dio.interceptors.add(MockInterceptor());

    return ChatApi(
      dio: dio,
      basePathOverride: 'http://mock.localhost',
    );
  }
}

/// Mock interceptor for testing
class MockInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Mock request handling
    print('[Mock API] Request: ${options.method} ${options.path}');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Mock response handling
    print('[Mock API] Response: ${response.statusCode}');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Mock error handling
    print('[Mock API] Error: ${err.message}');
    handler.next(err);
  }
}
