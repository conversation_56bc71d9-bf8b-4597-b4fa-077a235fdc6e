# API Layer - Unified Client Architecture

Lớp API cung cấp một client tổng hợp để truy cập tất cả các API endpoints được định nghĩa trong ChatAPI package.

## 🏗️ **Kiến Trúc**

### **Interface-Implementation Pattern**
```
┌─────────────────────────────────────────────────────────────┐
│                    UnifiedApiClient                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  IAuthService   │  │ IMessageService │  │ IChannelSvc  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│           │                     │                   │       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ AuthServiceImpl │  │MessageServiceImpl│  │ChannelSvcImpl│ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      ChatApi                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ AuthServiceApi  │  │MessageServiceApi│  │ChannelSvcApi │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **Thành Phần Chính**

1. **UnifiedApiClient** - Entry point chính cho tất cả API operations
2. **Service Interfaces** - Đ<PERSON><PERSON> nghĩa contracts cho từng service
3. **Service Implementations** - Wrapper implementations sử dụng ChatApi
4. **Base Classes** - Common functionality và error handling
5. **Dependency Injection** - Module configuration cho DI

## 🚀 **Cách Sử Dụng**

### **Basic Setup**

```dart
import 'package:data_router/data_router.dart';

// Tạo API client
final apiClient = UnifiedApiClient.create(
  baseUrl: 'https://api.example.com',
  authToken: 'your-auth-token',
);

// Initialize
await apiClient.initialize();
```

### **Authentication**

```dart
// Login
final loginRequest = LoginWithUserKeyRequest(
  userKey: '<EMAIL>',
  password: 'password123',
);

final result = await apiClient.auth.loginWithUserKey(loginRequest);

if (result.isSuccess) {
  final loginResponse = result.data!;
  print('Logged in as: ${loginResponse.user.displayName}');
  
  // Set auth token for subsequent requests
  apiClient.setAuthToken(loginResponse.accessToken);
} else {
  print('Login failed: ${result.error}');
}
```

### **Error Handling**

```dart
try {
  final result = await apiClient.auth.loginWithUserKey(request);
  
  if (result.isSuccess) {
    // Handle success
    final data = result.data!;
  } else {
    // Handle API error
    print('Error: ${result.error}');
    print('Status: ${result.statusCode}');
  }
} on AuthenticationException catch (e) {
  // Handle auth errors
  print('Auth error: ${e.message}');
} on NetworkException catch (e) {
  // Handle network errors
  print('Network error: ${e.message}');
} on ApiException catch (e) {
  // Handle general API errors
  print('API error: ${e.message}');
}
```

### **Dependency Injection**

```dart
// Configure DI
await configureInjection();

// Get API client from DI
final apiClient = getIt<UnifiedApiClient>();

// Or use convenience getter
final apiClient = apiClient;
```

## 📁 **Cấu Trúc Thư Mục**

```
lib/src/data/api/
├── client/
│   └── unified_api_client.dart          # Main API client
├── interfaces/
│   ├── base_service_interface.dart      # Base interface & result types
│   ├── auth_service_interface.dart      # Auth service interface
│   └── ...                              # Other service interfaces
├── implementations/
│   ├── base_service_impl.dart           # Base implementation
│   ├── auth_service_impl.dart           # Auth service implementation
│   └── ...                              # Other service implementations
├── di/
│   └── api_module.dart                  # DI configuration
├── examples/
│   └── api_usage_example.dart           # Usage examples
├── models/                              # Custom models (if needed)
├── utils/                               # Utility classes
├── api.dart                             # Main export file
└── README.md                            # This file
```

## 🔧 **Configuration**

### **Environment Configuration**

```dart
// Development
final client = UnifiedApiClient.create(
  baseUrl: 'http://localhost:8080',
);

// Production
final client = UnifiedApiClient.create(
  baseUrl: 'https://api.production.com',
);
```

### **Custom Dio Configuration**

```dart
final dio = Dio(BaseOptions(
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 30),
  headers: {
    'X-App-Version': '1.0.0',
  },
));

final client = UnifiedApiClient(dio: dio);
```

## 🧪 **Testing**

### **Mock Client**

```dart
// Create mock client for testing
final mockClient = UnifiedApiClient.createMock();
await mockClient.initialize();

// Use in tests
final result = await mockClient.auth.loginWithUserKey(request);
```

### **Unit Testing Services**

```dart
testWidgets('should login successfully', (tester) async {
  // Arrange
  final mockChatApi = MockChatApi();
  final authService = AuthServiceImpl(mockChatApi);
  
  // Act
  final result = await authService.loginWithUserKey(request);
  
  // Assert
  expect(result.isSuccess, isTrue);
});
```

## 📊 **Service Coverage**

| Service | Interface | Implementation | Status |
|---------|-----------|----------------|--------|
| Auth | ✅ | ✅ | **Ready** |
| Message | 🔄 | 🔄 | **In Progress** |
| Channel | 🔄 | 🔄 | **In Progress** |
| UserProfile | 🔄 | 🔄 | **In Progress** |
| Friend | 🔄 | 🔄 | **In Progress** |
| Member | 🔄 | 🔄 | **In Progress** |
| Search | 🔄 | 🔄 | **In Progress** |
| Suggestion | 🔄 | 🔄 | **In Progress** |
| Call | 🔄 | 🔄 | **In Progress** |
| UserSetting | 🔄 | 🔄 | **In Progress** |
| WebSocket | 🔄 | 🔄 | **In Progress** |
| Invitation | 🔄 | 🔄 | **In Progress** |

## 🔄 **Migration từ Old Clients**

### **Before (Old Architecture)**
```dart
// Multiple separate clients
final authClient = AuthClient();
final messageClient = MessageClient();
final channelClient = ChannelClient();

// Manual initialization
await authClient.initialize();
await messageClient.initialize();
await channelClient.initialize();
```

### **After (Unified Architecture)**
```dart
// Single unified client
final apiClient = UnifiedApiClient.create();
await apiClient.initialize();

// Access all services through unified interface
await apiClient.auth.login(request);
await apiClient.message.sendMessage(request);
await apiClient.channel.createChannel(request);
```

## 🎯 **Best Practices**

1. **Always check result.isSuccess** before accessing data
2. **Use proper exception handling** for different error types
3. **Initialize client once** at app startup
4. **Dispose client** when app terminates
5. **Use DI** for better testability
6. **Set auth tokens** after successful login
7. **Clear auth tokens** after logout

## 🔗 **Related Documentation**

- [ChatAPI Documentation](../../../chat_api/)
- [Database Layer](../database/)
- [Dependency Injection](../../core/di/)
- [Error Handling](../../shared/types/)

---

**Note:** Đây là implementation ban đầu với AuthService. Các services khác sẽ được thêm vào theo kế hoạch implementation.
