/// API Layer Exports
/// 
/// This file provides a unified export for all API-related functionality.
/// Import this file to access the complete API layer.

// Core API Client
export 'client/unified_api_client.dart';

// Base Interfaces and Implementations
export 'interfaces/base_service_interface.dart';
export 'implementations/base_service_impl.dart';

// Service Interfaces
export 'interfaces/auth_service_interface.dart';
// TODO: Add other service interfaces as they are implemented
// export 'interfaces/message_service_interface.dart';
// export 'interfaces/channel_service_interface.dart';
// export 'interfaces/user_profile_service_interface.dart';
// export 'interfaces/friend_service_interface.dart';
// export 'interfaces/member_service_interface.dart';
// export 'interfaces/search_service_interface.dart';
// export 'interfaces/suggestion_service_interface.dart';
// export 'interfaces/call_service_interface.dart';
// export 'interfaces/user_setting_service_interface.dart';
// export 'interfaces/websocket_service_interface.dart';
// export 'interfaces/invitation_service_interface.dart';

// Service Implementations
export 'implementations/auth_service_impl.dart';
// TODO: Add other service implementations as they are implemented
// export 'implementations/message_service_impl.dart';
// export 'implementations/channel_service_impl.dart';
// export 'implementations/user_profile_service_impl.dart';
// export 'implementations/friend_service_impl.dart';
// export 'implementations/member_service_impl.dart';
// export 'implementations/search_service_impl.dart';
// export 'implementations/suggestion_service_impl.dart';
// export 'implementations/call_service_impl.dart';
// export 'implementations/user_setting_service_impl.dart';
// export 'implementations/websocket_service_impl.dart';
// export 'implementations/invitation_service_impl.dart';

// Dependency Injection
export 'di/api_module.dart';

// Models and DTOs
// TODO: Add model exports as they are created
// export 'models/auth_models.dart';
// export 'models/message_models.dart';
// export 'models/channel_models.dart';
// export 'models/user_models.dart';

// Utilities
// TODO: Add utility exports as they are created
// export 'utils/api_utils.dart';
// export 'utils/error_handler.dart';
// export 'utils/response_mapper.dart';

// Re-export ChatApi for direct access if needed
export 'package:chat_api/chat_api.dart';
