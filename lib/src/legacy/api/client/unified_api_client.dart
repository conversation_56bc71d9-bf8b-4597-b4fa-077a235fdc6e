import 'package:chat_api/chat_api.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../implementations/auth_service_impl.dart';
import '../interfaces/auth_service_interface.dart';

/// Unified API client that provides access to all services
/// This is the main entry point for all API operations
@lazySingleton
class UnifiedApiClient {
  static final BaseOptions baseOptions = BaseOptions(
    // baseUrl: EnvConfig.getApiHost,
    baseUrl: '',
    receiveDataWhenStatusError: true,
    // Only consider 2xx and 3xx as successful responses
    // This ensures 4xx and 5xx errors are handled in onError
    // But we still need ServiceUnavailableInterceptor for backward compatibility
    validateStatus: (status) {
      return status != null && status >= 200 && status < 400;
    },
    headers: {
      // EnvConfig.getClientIdEnv: EnvConfig.getClientId,
      // EnvConfig.getApiHeaderEnv: EnvConfig.getApiVersion,
    },
    // sendTimeout: GlobalConfig.sendTimeoutDuration,
    // receiveTimeout: GlobalConfig.receiveTimeoutDuration,
    // connectTimeout: GlobalConfig.connectTimeoutDuration,
  );

  static final Dio dio = Dio(baseOptions)
    ..interceptors.addAll([
      // // 1. ServiceUnavailableInterceptor: Handle 503 errors with "no healthy upstream"
      // // This must be first to handle 503 errors before other interceptors
      // ServiceUnavailableInterceptor(),
      //
      // // 2. LoggingInterceptor: Log all requests, responses, and errors
      // // This should be early to log everything but after special error handlers
      // LoggingInterceptor(),
      //
      // // 3. Event-firing interceptors: These don't modify responses, just fire events
      // // The order between these doesn't matter as they don't affect each other
      // ApiErrorInterceptor(),
      // // Handles blocked user errors
      // ValidTokenInterceptor(),
      // // Handles invalid token errors
      // IncludesDataInterceptor(),
      // // Processes includes data
      //
      // // 4. RetryInterceptor: Must be last to handle all errors after other interceptors
      // // This can retry requests based on certain error conditions
      // RetryInterceptor(
      //   dio: Dio(baseOptions),
      //   maxRetries: GlobalConfig.maxAttempts,
      //   retryIntervalMs: GlobalConfig.delayFactor.inMilliseconds,
      // ),
    ]);

  late ChatApi _chatApi;

  IAuthService? _authService;

  // Configuration
  bool _isInitialized = false;
  String? _baseUrl;
  String? _accessToken;

  UnifiedApiClient() {
    _chatApi = ChatApi(dio: dio);
  }

  /// Initialize the unified client
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize any global configurations
    await _setupGlobalInterceptors();

    _isInitialized = true;
  }

  /// Dispose the unified client and all services
  Future<void> dispose() async {
    if (!_isInitialized) return;

    // Dispose all services
    await _authService?.dispose();

    // Reset state
    _authService = null;
    _isInitialized = false;
  }

  /// Check if client is initialized
  bool get isInitialized => _isInitialized;

  /// Get the base URL
  String? get baseUrl => _baseUrl;

  /// Set the base URL
  void setBaseUrl(String baseUrl) {
    _baseUrl = baseUrl;
    // Update ChatApi base URL if needed
  }

  /// Set authentication token
  void setAuthToken(String token) {
    _accessToken = token;
    _chatApi.setBearerAuth('Bearer', token);
  }

  /// Clear authentication token
  void clearAuthToken() {
    _accessToken = null;
    _chatApi.setBearerAuth('Bearer', '');
  }

  /// Get current authentication token
  String? get authToken => _accessToken;

  /// Check if user is authenticated
  bool get isAuthenticated => _accessToken != null && _accessToken!.isNotEmpty;

  // Service Getters (Lazy-loaded)

  /// Get AuthService instance
  IAuthService get auth {
    _authService ??= AuthServiceImpl(_chatApi);
    return _authService!;
  }

  // TODO: Add other service getters as they are implemented
  // IMessageService get message => _messageService ??= MessageServiceImpl(_chatApi);
  // IChannelService get channel => _channelService ??= ChannelServiceImpl(_chatApi);
  // IUserProfileService get userProfile => _userProfileService ??= UserProfileServiceImpl(_chatApi);
  // IFriendService get friend => _friendService ??= FriendServiceImpl(_chatApi);
  // IMemberService get member => _memberService ??= MemberServiceImpl(_chatApi);
  // ISearchService get search => _searchService ??= SearchServiceImpl(_chatApi);
  // ISuggestionService get suggestion => _suggestionService ??= SuggestionServiceImpl(_chatApi);
  // ICallService get call => _callService ??= CallServiceImpl(_chatApi);
  // IUserSettingService get userSetting => _userSettingService ??= UserSettingServiceImpl(_chatApi);
  // IWebSocketService get webSocket => _webSocketService ??= WebSocketServiceImpl(_chatApi);
  // IInvitationService get invitation => _invitationService ??= InvitationServiceImpl(_chatApi);

  /// Get the underlying ChatApi instance (for advanced usage)
  ChatApi get chatApi => _chatApi;

  /// Setup global interceptors for common functionality
  Future<void> _setupGlobalInterceptors() async {
    // Add logging interceptor
    _chatApi.dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        error: true,
        logPrint: (object) {
          // Use your preferred logging mechanism
          print('[API] $object');
        },
      ),
    );

    // Add error handling interceptor
    _chatApi.dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) {
          // Global error handling
          _handleGlobalError(error);
          handler.next(error);
        },
        onRequest: (options, handler) {
          // Add common headers
          options.headers['Accept'] = 'application/json';
          options.headers['Content-Type'] = 'application/json';

          // Add auth token if available
          if (_accessToken != null && _accessToken!.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $_accessToken';
          }

          handler.next(options);
        },
        onResponse: (response, handler) {
          // Global response handling
          _handleGlobalResponse(response);
          handler.next(response);
        },
      ),
    );
  }

  /// Handle global errors
  void _handleGlobalError(DioException error) {
    // Log error
    print('[API Error] ${error.type}: ${error.message}');

    // Handle specific error types
    switch (error.response?.statusCode) {
      case 401:
        // Unauthorized - clear token and notify auth service
        clearAuthToken();
        // TODO: Emit authentication required event
        break;
      case 403:
        // Forbidden - user doesn't have permission
        // TODO: Emit permission denied event
        break;
      case 429:
        // Rate limited
        // TODO: Implement retry logic or emit rate limit event
        break;
      case 500:
      case 502:
      case 503:
        // Server errors
        // TODO: Emit server error event
        break;
    }
  }

  /// Handle global responses
  void _handleGlobalResponse(Response response) {
    // Log successful responses
    print(
        '[API Success] ${response.statusCode}: ${response.requestOptions.path}');

    // Handle specific response patterns
    if (response.statusCode == 200 || response.statusCode == 201) {
      // Success - could emit success events if needed
    }
  }
}
