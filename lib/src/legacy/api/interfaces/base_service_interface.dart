/// Base interface for all API services
/// Provides common functionality and error handling patterns
abstract class IBaseService {
  /// Initialize the service
  Future<void> initialize();

  /// Dispose resources
  Future<void> dispose();

  /// Check if service is initialized
  bool get isInitialized;

  /// Service name for logging and debugging
  String get serviceName;
}

/// Base result class for API operations
abstract class BaseResult<T> {
  final bool isSuccess;
  final T? data;
  final String? error;
  final int? statusCode;

  const BaseResult({
    required this.isSuccess,
    this.data,
    this.error,
    this.statusCode,
  });

  /// Create success result
  factory BaseResult.success(T data) = SuccessResult<T>;

  /// Create error result
  factory BaseResult.error(String error, [int? statusCode]) = ErrorResult<T>;
}

/// Success result implementation
class SuccessResult<T> extends BaseResult<T> {
  const SuccessResult(T data)
      : super(
          isSuccess: true,
          data: data,
        );
}

/// Error result implementation
class ErrorResult<T> extends BaseResult<T> {
  const ErrorResult(String error, [int? statusCode])
      : super(
          isSuccess: false,
          error: error,
          statusCode: statusCode,
        );
}

/// Common API exceptions
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;

  const ApiException(
    this.message, {
    this.statusCode,
    this.originalError,
  });

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

/// Authentication exception
class AuthenticationException extends ApiException {
  const AuthenticationException(String message, {int? statusCode})
      : super(message, statusCode: statusCode);
}

/// Network exception
class NetworkException extends ApiException {
  const NetworkException(String message, {dynamic originalError})
      : super(message, originalError: originalError);
}

/// Validation exception
class ValidationException extends ApiException {
  final Map<String, List<String>>? fieldErrors;

  const ValidationException(
    String message, {
    this.fieldErrors,
    int? statusCode,
  }) : super(message, statusCode: statusCode);
}
