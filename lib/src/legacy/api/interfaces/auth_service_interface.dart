import 'base_service_interface.dart';

/// Authentication service interface
/// Handles user authentication, session management, and security operations
abstract class IAuthService extends IBaseService {
  @override
  String get serviceName => 'AuthService';

  // Authentication Methods
  Future<BaseResult<LoginResponse>> loginWithUserKey(LoginWithUserKeyRequest request);
  Future<BaseResult<LoginResponse>> loginWithSmartOtp(LoginWithSmartOtpRequest request);
  Future<BaseResult<LoginResponse>> loginWithQrCode(LoginWithQrCodeRequest request);
  Future<BaseResult<LogoutResponse>> logout();

  // Registration Methods
  Future<BaseResult<RegisterResponse>> registerWithUserKey(RegisterWithUserKeyRequest request);
  Future<BaseResult<void>> cancelRegistration(CancelRegistrationRequest request);

  // Session Management
  Future<BaseResult<List<SessionData>>> listActiveSessions();
  Future<BaseResult<void>> terminateSession(String sessionId);
  Future<BaseResult<void>> terminateAllSessions();
  Future<BaseResult<TokenExchangeResponse>> exchangeToken(TokenExchangeRequest request);

  // QR Authentication
  Future<BaseResult<QrAuthFlowResponse>> initiateQrAuthFlow(QrAuthFlowRequest request);
  Future<BaseResult<QrAuthStateResponse>> getQrAuthState(String flowId);
  Future<BaseResult<void>> acceptQrAuth(AcceptQrAuthRequest request);
  Future<BaseResult<void>> renewQrAuthCode(RenewQrAuthCodeRequest request);

  // Security Key Management
  Future<BaseResult<SecurityKeyFlowResponse>> initiateSecurityKeyFlow(SecurityKeyFlowRequest request);
  Future<BaseResult<SecurityKeyResponse>> generateSecurityKey(GenerateSecurityKeyRequest request);
  Future<BaseResult<void>> confirmViewSecurityKey(ConfirmViewSecurityKeyRequest request);

  // Account Recovery
  Future<BaseResult<RecoveryFlowResponse>> initiateRecoveryFlow(RecoveryFlowRequest request);
  Future<BaseResult<RecoveryResponse>> recoverAccount(RecoveryAccountRequest request);

  // Account Deletion
  Future<BaseResult<AccountDeletionFlowResponse>> initiateAccountDeletionFlow(AccountDeletionFlowRequest request);
  Future<BaseResult<void>> confirmAccountDeletion(ConfirmAccountDeletionRequest request);
  Future<BaseResult<void>> confirmAccountDeletionBySecurityKey(ConfirmAccountDeletionBySecurityKeyRequest request);

  // Passkey Migration
  Future<BaseResult<MigratePasskeyStatusResponse>> checkMigratePasskeyStatus();
  Future<BaseResult<MigratePasskeyResponse>> migratePasskey(MigratePasskeyRequest request);
  Future<BaseResult<VerifyMigratePasskeyResponse>> verifyMigratePasskey(VerifyMigratePasskeyRequest request);

  // Recovery Code Management
  Future<BaseResult<RecoveryCodeFlowResponse>> initiateRecoveryCodeFlow(RecoveryCodeFlowRequest request);
  Future<BaseResult<void>> confirmRecoveryCodeGeneration(ConfirmRecoveryCodeRequest request);

  // Smart OTP
  Future<BaseResult<SmartOtpFlowResponse>> initiateSmartOtpFlow(SmartOtpFlowRequest request);
  Future<BaseResult<VerifySmartOtpResponse>> verifySmartOtp(VerifySmartOtpRequest request);

  // User Key Authentication
  Future<BaseResult<UserKeyAuthFlowResponse>> initiateUserKeyAuthFlow(UserKeyAuthFlowRequest request);
  Future<BaseResult<SuggestUserKeyAuthFlowResponse>> initiateSuggestUserKeyAuthFlow(SuggestUserKeyAuthFlowRequest request);
  Future<BaseResult<LoginResponse>> loginWithSuggestUserKey(LoginWithSuggestUserKeyRequest request);
}

// Request/Response Models
class LoginWithUserKeyRequest {
  final String userKey;
  final String password;
  final String? deviceInfo;

  const LoginWithUserKeyRequest({
    required this.userKey,
    required this.password,
    this.deviceInfo,
  });
}

class LoginWithSmartOtpRequest {
  final String phoneNumber;
  final String otpCode;
  final String? deviceInfo;

  const LoginWithSmartOtpRequest({
    required this.phoneNumber,
    required this.otpCode,
    this.deviceInfo,
  });
}

class LoginWithQrCodeRequest {
  final String qrCode;
  final String? deviceInfo;

  const LoginWithQrCodeRequest({
    required this.qrCode,
    this.deviceInfo,
  });
}

class LoginResponse {
  final String accessToken;
  final String refreshToken;
  final String userId;
  final DateTime expiresAt;
  final UserProfile user;

  const LoginResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.userId,
    required this.expiresAt,
    required this.user,
  });
}

class LogoutResponse {
  final bool success;
  final String? message;

  const LogoutResponse({
    required this.success,
    this.message,
  });
}

class RegisterWithUserKeyRequest {
  final String userKey;
  final String password;
  final String displayName;
  final String? email;
  final String? phoneNumber;

  const RegisterWithUserKeyRequest({
    required this.userKey,
    required this.password,
    required this.displayName,
    this.email,
    this.phoneNumber,
  });
}

class RegisterResponse {
  final String userId;
  final String message;

  const RegisterResponse({
    required this.userId,
    required this.message,
  });
}

class CancelRegistrationRequest {
  final String registrationId;

  const CancelRegistrationRequest({
    required this.registrationId,
  });
}

class SessionData {
  final String sessionId;
  final String deviceInfo;
  final DateTime createdAt;
  final DateTime lastActiveAt;
  final bool isCurrent;

  const SessionData({
    required this.sessionId,
    required this.deviceInfo,
    required this.createdAt,
    required this.lastActiveAt,
    required this.isCurrent,
  });
}

class TokenExchangeRequest {
  final String token;
  final String tokenType;

  const TokenExchangeRequest({
    required this.token,
    required this.tokenType,
  });
}

class TokenExchangeResponse {
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;

  const TokenExchangeResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
  });
}

class QrAuthFlowRequest {
  final String? deviceInfo;

  const QrAuthFlowRequest({this.deviceInfo});
}

class QrAuthFlowResponse {
  final String flowId;
  final String qrCode;
  final DateTime expiresAt;

  const QrAuthFlowResponse({
    required this.flowId,
    required this.qrCode,
    required this.expiresAt,
  });
}

class QrAuthStateResponse {
  final String state;
  final String? userId;
  final DateTime? completedAt;

  const QrAuthStateResponse({
    required this.state,
    this.userId,
    this.completedAt,
  });
}

class AcceptQrAuthRequest {
  final String flowId;
  final String authCode;

  const AcceptQrAuthRequest({
    required this.flowId,
    required this.authCode,
  });
}

class RenewQrAuthCodeRequest {
  final String flowId;

  const RenewQrAuthCodeRequest({
    required this.flowId,
  });
}

// Additional request/response models would be defined here...
// For brevity, I'm including the most essential ones

class UserProfile {
  final String id;
  final String displayName;
  final String? email;
  final String? phoneNumber;
  final String? avatarUrl;

  const UserProfile({
    required this.id,
    required this.displayName,
    this.email,
    this.phoneNumber,
    this.avatarUrl,
  });
}

// Placeholder classes for other request/response types
class SecurityKeyFlowRequest {}
class SecurityKeyFlowResponse {}
class GenerateSecurityKeyRequest {}
class SecurityKeyResponse {}
class ConfirmViewSecurityKeyRequest {}
class RecoveryFlowRequest {}
class RecoveryFlowResponse {}
class RecoveryAccountRequest {}
class RecoveryResponse {}
class AccountDeletionFlowRequest {}
class AccountDeletionFlowResponse {}
class ConfirmAccountDeletionRequest {}
class ConfirmAccountDeletionBySecurityKeyRequest {}
class MigratePasskeyStatusResponse {}
class MigratePasskeyRequest {}
class MigratePasskeyResponse {}
class VerifyMigratePasskeyRequest {}
class VerifyMigratePasskeyResponse {}
class RecoveryCodeFlowRequest {}
class RecoveryCodeFlowResponse {}
class ConfirmRecoveryCodeRequest {}
class SmartOtpFlowRequest {}
class SmartOtpFlowResponse {}
class VerifySmartOtpRequest {}
class VerifySmartOtpResponse {}
class UserKeyAuthFlowRequest {}
class UserKeyAuthFlowResponse {}
class SuggestUserKeyAuthFlowRequest {}
class SuggestUserKeyAuthFlowResponse {}
class LoginWithSuggestUserKeyRequest {}
