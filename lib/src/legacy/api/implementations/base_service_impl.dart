import 'package:dio/dio.dart';
import 'package:chat_api/chat_api.dart';
import '../interfaces/base_service_interface.dart';

/// Base implementation for all API services
/// Provides common functionality and error handling
abstract class BaseServiceImpl implements IBaseService {
  final ChatApi _chatApi;
  bool _isInitialized = false;

  BaseServiceImpl(this._chatApi);

  @override
  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    await onInitialize();
    _isInitialized = true;
  }

  @override
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    await onDispose();
    _isInitialized = false;
  }

  /// Override this method to perform service-specific initialization
  Future<void> onInitialize() async {}

  /// Override this method to perform service-specific cleanup
  Future<void> onDispose() async {}

  /// Get the ChatApi instance
  ChatApi get chatApi => _chatApi;

  /// Handle API calls with common error handling
  Future<BaseResult<T>> handleApiCall<T>(
    Future<Response<T>> Function() apiCall,
  ) async {
    try {
      final response = await apiCall();
      
      if (response.data != null) {
        return BaseResult.success(response.data!);
      } else {
        return BaseResult.error(
          'No data received from server',
          response.statusCode,
        );
      }
    } on DioException catch (e) {
      return BaseResult.error(
        _handleDioError(e),
        e.response?.statusCode,
      );
    } catch (e) {
      return BaseResult.error(
        'Unexpected error: ${e.toString()}',
      );
    }
  }

  /// Handle Dio errors and convert to user-friendly messages
  String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Server response timeout. Please try again.';
      case DioExceptionType.badResponse:
        return _handleHttpError(error.response?.statusCode);
      case DioExceptionType.cancel:
        return 'Request was cancelled.';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      case DioExceptionType.badCertificate:
        return 'Security certificate error.';
      case DioExceptionType.unknown:
      default:
        return error.message ?? 'Unknown error occurred.';
    }
  }

  /// Handle HTTP status code errors
  String _handleHttpError(int? statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Authentication required. Please login again.';
      case 403:
        return 'Access denied. You don\'t have permission.';
      case 404:
        return 'Resource not found.';
      case 409:
        return 'Conflict. Resource already exists.';
      case 422:
        return 'Validation error. Please check your input.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Bad gateway. Server is temporarily unavailable.';
      case 503:
        return 'Service unavailable. Please try again later.';
      default:
        return 'HTTP error $statusCode occurred.';
    }
  }

  /// Throw appropriate exception based on error type
  void throwApiException(String message, {int? statusCode, dynamic originalError}) {
    if (statusCode == 401) {
      throw AuthenticationException(message, statusCode: statusCode);
    } else if (statusCode != null && statusCode >= 400 && statusCode < 500) {
      throw ValidationException(message, statusCode: statusCode);
    } else {
      throw ApiException(message, statusCode: statusCode, originalError: originalError);
    }
  }
}
