/// Generic adapter interface for entity models
/// Defines how to convert between models and data storage formats
abstract class ModelAdapter<T> {
  /// Table name for local storage (SQLite)
  String get tableName;
  
  /// API endpoint for remote operations
  String get endpoint;
  
  /// WebSocket channel for real-time updates
  String get socketChannel;
  
  /// Convert from Map to Model
  T fromMap(Map<String, dynamic> map);
  
  /// Convert from Model to Map
  Map<String, dynamic> toMap(T model);
  
  /// Get primary key from model
  String getId(T model);
  
  /// Validation rules (optional)
  bool validate(T model) => true;
  
  /// Create empty/default instance (optional)
  T? createEmpty() => null;
}
