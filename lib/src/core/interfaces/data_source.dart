import 'model_adapter.dart';

/// Local data source interface for SQLite operations
abstract class LocalDataSource {
  /// Initialize the local data source
  Future<void> init();

  /// Get single item by ID
  Future<T?> get<T>(String id, ModelAdapter<T> adapter);

  /// Get all items of type T
  Future<List<T>> getAll<T>(ModelAdapter<T> adapter);

  /// Save single item
  Future<void> save<T>(T item, ModelAdapter<T> adapter);

  /// Save multiple items
  Future<void> saveAll<T>(List<T> items, ModelAdapter<T> adapter);

  /// Delete item by ID
  Future<void> delete<T>(String id, ModelAdapter<T> adapter);

  /// Clear all items of type T
  Future<void> clear<T>(ModelAdapter<T> adapter);

  /// Watch single item for changes (reactive)
  Stream<T?> watch<T>(String id, ModelAdapter<T> adapter);

  /// Watch all items for changes (reactive)
  Stream<List<T>> watchAll<T>(ModelAdapter<T> adapter);

  /// Dispose all resources
  Future<void> dispose();
}

/// Remote data source interface for HTTP API operations
abstract class RemoteDataSource {
  /// Initialize the remote data source
  Future<void> init();

  /// Fetch single item by ID from API
  Future<T?> fetch<T>(String id, ModelAdapter<T> adapter);

  /// Fetch all items from API
  Future<List<T>> fetchAll<T>(ModelAdapter<T> adapter);

  /// Create new item on API
  Future<T> create<T>(T item, ModelAdapter<T> adapter);

  /// Update existing item on API
  Future<T> update<T>(T item, ModelAdapter<T> adapter);

  /// Delete item on API
  Future<void> delete<T>(String id, ModelAdapter<T> adapter);

  /// Search items with query
  Future<List<T>> search<T>(String query, ModelAdapter<T> adapter);

  /// Dispose all resources
  Future<void> dispose();
}

/// WebSocket data source interface for real-time updates
abstract class WebSocketDataSource {
  /// Initialize the WebSocket connection
  Future<void> init();

  /// Subscribe to updates for specific item
  Stream<T> subscribe<T>(String id, ModelAdapter<T> adapter);

  /// Subscribe to updates for all items of type T
  Stream<List<T>> subscribeAll<T>(ModelAdapter<T> adapter);

  /// Unsubscribe from updates
  void unsubscribe<T>(String id, ModelAdapter<T> adapter);

  /// Unsubscribe from all updates of type T
  void unsubscribeAll<T>(ModelAdapter<T> adapter);

  /// Check if connected
  bool get isConnected;

  /// Connection status stream
  Stream<bool> get connectionStatus;

  /// Dispose all resources
  Future<void> dispose();
}
