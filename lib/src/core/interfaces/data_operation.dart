import '../../data/models/resource.dart';

/// Interface for data operations on a specific entity type
/// Provides local-first data access with reactive streams
abstract class DataOperation<T> {
  /// Get single item by ID with local-first strategy
  /// Returns stream that emits:
  /// 1. Loading state (with cached data if available)
  /// 2. Success state with fresh data from API
  /// 3. Error state if API call fails (with cached data if available)
  Stream<Resource<T?>> get(String id);

  /// Get all items with local-first strategy
  /// Similar flow to get() but for collections
  Stream<Resource<List<T>>> getAll();

  /// Get items with pagination
  Stream<Resource<List<T>>> getPaginated({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  });

  /// Search items with query
  Stream<Resource<List<T>>> search(String query);

  /// Subscribe to real-time updates for specific item
  /// Returns stream of updated items from WebSocket
  Stream<T> subscribeUpdate(String id);

  /// Subscribe to real-time updates for all items
  Stream<List<T>> subscribeUpdates();

  /// Create new item
  /// 1. Create on remote API
  /// 2. Save to local storage
  /// 3. Return created item
  Future<T> create(T item);

  /// Update existing item
  /// 1. Update on remote API
  /// 2. Update in local storage
  /// 3. Return updated item
  Future<T> update(T item);

  /// Delete item by ID
  /// 1. Delete from remote API
  /// 2. Delete from local storage
  Future<void> delete(String id);

  /// Refresh data from remote
  /// Force fetch from API and update local storage
  Future<void> refresh([String? id]);

  /// Clear local cache
  Future<void> clearCache();

  /// Get cached data only (no API call)
  Future<T?> getCached(String id);

  /// Get all cached data only (no API call)
  Future<List<T>> getAllCached();

  /// Check if item exists in cache
  Future<bool> existsInCache(String id);

  /// Watch local data changes (reactive)
  Stream<T?> watchLocal(String id);

  /// Watch all local data changes (reactive)
  Stream<List<T>> watchAllLocal();
}
