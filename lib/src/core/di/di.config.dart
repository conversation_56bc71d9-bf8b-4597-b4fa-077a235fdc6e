// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:chat_api/chat_api.dart' as _i512;
import 'package:data_router/src/core/config/config.dart' as _i436;
import 'package:data_router/src/core/database.dart' as _i3;
import 'package:data_router/src/core/di/database_module.dart' as _i137;
import 'package:data_router/src/data/database/generated/objectbox.g.dart'
    as _i535;
import 'package:data_router/src/legacy/api/client/unified_api_client.dart'
    as _i656;
import 'package:data_router/src/legacy/api/di/api_module.dart' as _i527;
import 'package:data_router/src/legacy/api/interfaces/auth_service_interface.dart'
    as _i541;
import 'package:dio/dio.dart' as _i361;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

const String _test = 'test';

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final apiModule = _$ApiModule();
    final environmentApiModule = _$EnvironmentApiModule();
    final databaseModule = _$DatabaseModule();
    final mockApiModule = _$MockApiModule();
    gh.singleton<_i361.Dio>(() => apiModule.provideDio());
    gh.singleton<_i527.ApiConfig>(
        () => environmentApiModule.provideApiConfig());
    await gh.lazySingletonAsync<_i535.Store>(
      () => databaseModule.provideObjectBoxStore(),
      preResolve: true,
    );
    gh.lazySingleton<_i436.DataRouterConfig>(() => _i436.DataRouterConfig());
    gh.lazySingleton<_i656.UnifiedApiClient>(() => _i656.UnifiedApiClient());
    gh.lazySingleton<_i3.Database>(
        () => databaseModule.provideDatabase(gh<_i535.Store>()));
    gh.singleton<_i512.ChatApi>(
      () => mockApiModule.provideMockChatApi(),
      registerFor: {_test},
    );
    gh.singleton<_i541.IAuthService>(
        () => apiModule.provideAuthService(gh<_i512.ChatApi>()));
    return this;
  }
}

class _$ApiModule extends _i527.ApiModule {}

class _$EnvironmentApiModule extends _i527.EnvironmentApiModule {}

class _$DatabaseModule extends _i137.DatabaseModule {}

class _$MockApiModule extends _i527.MockApiModule {}
