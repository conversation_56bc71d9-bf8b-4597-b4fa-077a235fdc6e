import 'dart:async';

import '../interfaces/data_operation.dart';
import '../interfaces/model_adapter.dart';
import '../interfaces/data_source.dart';
import '../../data/models/resource.dart';

/// Implementation of DataOperation with local-first strategy
/// Provides reactive data access with automatic synchronization
class DataOperationImpl<T> implements DataOperation<T> {
  final ModelAdapter<T> adapter;
  final LocalDataSource localSource;
  final RemoteDataSource remoteSource;
  final WebSocketDataSource webSocketSource;

  DataOperationImpl({
    required this.adapter,
    required this.localSource,
    required this.remoteSource,
    required this.webSocketSource,
  });

  @override
  Stream<Resource<T?>> get(String id) async* {
    try {
      // 1. First, try to get from local storage (immediate response)
      final cached = await localSource.get(id, adapter);
      if (cached != null) {
        yield Resource.success(cached, isFromCache: true);
        print('DataOperation: Returned cached ${adapter.tableName}[$id]');
      } else {
        yield Resource.loading();
        print('DataOperation: Loading ${adapter.tableName}[$id] from remote');
      }

      // 2. Fetch from remote API (fresh data)
      try {
        final remote = await remoteSource.fetch(id, adapter);
        if (remote != null) {
          // 3. Save to local storage
          await localSource.save(remote, adapter);

          // 4. Emit fresh data
          yield Resource.success(remote, isFromCache: false);
          print('DataOperation: Fetched and cached ${adapter.tableName}[$id]');
        } else if (cached == null) {
          // No data found locally or remotely
          yield Resource.empty();
          print('DataOperation: ${adapter.tableName}[$id] not found');
        }
      } catch (e) {
        // API call failed, but we might have cached data
        if (cached != null) {
          yield Resource.error(e.toString(), cached);
          print(
              'DataOperation: API failed, using cached ${adapter.tableName}[$id]: $e');
        } else {
          yield Resource.error(e.toString());
          print(
              'DataOperation: API failed, no cached data for ${adapter.tableName}[$id]: $e');
        }
      }
    } catch (e) {
      yield Resource.error('Local storage error: $e');
      print(
          'DataOperation: Local storage error for ${adapter.tableName}[$id]: $e');
    }
  }

  @override
  Stream<Resource<List<T>>> getAll() async* {
    try {
      // 1. Get cached data first
      final cached = await localSource.getAll(adapter);
      if (cached.isNotEmpty) {
        yield Resource.success(cached, isFromCache: true);
        print(
            'DataOperation: Returned ${cached.length} cached ${adapter.tableName} items');
      } else {
        yield Resource.loading();
        print('DataOperation: Loading all ${adapter.tableName} from remote');
      }

      // 2. Fetch from remote
      try {
        final remote = await remoteSource.fetchAll(adapter);
        if (remote.isNotEmpty) {
          // 3. Save all to local storage
          await localSource.saveAll(remote, adapter);

          // 4. Emit fresh data
          yield Resource.success(remote, isFromCache: false);
          print(
              'DataOperation: Fetched and cached ${remote.length} ${adapter.tableName} items');
        } else if (cached.isEmpty) {
          yield Resource.empty();
          print('DataOperation: No ${adapter.tableName} items found');
        }
      } catch (e) {
        // API call failed
        if (cached.isNotEmpty) {
          yield Resource.error(e.toString(), cached);
          print(
              'DataOperation: API failed, using ${cached.length} cached ${adapter.tableName} items: $e');
        } else {
          yield Resource.error(e.toString());
          print(
              'DataOperation: API failed, no cached ${adapter.tableName} items: $e');
        }
      }
    } catch (e) {
      yield Resource.error('Local storage error: $e');
      print('DataOperation: Local storage error for ${adapter.tableName}: $e');
    }
  }

  @override
  Stream<T> subscribeUpdate(String id) {
    print(
        'DataOperation: Subscribing to updates for ${adapter.tableName}[$id]');
    return webSocketSource.subscribe(id, adapter);
  }

  @override
  Future<T> create(T item) async {
    try {
      print(
          'DataOperation: Creating ${adapter.tableName}[${adapter.getId(item)}]');

      // 1. Validate item
      if (!adapter.validate(item)) {
        throw Exception('Validation failed for ${adapter.tableName}');
      }

      // 2. Create on remote first
      final created = await remoteSource.create(item, adapter);

      // 3. Save to local storage
      await localSource.save(created, adapter);

      print(
          'DataOperation: Created ${adapter.tableName}[${adapter.getId(created)}]');
      return created;
    } catch (e) {
      print('DataOperation: Create failed for ${adapter.tableName}: $e');
      rethrow;
    }
  }

  @override
  Future<T> update(T item) async {
    try {
      final id = adapter.getId(item);
      print('DataOperation: Updating ${adapter.tableName}[$id]');

      // 1. Validate item
      if (!adapter.validate(item)) {
        throw Exception('Validation failed for ${adapter.tableName}');
      }

      // 2. Update on remote first
      final updated = await remoteSource.update(item, adapter);

      // 3. Update in local storage
      await localSource.save(updated, adapter);

      print('DataOperation: Updated ${adapter.tableName}[$id]');
      return updated;
    } catch (e) {
      print('DataOperation: Update failed for ${adapter.tableName}: $e');
      rethrow;
    }
  }

  @override
  Future<void> delete(String id) async {
    try {
      print('DataOperation: Deleting ${adapter.tableName}[$id]');

      // 1. Delete from remote first
      await remoteSource.delete(id, adapter);

      // 2. Delete from local storage
      await localSource.delete(id, adapter);

      print('DataOperation: Deleted ${adapter.tableName}[$id]');
    } catch (e) {
      print('DataOperation: Delete failed for ${adapter.tableName}[$id]: $e');
      rethrow;
    }
  }

  @override
  Future<void> refresh([String? id]) async {
    try {
      if (id != null) {
        print('DataOperation: Refreshing ${adapter.tableName}[$id]');
        final fresh = await remoteSource.fetch(id, adapter);
        if (fresh != null) {
          await localSource.save(fresh, adapter);
        }
      } else {
        print('DataOperation: Refreshing all ${adapter.tableName}');
        final fresh = await remoteSource.fetchAll(adapter);
        await localSource.clear(adapter);
        await localSource.saveAll(fresh, adapter);
      }
      print('DataOperation: Refresh completed for ${adapter.tableName}');
    } catch (e) {
      print('DataOperation: Refresh failed for ${adapter.tableName}: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      await localSource.clear(adapter);
      print('DataOperation: Cleared cache for ${adapter.tableName}');
    } catch (e) {
      print('DataOperation: Clear cache failed for ${adapter.tableName}: $e');
      rethrow;
    }
  }

  @override
  Future<T?> getCached(String id) async {
    try {
      final cached = await localSource.get(id, adapter);
      print(
          'DataOperation: Got cached ${adapter.tableName}[$id]: ${cached != null}');
      return cached;
    } catch (e) {
      print(
          'DataOperation: Get cached failed for ${adapter.tableName}[$id]: $e');
      return null;
    }
  }

  @override
  Future<List<T>> getAllCached() async {
    try {
      final cached = await localSource.getAll(adapter);
      print(
          'DataOperation: Got ${cached.length} cached ${adapter.tableName} items');
      return cached;
    } catch (e) {
      print(
          'DataOperation: Get all cached failed for ${adapter.tableName}: $e');
      return [];
    }
  }

  @override
  Stream<T?> watchLocal(String id) {
    print('DataOperation: Watching local ${adapter.tableName}[$id]');
    return localSource.watch(id, adapter);
  }

  @override
  Stream<List<T>> watchAllLocal() {
    print('DataOperation: Watching all local ${adapter.tableName}');
    return localSource.watchAll(adapter);
  }

  @override
  Future<bool> existsInCache(String id) async {
    try {
      final cached = await localSource.get(id, adapter);
      final exists = cached != null;
      print(
          'DataOperation: ${adapter.tableName}[$id] exists in cache: $exists');
      return exists;
    } catch (e) {
      print(
          'DataOperation: Exists check failed for ${adapter.tableName}[$id]: $e');
      return false;
    }
  }

  @override
  Stream<Resource<List<T>>> getPaginated({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async* {
    // For now, implement simple pagination by getting all and slicing
    // In real implementation, this would use API pagination
    await for (final resource in getAll()) {
      if (resource.hasData) {
        final allItems = resource.data!;
        final startIndex = (page - 1) * limit;
        final endIndex = startIndex + limit;

        if (startIndex >= allItems.length) {
          yield Resource.success([], isFromCache: resource.isFromCache);
        } else {
          final paginatedItems = allItems.sublist(
            startIndex,
            endIndex > allItems.length ? allItems.length : endIndex,
          );
          yield Resource.success(paginatedItems,
              isFromCache: resource.isFromCache);
        }
      } else {
        yield Resource<List<T>>(
          state: resource.state,
          error: resource.error,
          isFromCache: resource.isFromCache,
        );
      }
    }
  }

  @override
  Stream<Resource<List<T>>> search(String query) async* {
    try {
      yield Resource.loading();
      print('DataOperation: Searching ${adapter.tableName} for "$query"');

      // Search in remote first (more comprehensive)
      try {
        final results = await remoteSource.search(query, adapter);

        // Save results to local cache
        for (final item in results) {
          await localSource.save(item, adapter);
        }

        yield Resource.success(results);
        print(
            'DataOperation: Found ${results.length} ${adapter.tableName} results for "$query"');
      } catch (e) {
        // Fallback to local search
        final cached = await localSource.getAll(adapter);
        final filteredResults = cached.where((item) {
          final data = adapter.toMap(item);
          final searchableText = data.values
              .where((value) => value is String)
              .join(' ')
              .toLowerCase();
          return searchableText.contains(query.toLowerCase());
        }).toList();

        yield Resource.error(e.toString(), filteredResults);
        print(
            'DataOperation: API search failed, found ${filteredResults.length} local results: $e');
      }
    } catch (e) {
      yield Resource.error('Search error: $e');
      print('DataOperation: Search error for ${adapter.tableName}: $e');
    }
  }

  @override
  Stream<List<T>> subscribeUpdates() {
    print('DataOperation: Subscribing to all updates for ${adapter.tableName}');
    return webSocketSource.subscribeAll(adapter);
  }
}
