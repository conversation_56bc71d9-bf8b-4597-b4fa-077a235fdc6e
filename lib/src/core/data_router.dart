import 'dart:async';

import 'interfaces/model_adapter.dart';
import 'interfaces/data_source.dart';
import 'interfaces/data_operation.dart';
import 'operations/data_operation_impl.dart';
import '../data/sources/stub_local_data_source.dart';
import '../data/sources/chatapi_remote_data_source.dart';
import '../data/sources/websocket_data_source.dart' as ws;

/// Central data router singleton for managing all data operations
/// Provides local-first data access with automatic synchronization
///
/// Usage:
/// ```dart
/// // Setup
/// DataRouter().registerAdapter<User>(UserAdapter());
/// await DataRouter().init();
///
/// // Use
/// final userOp = await DataRouter().get<User>();
/// userOp.get('userId').listen((resource) {
///   if (resource.isSuccess) {
///     print('User: ${resource.data!.username}');
///   }
/// });
/// ```
class DataRouter {
  static final DataRouter _instance = DataRouter._internal();
  factory DataRouter() => _instance;
  DataRouter._internal();

  // Registered adapters for each entity type
  final Map<Type, ModelAdapter> _adapters = {};

  // Data sources
  LocalDataSource? _localSource;
  RemoteDataSource? _remoteSource;
  WebSocketDataSource? _webSocketSource;

  // Initialization state
  bool _initialized = false;
  Completer<void>? _initCompleter;

  /// Register an adapter for entity type T
  /// Must be called before init()
  void registerAdapter<T>(ModelAdapter<T> adapter) {
    if (_initialized) {
      throw StateError(
          'Cannot register adapters after DataRouter is initialized');
    }

    _adapters[T] = adapter;
    print('DataRouter: Registered adapter for ${T.toString()}');
  }

  /// Initialize the DataRouter with data sources
  /// Uses production data sources by default
  Future<void> init({
    LocalDataSource? localSource,
    RemoteDataSource? remoteSource,
    WebSocketDataSource? webSocketSource,
  }) async {
    // Prevent multiple simultaneous initializations
    if (_initCompleter != null) {
      return _initCompleter!.future;
    }

    if (_initialized) return;

    _initCompleter = Completer<void>();

    try {
      print('DataRouter: Initializing...');

      // Use provided sources or create default production sources
      _localSource = localSource ?? ObjectBoxLocalDataSource();
      _remoteSource = remoteSource ?? ChatApiRemoteDataSource();
      _webSocketSource = webSocketSource ?? ws.WebSocketDataSource();

      // Initialize all data sources
      await Future.wait<void>([
        _localSource!.init(),
        _remoteSource!.init(),
        _webSocketSource!.init(),
      ]);

      _initialized = true;
      print('DataRouter: Initialized successfully');

      _initCompleter!.complete();
    } catch (e) {
      print('DataRouter: Initialization failed: $e');
      _initCompleter!.completeError(e);
      rethrow;
    } finally {
      _initCompleter = null;
    }
  }

  /// Get data operation for entity type T
  Future<DataOperation<T>> get<T>() async {
    await _ensureInitialized();

    final adapter = _adapters[T] as ModelAdapter<T>?;
    if (adapter == null) {
      throw StateError('No adapter registered for type ${T.toString()}. '
          'Call DataRouter().registerAdapter<$T>(adapter) first.');
    }

    return DataOperationImpl<T>(
      adapter: adapter,
      localSource: _localSource!,
      remoteSource: _remoteSource!,
      webSocketSource: _webSocketSource!,
    );
  }

  /// Check if DataRouter is initialized
  bool get isInitialized => _initialized;

  /// Get list of registered entity types
  List<Type> get registeredTypes => _adapters.keys.toList();

  /// Check if adapter is registered for type T
  bool isRegistered<T>() => _adapters.containsKey(T);

  /// Get adapter for type T (if registered)
  ModelAdapter<T>? getAdapter<T>() => _adapters[T] as ModelAdapter<T>?;

  /// Clear all cached data for all entities
  Future<void> clearAllCache() async {
    await _ensureInitialized();

    print('DataRouter: Clearing all cache...');

    for (final adapter in _adapters.values) {
      try {
        await (_localSource as dynamic).clear(adapter);
        print('DataRouter: Cleared cache for ${adapter.tableName}');
      } catch (e) {
        print('DataRouter: Failed to clear cache for ${adapter.tableName}: $e');
      }
    }

    print('DataRouter: All cache cleared');
  }

  /// Refresh all data from remote for all entities
  Future<void> refreshAll() async {
    await _ensureInitialized();

    print('DataRouter: Refreshing all data...');

    final futures = <Future>[];

    for (final entry in _adapters.entries) {
      final adapter = entry.value;

      try {
        // Create operation and refresh
        final operation = DataOperationImpl(
          adapter: adapter,
          localSource: _localSource!,
          remoteSource: _remoteSource!,
          webSocketSource: _webSocketSource!,
        );

        futures.add(operation.refresh());
      } catch (e) {
        print('DataRouter: Failed to refresh ${adapter.tableName}: $e');
      }
    }

    await Future.wait(futures);
    print('DataRouter: All data refreshed');
  }

  /// Get connection status from WebSocket
  bool get isConnected => _webSocketSource?.isConnected ?? false;

  /// Stream of connection status changes
  Stream<bool> get connectionStatus =>
      _webSocketSource?.connectionStatus ?? Stream.value(false);

  /// Get statistics about registered adapters and data sources
  Map<String, dynamic> getStats() {
    return {
      'initialized': _initialized,
      'registeredTypes': _adapters.length,
      'adapters': _adapters.keys.map((type) => type.toString()).toList(),
      'isConnected': isConnected,
      'localSourceType': _localSource.runtimeType.toString(),
      'remoteSourceType': _remoteSource.runtimeType.toString(),
      'webSocketSourceType': _webSocketSource.runtimeType.toString(),
    };
  }

  /// Dispose all resources
  Future<void> dispose() async {
    if (!_initialized) return;

    print('DataRouter: Disposing...');

    // Dispose data sources
    await _localSource?.dispose();
    await _remoteSource?.dispose();
    await _webSocketSource?.dispose();

    _adapters.clear();
    _localSource = null;
    _remoteSource = null;
    _webSocketSource = null;
    _initialized = false;

    print('DataRouter: Disposed');
  }

  /// Reset DataRouter to uninitialized state (useful for testing)
  Future<void> reset() async {
    await dispose();
    print('DataRouter: Reset completed');
  }

  /// Ensure DataRouter is initialized
  Future<void> _ensureInitialized() async {
    if (_initCompleter != null) {
      await _initCompleter!.future;
    }

    if (!_initialized) {
      throw StateError(
          'DataRouter not initialized. Call DataRouter().init() first.');
    }
  }
}
