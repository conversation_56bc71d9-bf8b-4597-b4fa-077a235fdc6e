/// {@template data_router}
/// Local-first data router with Clean Architecture and Unified API Client
/// {@endtemplate}

// Core exports
export 'core/database.dart';
export 'core/config/config.dart';
export 'core/di/di.dart';

// Database entities
export 'data/database/entities/entities.dart';

// Legacy API layer exports (will be deprecated)
export 'legacy/api/api.dart';

// New DataRouter exports
export 'core/data_router.dart';
export 'core/interfaces/model_adapter.dart';
export 'core/interfaces/data_source.dart';
export 'core/interfaces/data_operation.dart';
export 'data/models/resource.dart';
export 'data/adapters/user_adapter.dart';

// Shared types
export 'shared/types/result.dart';

/// Main DataRouter class
class DataRouter {
  /// {@macro data_router}
  const DataRouter();
}
