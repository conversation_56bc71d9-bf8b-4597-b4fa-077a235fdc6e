# Tổng Hợ<PERSON> <PERSON><PERSON><PERSON> Hệ Entity - ObjectBox Database

## 1. DANH SÁCH TẤT CẢ CÁC MỐI QUAN HỆ GIỮA ENTITIES

### 📋 **A. SESSION & METADATA RELATIONSHIPS**

- `SessionLocalMetadata` → `Session`: [One-to-One] - 1 session có 1 metadata duy nhất
- `History` → `Session`: [Many-to-One] - <PERSON><PERSON><PERSON><PERSON> lịch sử thuộc về 1 session
- `Manager` → `Session`: [One-to-One] - 1 session có 1 manager duy nhất
- `PrivateData` → `Session`: [Many-to-One] - Nhiều private data thuộc về 1 session
- `Collection` → `Session`: [Many-to-One] - Nhiều collection thuộc về 1 session

### 📋 **B. USER & PROFILE RELATIONSHIPS**

- `User` → `Session`: [Many-to-One] - Nhiều user thuộc về 1 session
- `Profile` → `User`: [Many-to-One] - Nhiều profile thuộc về 1 user
- `VisitedProfile` → `User`: [Many-to-One] - <PERSON><PERSON><PERSON><PERSON> visited profile thuộc về 1 user
- `UserPresence` → `User`: [Many-to-One] - Nhiều presence record thuộc về 1 user
- `UserStatus` → `User`: [Many-to-One] - Nhiều status record thuộc về 1 user
- `UserPrivateData` → `User`: [Many-to-One] - Nhiều user private data thuộc về 1 user
- `PrivateData` → `User`: [Many-to-One] - Nhiều general private data thuộc về 1 user

### 📋 **C. CHANNEL & COMMUNICATION RELATIONSHIPS**

- `Channel` → `Session`: [Many-to-One] - Nhiều channel thuộc về 1 session
- `Channel` → `User` (owner): [Many-to-One] - Nhiều channel thuộc về 1 owner user
- `Channel` → `User` (recipient): [Many-to-One] - Nhiều channel có 1 recipient user (DM)
- `Member` → `Session`: [Many-to-One] - Nhiều member thuộc về 1 session
- `Member` → `Channel`: [Many-to-One] - Nhiều member thuộc về 1 channel
- `Member` → `User`: [Many-to-One] - Nhiều member record thuộc về 1 user
- `ChannelPrivateData` → `Channel`: [One-to-One] - 1 channel có 1 private data duy nhất

### 📋 **D. MESSAGE & ATTACHMENT RELATIONSHIPS**

- `Message` → `Session`: [Many-to-One] - Nhiều message thuộc về 1 session
- `Message` → `Channel`: [Many-to-One] - Nhiều message thuộc về 1 channel
- `Message` → `User` (sender): [Many-to-One] - Nhiều message thuộc về 1 sender user
- `Attachment` → `Message`: [Many-to-One] - Nhiều attachment thuộc về 1 message
- `TranslatedResult` → `Message`: [One-to-One] - 1 message có 1 kết quả dịch duy nhất

### 📋 **E. FRIEND RELATIONSHIPS**

- `Friend` → `User` (owner): [Many-to-One] - Nhiều friendship thuộc về 1 owner user
- `Friend` → `User` (friend): [Many-to-One] - Nhiều friendship thuộc về 1 friend user

### 📋 **F. CALL RELATIONSHIPS**

- `CallLog` → `User` (caller): [Many-to-One] - Nhiều call log thuộc về 1 caller user
- `CallLog` → `User` (callee): [Many-to-One] - Nhiều call log thuộc về 1 callee user
- `CallLogPrivateData` → `CallLog`: [One-to-One] - 1 call log có 1 private data duy nhất

### 📋 **G. STICKER & COLLECTION RELATIONSHIPS**

- `Sticker` → `Collection`: [Many-to-One] - Nhiều sticker thuộc về 1 collection
- `StickerFrameCount` → `Sticker`: [One-to-One] - 1 sticker có 1 frame count duy nhất

## 2. PHÂN LOẠI THEO LOẠI RELATIONSHIP OBJECTBOX

### 🔗 **A. ToOne RELATIONSHIPS (Quan hệ 1-1 và Many-1)**

#### **Session References:**
- `SessionLocalMetadata.session = ToOne<Session>()`
- `History.session = ToOne<Session>()`
- `Manager.session = ToOne<Session>()`
- `PrivateData.session = ToOne<Session>()`
- `Collection.session = ToOne<Session>()`
- `User.session = ToOne<Session>()`
- `Channel.session = ToOne<Session>()`
- `Member.session = ToOne<Session>()`
- `Message.session = ToOne<Session>()`

#### **User References:**
- `Profile.user = ToOne<User>()`
- `VisitedProfile.visitedUser = ToOne<User>()`
- `UserPresence.user = ToOne<User>()`
- `UserStatus.user = ToOne<User>()`
- `UserPrivateData.user = ToOne<User>()`
- `PrivateData.user = ToOne<User>()`
- `Friend.ownerUser = ToOne<User>()`
- `Friend.friendUser = ToOne<User>()`
- `Channel.ownerUser = ToOne<User>()`
- `Channel.recipientUser = ToOne<User>()`
- `Member.user = ToOne<User>()`
- `Message.sender = ToOne<User>()`
- `CallLog.callerUser = ToOne<User>()`
- `CallLog.calleeUser = ToOne<User>()`

#### **Channel References:**
- `Member.channel = ToOne<Channel>()`
- `Message.channel = ToOne<Channel>()`
- `ChannelPrivateData.channel = ToOne<Channel>()`

#### **Message References:**
- `Attachment.message = ToOne<Message>()`
- `TranslatedResult.message = ToOne<Message>()` (1:1 relationship)

#### **Other References:**
- `CallLogPrivateData.callLog = ToOne<CallLog>()`
- `Sticker.collection = ToOne<Collection>()`
- `StickerFrameCount.sticker = ToOne<Sticker>()`

### 🔗 **B. ToMany RELATIONSHIPS (Quan hệ 1-Many)**

#### **Session Backlinks:**
- `Session.localMetadata = ToOne<SessionLocalMetadata>()` via relationship (1:1)
- `Session.histories = ToMany<History>()` via `@Backlink('session')`
- `Session.manager = ToOne<Manager>()` via relationship (1:1)
- `Session.privateDataRecords = ToMany<PrivateData>()` via `@Backlink('session')`
- `Session.collections = ToMany<Collection>()` via `@Backlink('session')`

#### **User Backlinks:**
- `User.profiles = ToMany<Profile>()` via `@Backlink('user')`
- `User.visitedProfiles = ToMany<VisitedProfile>()` via `@Backlink('visitedUser')`
- `User.presences = ToMany<UserPresence>()` via `@Backlink('user')`
- `User.statuses = ToMany<UserStatus>()` via `@Backlink('user')`
- `User.privateDataRecords = ToMany<UserPrivateData>()` via `@Backlink('user')`
- `User.generalPrivateDataRecords = ToMany<PrivateData>()` via `@Backlink('user')`
- `User.ownedFriendships = ToMany<Friend>()` via `@Backlink('ownerUser')`
- `User.friendships = ToMany<Friend>()` via `@Backlink('friendUser')`
- `User.ownedChannels = ToMany<Channel>()` via `@Backlink('ownerUser')`
- `User.recipientChannels = ToMany<Channel>()` via `@Backlink('recipientUser')`
- `User.channelMemberships = ToMany<Member>()` via `@Backlink('user')`
- `User.sentMessages = ToMany<Message>()` via `@Backlink('sender')`
- `User.outgoingCalls = ToMany<CallLog>()` via `@Backlink('callerUser')`
- `User.incomingCalls = ToMany<CallLog>()` via `@Backlink('calleeUser')`

#### **Channel Backlinks:**
- `Channel.privateData = ToOne<ChannelPrivateData>()` via relationship (1:1)
- `Channel.members = ToMany<Member>()` via `@Backlink('channel')`
- `Channel.messages = ToMany<Message>()` via `@Backlink('channel')`

#### **Message Backlinks:**
- `Message.attachments = ToMany<Attachment>()` via `@Backlink('message')`

#### **Other Backlinks:**
- `CallLog.privateData = ToOne<CallLogPrivateData>()` via relationship (1:1)
- `Collection.stickers = ToMany<Sticker>()` via `@Backlink('collection')`
- `Sticker.frameCount = ToOne<StickerFrameCount>()` via relationship (1:1)

### 🔗 **C. BACKLINK RELATIONSHIPS (Quan hệ ngược)**

Tất cả các ToMany relationships ở trên đều sử dụng `@Backlink()` annotation để tạo quan hệ ngược tự động từ ObjectBox.

## 3. SƠ ĐỒ QUAN HỆ DẠNG TEXT

```
SESSION (Root Entity)
├── SessionLocalMetadata (1:1)
├── History (1:N)
├── Manager (1:1)
├── PrivateData (1:N)
├── Collection (1:N)
└── User (1:N)
    ├── Profile (1:N)
    ├── VisitedProfile (1:N)
    ├── UserPresence (1:N)
    ├── UserStatus (1:N)
    ├── UserPrivateData (1:N)
    ├── PrivateData (1:N)
    ├── Friend (as owner) (1:N)
    ├── Friend (as friend) (1:N)
    ├── Channel (as owner) (1:N)
    ├── Channel (as recipient) (1:N)
    ├── Member (1:N)
    ├── Message (as sender) (1:N)
    ├── CallLog (as caller) (1:N)
    └── CallLog (as callee) (1:N)

CHANNEL
├── ChannelPrivateData (1:1)
├── Member (1:N)
└── Message (1:N)
    ├── Attachment (1:N)
    └── TranslatedResult (1:1)

CALLLOG
└── CallLogPrivateData (1:1)

COLLECTION
└── Sticker (1:N)
    └── StickerFrameCount (1:1)
```

## 4. GHI CHÚ CÁC RÀNG BUỘC ĐẶC BIỆT

### 🔒 **A. Unique Constraints:**
- `Session.sessionKey` - Unique identifier cho session
- `User.userId` - Unique identifier cho user trong session
- `Channel.channelId` - Unique identifier cho channel

### 🔗 **B. Composite Indexes:**
- `User.sessionUserId` = `sessionKey_userId`
- `Channel.sessionChannelId` = `sessionKey_channelId`
- `Member.channelUserId` = `channelId_userId`
- `Message.channelIdCreateTime` = `channelId_createTime`
- `Friend.ownerFriendStatus` = `ownerUserId_friendUserId_status`

### ⚠️ **C. Special Relationship Notes:**

#### **1:1 Relationships:**
- `Session` ↔ `SessionLocalMetadata`: Mỗi session có 1 metadata duy nhất
- `Session` ↔ `Manager`: Mỗi session có 1 manager duy nhất
- `Channel` ↔ `ChannelPrivateData`: Mỗi channel có 1 private data duy nhất
- `CallLog` ↔ `CallLogPrivateData`: Mỗi call log có 1 private data duy nhất
- `Sticker` ↔ `StickerFrameCount`: Mỗi sticker có 1 frame count duy nhất
- `Message` ↔ `TranslatedResult`: Mỗi message có 1 kết quả dịch duy nhất

#### **Self-Referencing Relationships:**
- `Friend` entity có 2 references đến `User`: `ownerUser` và `friendUser`
- `Channel` entity có 2 references đến `User`: `ownerUser` và `recipientUser`
- `CallLog` entity có 2 references đến `User`: `callerUser` và `calleeUser`

#### **Backward Compatibility:**
- Tất cả entities đều giữ lại các field cũ (với suffix `Field`) để đảm bảo backward compatibility
- Các getter methods được implement để trả về giá trị từ relationship hoặc field cũ

#### **Cascade Behavior:**
- ObjectBox tự động quản lý cascade operations thông qua ToOne/ToMany relationships
- Khi xóa parent entity, các child entities sẽ được xử lý theo cấu hình ObjectBox

### 📊 **D. Thống Kê Relationships:**

| Loại Relationship | Số lượng | Mô tả |
|-------------------|----------|-------|
| **ToOne** | 24 | Quan hệ Many-to-One và One-to-One (thêm Collection→Session) |
| **ToMany (Backlink)** | 26 | Quan hệ One-to-Many với backlink (giảm 5 quan hệ 1:1) |
| **One-to-One** | 6 | Quan hệ 1:1 đặc biệt |
| **Tổng cộng** | **56** | **Tổng số relationships** |

### 🎯 **E. Entities Tham Gia:**

| Entity | ToOne Count | ToMany Count | One-to-One | Tổng |
|--------|-------------|--------------|------------|------|
| Session | 0 | 3 | 2 | 5 |
| User | 1 | 12 | 0 | 13 |
| Channel | 3 | 2 | 1 | 6 |
| Message | 3 | 1 | 1 | 5 |
| Collection | 1 | 1 | 0 | 2 |
| Others | 16 | 7 | 2 | 25 |
| **Tổng** | **24** | **26** | **6** | **56** |

---

*File này được tạo tự động từ phân tích các entity files trong `lib/src/data/database/entities/`*
*Cập nhật lần cuối: $(date)*
