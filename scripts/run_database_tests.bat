@echo off
setlocal enabledelayedexpansion

REM Database Integration Test Runner Script for Windows
REM Runs comprehensive ObjectBox database tests with detailed reporting

echo ============================================== 
echo   DATABASE INTEGRATION TEST SUITE
echo ==============================================
echo.

REM Check if we're in the right directory
if not exist "pubspec.yaml" (
    echo [ERROR] Please run this script from the project root directory
    exit /b 1
)

REM Check if Flutter is available
flutter --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Flutter is not installed or not in PATH
    exit /b 1
)

echo [INFO] Pre-test Setup
echo [INFO] Checking Flutter installation...
flutter --version

echo [INFO] Getting dependencies...
flutter pub get

echo.
echo [INFO] Running Database Integration Tests

REM Parse command line arguments
set "option=%1"

if "%option%"=="--comprehensive" goto comprehensive
if "%option%"=="-c" goto comprehensive
if "%option%"=="--individual" goto individual
if "%option%"=="-i" goto individual
if "%option%"=="--all" goto all
if "%option%"=="-a" goto all
if "%option%"=="--entity" goto entity
if "%option%"=="-e" goto entity
if "%option%"=="--help" goto help
if "%option%"=="" goto default

:comprehensive
echo [INFO] Running comprehensive database test suite...
flutter test test/database_integration_test_suite.dart --reporter=expanded
goto end

:individual
echo [INFO] Running individual entity tests...

set test_files=^
test/entities/user/user_objectbox_integration_test.dart ^
test/entities/collection/collection_objectbox_integration_test.dart ^
test/entities/sticker/sticker_objectbox_integration_test.dart ^
test/entities/user_private_data/user_private_data_objectbox_integration_test.dart ^
test/entities/visited_profile/visited_profile_objectbox_integration_test.dart ^
test/entities/metadata/metadata_entities_objectbox_integration_test.dart ^
test/entities/call/call_entities_objectbox_integration_test.dart ^
test/entities/channel/channel_entities_objectbox_integration_test.dart ^
test/entities/member/member_objectbox_integration_test.dart ^
test/entities/message/message_objectbox_integration_test.dart ^
test/entities/attachment/attachment_objectbox_integration_test.dart ^
test/entities/translated_result/translated_result_objectbox_integration_test.dart

set passed_tests=0
set failed_tests=0
set total_tests=12

for %%f in (%test_files%) do (
    echo [INFO] Testing %%~nf entity...
    flutter test "%%f" --reporter=compact
    if errorlevel 1 (
        echo [ERROR] %%~nf tests FAILED
        set /a failed_tests+=1
    ) else (
        echo [SUCCESS] %%~nf tests PASSED
        set /a passed_tests+=1
    )
    echo.
)

echo Individual Tests Summary
echo Total entities tested: %total_tests%
echo Passed: %passed_tests%
echo Failed: %failed_tests%
goto end

:all
echo [INFO] Running all database-related tests...
flutter test test/entities/ --reporter=expanded
goto end

:entity
if "%2"=="" (
    echo [ERROR] Please specify entity name. Usage: %0 --entity ^<entity_name^>
    exit /b 1
)

set entity_name=%2
set test_file=test/entities/%entity_name%/%entity_name%_objectbox_integration_test.dart

if exist "%test_file%" (
    echo [INFO] Running %entity_name% entity tests...
    flutter test "%test_file%" --reporter=expanded
) else (
    echo [ERROR] Test file not found: %test_file%
    exit /b 1
)
goto end

:default
echo [INFO] Running comprehensive database test suite (default)...
echo [INFO] Use --help for more options
flutter test test/database_integration_test_suite.dart --reporter=expanded
goto end

:help
echo Usage Options:
echo   %0                    # Run comprehensive test suite (default)
echo   %0 --comprehensive    # Run comprehensive test suite  
echo   %0 --individual       # Run individual entity tests
echo   %0 --all              # Run all database tests
echo   %0 --entity ^<name^>    # Run specific entity tests
echo   %0 --help             # Show this help
exit /b 0

:end
if errorlevel 1 (
    echo.
    echo [ERROR] Some tests FAILED! ❌
    echo [ERROR] Please check the output above for details.
    exit /b 1
) else (
    echo.
    echo [SUCCESS] All database tests PASSED! 🎉
    echo [SUCCESS] Database is ready for production! 🚀
)

echo.
echo Usage Options:
echo   %0                    # Run comprehensive test suite (default)
echo   %0 --comprehensive    # Run comprehensive test suite
echo   %0 --individual       # Run individual entity tests  
echo   %0 --all              # Run all database tests
echo   %0 --entity ^<name^>    # Run specific entity tests
echo   %0 --help             # Show this help
