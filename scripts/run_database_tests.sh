#!/bin/bash

# Database Integration Test Runner Script
# Runs comprehensive ObjectBox database tests with detailed reporting

echo "🗄️ =============================================="
echo "🗄️  DATABASE INTEGRATION TEST SUITE"
echo "🗄️ =============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_header "📋 Pre-test Setup"
print_status "Checking Flutter installation..."
flutter --version

print_status "Getting dependencies..."
flutter pub get

print_header "🧪 Running Database Integration Tests"

# Option 1: Run the comprehensive test suite
if [ "$1" = "--comprehensive" ] || [ "$1" = "-c" ]; then
    print_status "Running comprehensive database test suite..."
    flutter test test/database_integration_test_suite.dart --reporter=expanded
    TEST_RESULT=$?

# Option 2: Run individual entity tests
elif [ "$1" = "--individual" ] || [ "$1" = "-i" ]; then
    print_status "Running individual entity tests..."
    
    # Array of test files
    declare -a test_files=(
        "test/entities/user/user_objectbox_integration_test.dart"
        "test/entities/collection/collection_objectbox_integration_test.dart"
        "test/entities/sticker/sticker_objectbox_integration_test.dart"
        "test/entities/user_private_data/user_private_data_objectbox_integration_test.dart"
        "test/entities/visited_profile/visited_profile_objectbox_integration_test.dart"
        "test/entities/metadata/metadata_entities_objectbox_integration_test.dart"
        "test/entities/call/call_entities_objectbox_integration_test.dart"
        "test/entities/channel/channel_entities_objectbox_integration_test.dart"
        "test/entities/member/member_objectbox_integration_test.dart"
        "test/entities/message/message_objectbox_integration_test.dart"
        "test/entities/attachment/attachment_objectbox_integration_test.dart"
        "test/entities/translated_result/translated_result_objectbox_integration_test.dart"
    )
    
    TOTAL_TESTS=${#test_files[@]}
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    for test_file in "${test_files[@]}"; do
        entity_name=$(basename "$test_file" | sed 's/_objectbox_integration_test.dart//')
        print_status "Testing $entity_name entity..."
        
        if flutter test "$test_file" --reporter=compact; then
            print_success "$entity_name tests PASSED"
            ((PASSED_TESTS++))
        else
            print_error "$entity_name tests FAILED"
            ((FAILED_TESTS++))
        fi
        echo ""
    done
    
    # Summary
    print_header "📊 Individual Tests Summary"
    echo "Total entities tested: $TOTAL_TESTS"
    echo "Passed: $PASSED_TESTS"
    echo "Failed: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        TEST_RESULT=0
    else
        TEST_RESULT=1
    fi

# Option 3: Run all database-related tests
elif [ "$1" = "--all" ] || [ "$1" = "-a" ]; then
    print_status "Running all database-related tests..."
    flutter test test/entities/ --reporter=expanded
    TEST_RESULT=$?

# Option 4: Run specific entity test
elif [ "$1" = "--entity" ] || [ "$1" = "-e" ]; then
    if [ -z "$2" ]; then
        print_error "Please specify entity name. Usage: $0 --entity <entity_name>"
        exit 1
    fi
    
    entity_name="$2"
    test_file="test/entities/$entity_name/${entity_name}_objectbox_integration_test.dart"
    
    if [ -f "$test_file" ]; then
        print_status "Running $entity_name entity tests..."
        flutter test "$test_file" --reporter=expanded
        TEST_RESULT=$?
    else
        print_error "Test file not found: $test_file"
        exit 1
    fi

# Default: Run comprehensive test suite
else
    print_status "Running comprehensive database test suite (default)..."
    print_status "Use --help for more options"
    flutter test test/database_integration_test_suite.dart --reporter=expanded
    TEST_RESULT=$?
fi

# Final result
echo ""
print_header "🎯 Test Results"
if [ $TEST_RESULT -eq 0 ]; then
    print_success "All database tests PASSED! 🎉"
    print_success "Database is ready for production! 🚀"
else
    print_error "Some tests FAILED! ❌"
    print_error "Please check the output above for details."
fi

echo ""
print_header "📚 Usage Options:"
echo "  $0                    # Run comprehensive test suite (default)"
echo "  $0 --comprehensive    # Run comprehensive test suite"
echo "  $0 --individual       # Run individual entity tests"
echo "  $0 --all              # Run all database tests"
echo "  $0 --entity <name>    # Run specific entity tests"
echo "  $0 --help             # Show this help"

exit $TEST_RESULT
